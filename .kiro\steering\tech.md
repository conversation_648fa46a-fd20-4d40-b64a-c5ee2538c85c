# 技术栈规范

## 🏗 核心技术架构

### 前端框架
- **微信小程序原生框架**: 使用官方原生框架开发，确保最佳性能和兼容性
- **WXML**: 页面结构标记语言，类似HTML但针对小程序优化
- **WXSS**: 样式语言，支持CSS大部分特性，增加了rpx单位
- **JavaScript ES6+**: 使用现代JavaScript语法，支持async/await、解构等特性

### 状态管理
- **页面级状态**: 使用Page的data进行页面状态管理
- **全局状态**: 通过App实例的globalData共享全局数据
- **本地存储**: 使用wx.storage进行数据持久化
- **缓存策略**: 实现多层缓存机制，提升用户体验

### 组件化开发
- **自定义组件**: 使用Component构造器创建可复用组件
- **组件通信**: 通过properties、triggerEvent实现父子组件通信
- **插槽机制**: 使用slot实现组件内容分发
- **样式隔离**: 组件样式隔离，避免样式冲突

## 🌐 API集成架构

### 美团联盟CPS API
- **平台**: 美团联盟CPS开放API平台
- **协议**: HTTPS REST API，确保数据传输安全
- **数据格式**: JSON格式，便于JavaScript处理
- **HTTP方法**: POST（所有API调用都必须使用POST方法）
- **认证方式**: appkey/AppSecret签名认证

### API端点配置
```javascript
const API_ENDPOINTS = {
  // 推广链接生成接口
  GET_REFERRAL_LINK: 'https://media.meituan.com/cps_open/common/api/v1/get_referral_link',
  
  // 商品查询接口
  QUERY_COUPON: 'https://media.meituan.com/cps_open/common/api/v1/query_coupon',
  
  // 基础URL
  BASE_URL: 'https://media.meituan.com/cps_open/common/api/v1'
}
```

### 身份验证配置
```javascript
const AUTH_CONFIG = {
  appkey: process.env.MEITUAN_APPKEY,
  secret: process.env.MEITUAN_SECRET,
  mediaId: process.env.MEITUAN_MEDIA_ID,
  allianceId: process.env.MEITUAN_ALLIANCE_ID,
  sid: process.env.MEITUAN_SID
}
```

### 签名算法实现
```javascript
// 签名生成流程
class SignatureGenerator {
  static generate(method, contentMD5, headers, url, secret) {
    // 1. 构建签名字符串
    const stringToSign = `${method}\n${contentMD5}\n${headers}\n${url}`
    
    // 2. 使用HmacSHA256加密
    const hmac = crypto.HmacSHA256(stringToSign, secret)
    
    // 3. Base64编码
    return crypto.enc.Base64.stringify(hmac)
  }
}
```

## 📱 微信小程序API集成

### 核心API使用

#### 位置服务API
```javascript
// 获取用户位置
wx.getLocation({
  type: 'gcj02',
  success: (res) => {
    const { latitude, longitude } = res
    // 处理位置信息
  },
  fail: (error) => {
    // 处理获取失败
  }
})
```

#### 本地存储API
```javascript
// 数据存储
wx.setStorage({
  key: 'userInfo',
  data: userInfo,
  success: () => console.log('存储成功')
})

// 数据读取
wx.getStorage({
  key: 'userInfo',
  success: (res) => {
    const userInfo = res.data
    // 处理用户信息
  }
})
```

#### 网络请求API
```javascript
// 网络请求封装
wx.request({
  url: 'https://api.example.com/data',
  method: 'POST',
  data: requestData,
  header: {
    'Content-Type': 'application/json'
  },
  success: (res) => {
    // 处理响应数据
  },
  fail: (error) => {
    // 处理请求失败
  }
})
```

#### 分享API
```javascript
// 分享配置
onShareAppMessage() {
  return {
    title: '美团优惠大全',
    desc: '精选美团优惠券，吃喝玩乐全都有',
    path: '/pages/index/index',
    imageUrl: '/images/share-logo.png'
  }
}
```

## 🎨 样式技术栈

### WXSS特性
- **rpx单位**: 响应式像素单位，自动适配不同屏幕
- **CSS变量**: 支持CSS自定义属性，便于主题管理
- **Flex布局**: 现代布局方案，适合移动端开发
- **动画支持**: 支持CSS3动画和过渡效果

### 样式架构
```css
/* 全局变量定义 */
:root {
  --primary-color: #FFD100;
  --secondary-color: #FF6600;
  --text-color: #333333;
  --bg-color: #F8F8F8;
}

/* 组件样式隔离 */
.component {
  /* 使用CSS变量 */
  color: var(--text-color);
  background: var(--bg-color);
  
  /* 响应式单位 */
  padding: 32rpx;
  margin: 16rpx;
  
  /* Flex布局 */
  display: flex;
  align-items: center;
  justify-content: space-between;
}
```

## 🛠 开发工具链

### 开发环境
- **微信开发者工具**: 官方IDE，支持调试、预览、上传
- **VS Code**: 代码编辑器，配合小程序插件使用
- **Git**: 版本控制系统
- **Node.js**: 构建工具和脚本运行环境

### 代码质量工具
- **ESLint**: JavaScript代码检查工具
- **Prettier**: 代码格式化工具
- **Stylelint**: CSS代码检查工具
- **Husky**: Git钩子工具，确保代码质量

### 构建和部署
```json
{
  "scripts": {
    "dev": "微信开发者工具调试",
    "build": "构建生产版本",
    "lint": "eslint src/",
    "format": "prettier --write src/",
    "test": "运行测试用例"
  }
}
```

## 性能优化

### 核心优化策略
- **代码优化**: 按需加载、代码分割、Tree Shaking、压缩混淆
- **资源优化**: 图片压缩、懒加载、CDN加速、缓存策略
- **渲染优化**: 虚拟滚动、防抖节流、批量更新

## 🔒 安全技术规范

### 数据安全
- **HTTPS传输**: 所有网络请求使用HTTPS协议
- **数据加密**: 敏感数据本地加密存储
- **签名验证**: API请求使用签名验证身份
- **输入验证**: 所有用户输入进行验证和过滤

### 代码安全
```javascript
// 输入验证示例
function validateInput(input) {
  // 类型检查
  if (typeof input !== 'string') {
    throw new Error('输入必须是字符串')
  }
  
  // 长度检查
  if (input.length > 1000) {
    throw new Error('输入长度超出限制')
  }
  
  // XSS防护
  const sanitized = input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
  
  return sanitized
}
```

### 权限控制
- **用户授权**: 获取用户信息需要明确授权
- **位置权限**: 位置服务需要用户同意
- **存储权限**: 本地存储遵循小程序规范
- **网络权限**: 网络请求域名需要配置白名单

## 监控和测试

### 监控策略
- **性能监控**: 页面加载时间、内存使用、API响应时间
- **错误监控**: 全局错误捕获、异常上报、用户行为追踪

### 测试框架
- **单元测试**: Jest框架
- **集成测试**: 小程序官方测试工具
- **Mock工具**: 模拟API响应和用户交互

## 开发规范

### 核心原则
1. **API调用**: 统一封装、错误处理、缓存策略、签名验证
2. **代码组织**: 模块化、组件化、工具函数、配置管理
3. **性能优化**: 首屏优化、内存管理、网络优化、渲染优化