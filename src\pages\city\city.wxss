/* 城市选择页面样式 */
.city-page {
  background: var(--bg-color);
  min-height: 100vh;
  position: relative;
}

/* 位置权限弹窗 */
.permission-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.modal-content {
  background: var(--white);
  border-radius: var(--radius-large);
  margin: 0 48rpx;
  max-width: 600rpx;
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.modal-body {
  padding: 32rpx;
  text-align: center;
}

.permission-icon {
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 24rpx;
}

.permission-desc {
  font-size: var(--font-size-md);
  color: var(--text-light);
  line-height: var(--line-height-loose);
  margin-bottom: 32rpx;
  display: block;
}

.permission-tips {
  text-align: left;
  background: var(--bg-secondary);
  border-radius: var(--radius-medium);
  padding: 24rpx;
}

.tip-item {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-light);
  line-height: var(--line-height-loose);
  margin-bottom: 8rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.modal-footer {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 24rpx;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast);
}

.btn-cancel {
  background: var(--bg-secondary);
  color: var(--text-light);
}

.btn-cancel:active {
  background: var(--border-color);
}

.btn-confirm {
  background: var(--meituan-orange);
  color: var(--white);
}

.btn-confirm:active {
  background: var(--primary-dark);
}

/* 搜索区域 */
.search-section {
  background: var(--white);
  padding: 32rpx;
  box-shadow: var(--shadow-light);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  gap: 16rpx;
  border: 2rpx solid var(--border-light);
  transition: all var(--duration-fast);
}

.search-bar:focus-within {
  border-color: var(--meituan-orange);
  background: var(--white);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--text-lighter);
  transition: all var(--duration-fast);
}

.search-clear:active {
  background: var(--text-light);
}

.clear-icon {
  width: 20rpx;
  height: 20rpx;
  filter: brightness(0) invert(1);
}

/* 搜索结果 */
.search-results {
  background: var(--white);
  margin: 0 32rpx;
  border-radius: var(--radius-medium);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  animation: slideDown var(--duration-normal) var(--ease-out);
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-light);
  transition: all var(--duration-fast);
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background: var(--bg-secondary);
}

.result-item.selected {
  background: rgba(255, 102, 0, 0.05);
  color: var(--meituan-orange);
}

.result-name {
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.result-item.selected .result-name {
  color: var(--meituan-orange);
  font-weight: var(--font-weight-medium);
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--meituan-orange);
  font-size: 24rpx;
  font-weight: var(--font-weight-bold);
}

.no-result {
  padding: 120rpx 24rpx 80rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 96rpx;
  height: 96rpx;
  opacity: 0.3;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: var(--font-size-md);
  color: var(--text-light);
  margin-bottom: 8rpx;
}

.empty-tip {
  font-size: var(--font-size-sm);
  color: var(--text-lighter);
}

/* 城市内容 */
.city-content {
  padding: 0 32rpx 32rpx;
}

/* 区域标题 */
.section-title {
  font-size: var(--font-size-lg);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: 24rpx;
}

/* 当前定位 */
.location-section {
  margin-bottom: 48rpx;
}

.location-card {
  background: var(--white);
  border-radius: var(--radius-medium);
  padding: 32rpx;
  box-shadow: var(--shadow-light);
}

.location-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.location-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: var(--font-size-sm);
}

.location-status.success {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.location-status.loading {
  background: rgba(255, 173, 20, 0.1);
  color: var(--warning-color);
}

.location-status.error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

.status-icon {
  width: 24rpx;
  height: 24rpx;
}

.status-text {
  font-size: var(--font-size-xs);
}

.location-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: var(--bg-secondary);
  border-radius: var(--radius-small);
  transition: all var(--duration-fast);
}

.location-item:active {
  background: var(--border-light);
}

.location-item.loading {
  opacity: 0.7;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  filter: hue-rotate(15deg) saturate(1.2);
}

.location-name {
  font-size: var(--font-size-md);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

.location-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-tip {
  font-size: var(--font-size-sm);
  color: var(--meituan-orange);
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 热门城市 */
.hot-section {
  margin-bottom: 48rpx;
}

.hot-card {
  background: var(--white);
  border-radius: var(--radius-medium);
  padding: 32rpx;
  box-shadow: var(--shadow-light);
}

.hot-cities {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.hot-item {
  background: var(--bg-secondary);
  padding: 24rpx 16rpx;
  border-radius: var(--radius-small);
  text-align: center;
  transition: all var(--duration-fast);
  border: 2rpx solid transparent;
}

.hot-item:active {
  background: var(--border-light);
}

.hot-item.selected {
  background: rgba(255, 102, 0, 0.1);
  border-color: var(--meituan-orange);
}

.hot-name {
  font-size: var(--font-size-md);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

.hot-item.selected .hot-name {
  color: var(--meituan-orange);
}

/* 全部城市 */
.all-section {
  margin-bottom: 40rpx;
  position: relative;
}

.all-card {
  background: var(--white);
  border-radius: var(--radius-medium);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.city-list {
  max-height: 800rpx;
  padding: 32rpx 0 0;
}

.letter-group {
  border-bottom: 1rpx solid var(--border-light);
}

.letter-group:last-child {
  border-bottom: none;
}

.letter-title {
  background: var(--bg-secondary);
  padding: 16rpx 32rpx;
  font-size: var(--font-size-sm);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  position: sticky;
  top: 0;
  z-index: 10;
}

.city-group {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx 32rpx;
  gap: 16rpx;
}

.city-item {
  min-width: 120rpx;
  text-align: center;
  padding: 16rpx 24rpx;
  border-radius: var(--radius-small);
  transition: all var(--duration-fast);
  border: 2rpx solid transparent;
}

.city-item:active {
  background: var(--bg-secondary);
}

.city-item.selected {
  background: rgba(255, 102, 0, 0.1);
  border-color: var(--meituan-orange);
}

.city-name {
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.city-item.selected .city-name {
  color: var(--meituan-orange);
  font-weight: var(--font-weight-medium);
}

/* 字母索引 */
.letter-index {
  position: fixed;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 200;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 16rpx 8rpx;
  box-shadow: var(--shadow);
  backdrop-filter: blur(10rpx);
}

.index-item {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--duration-fast);
}

.index-item:active,
.index-item.active {
  background: var(--meituan-orange);
  transform: scale(1.2);
}

.index-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
}

.index-item:active .index-text,
.index-item.active .index-text {
  color: var(--white);
}

/* 字母提示 */
.letter-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-toast);
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
  transition: all var(--duration-fast);
}

.letter-tip.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.tip-text {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
}

/* 选择确认动画 */
.select-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-index-toast);
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: 48rpx 32rpx;
  border-radius: var(--radius-large);
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
  transition: all var(--duration-normal);
}

.select-animation.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.animation-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.check-animation {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 48rpx;
  font-weight: var(--font-weight-bold);
  animation: checkBounce var(--duration-slow) var(--ease-out);
}

.select-text {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

/* 动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes checkBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}