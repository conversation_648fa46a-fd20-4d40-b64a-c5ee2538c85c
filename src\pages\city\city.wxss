/* 城市选择页面样式 */
.city-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: var(--white);
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--bg-color);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  gap: 16rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-color);
}

.search-clear {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 搜索结果 */
.search-results {
  background: var(--white);
  margin-top: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.result-item {
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.result-item:last-child {
  border-bottom: none;
}

.result-name {
  font-size: 28rpx;
  color: var(--text-color);
}

.no-result {
  padding: 80rpx 24rpx;
  text-align: center;
  color: var(--text-lighter);
  font-size: 28rpx;
}

/* 城市内容 */
.city-content {
  padding: 24rpx;
}

/* 区域标题 */
.section-title {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 24rpx;
  padding-left: 8rpx;
}

/* 当前定位 */
.location-section {
  margin-bottom: 48rpx;
}

.location-item {
  display: flex;
  align-items: center;
  background: var(--white);
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  gap: 16rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
}

.location-name {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

.location-tip {
  margin-left: auto;
  font-size: 24rpx;
  color: var(--text-lighter);
}

/* 热门城市 */
.hot-section {
  margin-bottom: 48rpx;
}

.hot-cities {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.hot-item {
  background: var(--white);
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
  text-align: center;
}

.hot-name {
  font-size: 28rpx;
  color: var(--text-color);
}

/* 全部城市 */
.all-section {
  margin-bottom: 40rpx;
}

.city-list {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
}

.letter-group {
  border-bottom: 1rpx solid var(--border-color);
}

.letter-group:last-child {
  border-bottom: none;
}

.letter-title {
  background: var(--bg-color);
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: var(--text-light);
  font-weight: 500;
}

.city-group {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  gap: 24rpx;
}

.city-item {
  min-width: 120rpx;
  text-align: center;
  padding: 16rpx 24rpx;
}

.city-name {
  font-size: 28rpx;
  color: var(--text-color);
}