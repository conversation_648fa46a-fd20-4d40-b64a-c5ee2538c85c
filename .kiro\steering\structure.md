# 项目结构规范

## 核心目录结构

```
meituan-miniprogram/
├── .kiro/                    # Kiro AI助手配置
│   ├── specs/               # 项目规格说明
│   └── steering/            # AI助手指导文档
├── docs/                    # 项目文档
├── src/                     # 源代码目录
│   ├── pages/              # 页面目录（index、activity、share、profile等）
│   ├── components/         # 组件目录
│   ├── utils/              # 工具类目录
│   └── assets/             # 静态资源目录
├── re.md                   # API凭证信息
└── README.md               # 项目说明
```

## 📋 文件命名约定

### 页面文件命名
- **目录名**: 使用kebab-case，如`product-detail/`
- **文件名**: 与目录名保持一致，如`product-detail.js`
- **描述性**: 文件名应该清楚描述功能，如`search-result`而不是`sr`

### 组件文件命名
- **组件目录**: 使用kebab-case，如`product-card/`
- **组件文件**: 与目录名一致，如`product-card.wxml`
- **功能导向**: 以功能命名，如`loading-spinner`而不是`spinner`

### 工具文件命名
- **工具类**: 使用camelCase，如`apiService.js`
- **常量文件**: 使用UPPER_CASE，如`API_CONSTANTS.js`
- **配置文件**: 使用camelCase，如`envConfig.js`

## 🎯 目录职责说明

### 核心目录

#### `/src` - 源代码目录
- **职责**: 包含所有小程序源代码
- **规范**: 所有开发代码必须放在此目录下
- **结构**: 按功能模块组织，保持清晰的层次结构

#### `/src/pages` - 页面目录
- **职责**: 存放所有小程序页面
- **规范**: 每个页面一个独立目录，包含4个核心文件
- **命名**: 使用功能描述性名称，避免缩写

#### `/src/components` - 组件目录
- **职责**: 存放可复用的UI组件
- **规范**: 每个组件独立目录，支持嵌套组件
- **分类**: 按功能分类，如基础组件、业务组件等

#### `/src/utils` - 工具类目录
- **职责**: 存放通用工具函数和服务类
- **规范**: 单一职责原则，每个文件专注一个功能
- **导出**: 使用模块化导出，支持按需引入

#### `/src/assets` - 静态资源目录
- **职责**: 存放图片、样式、字体等静态资源
- **规范**: 按类型分类存放，使用相对路径引用
- **优化**: 图片压缩，样式模块化

### 文档目录

#### `/docs` - 项目文档
- **职责**: 存放所有项目相关文档
- **规范**: 使用Markdown格式，保持文档更新
- **分类**: 按文档类型分类，便于查找

#### `/.kiro` - AI助手配置
- **职责**: Kiro AI助手的配置和规格文档
- **规范**: 包含需求、设计、任务等规格文档
- **维护**: 随项目进展持续更新

### 配置文件

#### 小程序配置文件
- **app.json**: 全局配置，页面路由、窗口样式等
- **project.config.json**: 开发工具配置
- **sitemap.json**: 搜索优化配置

#### 项目配置文件
- **package.json**: 依赖管理和脚本配置
- **re.md**: API凭证和端点信息（临时文件）

## 🔒 安全考虑

### 敏感信息管理
```javascript
// ❌ 不要在代码中硬编码敏感信息
const config = {
  appkey: 'hardcoded_key',
  secret: 'hardcoded_secret'
}

// ✅ 使用配置文件或环境变量
const config = {
  appkey: process.env.MEITUAN_APPKEY || wx.getStorageSync('appkey'),
  secret: process.env.MEITUAN_SECRET || wx.getStorageSync('secret')
}
```

### 文件权限控制
- **敏感配置**: 不提交到版本控制系统
- **API密钥**: 使用加密存储或环境变量
- **用户数据**: 遵循数据保护规范

### 代码安全
- **输入验证**: 所有用户输入必须验证
- **XSS防护**: 避免直接渲染用户输入
- **HTTPS**: 所有网络请求使用HTTPS

## 文档规范

### 文档分类
- **API文档**: 美团API和微信API使用指南
- **设计文档**: UI规范、组件库、交互规范
- **开发文档**: 编码规范、项目结构、部署指南

### 维护原则
- 需求变更时及时更新文档
- 文档版本与代码版本保持同步
- 重要变更需要审核

## 最佳实践

### 组织原则
- 功能优先、层次清晰、命名一致、职责单一

### 管理规范
- 及时清理、版本控制、备份策略、权限管理

### 团队协作
- 文档先行、代码审查、知识共享、持续改进