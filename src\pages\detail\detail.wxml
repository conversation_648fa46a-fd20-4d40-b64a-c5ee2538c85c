<!--商品详情页面-->
<view class="detail-page" wx:if="{{!loading}}">
  <!-- 商品图片 -->
  <view class="image-section">
    <swiper class="product-swiper" 
            indicator-dots="{{product.images.length > 1}}"
            bindchange="onImageChange">
      <swiper-item wx:for="{{product.images}}" wx:key="*this">
        <image class="product-image" 
               src="{{item}}" 
               mode="aspectFill"
               bindtap="onImageTap" />
      </swiper-item>
    </swiper>
    <view class="image-indicator" wx:if="{{product.images.length > 1}}">
      <text>{{currentImageIndex + 1}}/{{product.images.length}}</text>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="info-section">
    <view class="price-info">
      <text class="current-price">¥{{product.price}}</text>
      <text class="original-price">¥{{product.originalPrice}}</text>
      <text class="discount">{{product.discount}}</text>
    </view>
    
    <text class="product-title">{{product.title}}</text>
    
    <view class="product-tags">
      <text class="tag tag-red" wx:for="{{product.tags}}" wx:key="*this">{{item}}</text>
    </view>
    
    <text class="product-desc">{{product.description}}</text>
  </view>

  <!-- 店铺信息 -->
  <view class="shop-section" bindtap="onShopTap">
    <image class="shop-logo" src="{{product.shopLogo}}" />
    <view class="shop-info">
      <text class="shop-name">{{product.shopName}}</text>
      <view class="shop-stats">
        <text class="shop-rating">{{product.rating}}分</text>
        <text class="shop-sales">{{product.sales}}</text>
        <text class="shop-distance">{{product.distance}}</text>
      </view>
    </view>
    <image class="shop-arrow" src="/assets/icons/arrow-right.png" />
  </view>

  <!-- 配送信息 -->
  <view class="delivery-section">
    <view class="delivery-item">
      <image class="delivery-icon" src="/assets/icons/time.png" />
      <text class="delivery-text">预计{{product.deliveryTime}}送达</text>
    </view>
    <view class="delivery-item">
      <image class="delivery-icon" src="/assets/icons/delivery.png" />
      <text class="delivery-text">配送费¥{{product.deliveryFee}}</text>
    </view>
  </view>

  <!-- 商品规格 -->
  <view class="spec-section">
    <view class="section-title">商品规格</view>
    <view class="spec-list">
      <view class="spec-item" wx:for="{{product.specifications}}" wx:key="name">
        <text class="spec-name">{{item.name}}</text>
        <text class="spec-value">{{item.value}}</text>
      </view>
    </view>
  </view>

  <!-- 优惠券信息 -->
  <view class="coupon-section" wx:if="{{product.couponInfo}}">
    <view class="section-title">优惠信息</view>
    <view class="coupon-item">
      <view class="coupon-badge">券</view>
      <view class="coupon-info">
        <text class="coupon-type">{{product.couponInfo.type}}</text>
        <text class="coupon-condition">{{product.couponInfo.condition}}</text>
      </view>
      <text class="coupon-time">{{product.couponInfo.validTime}}</text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="action-buttons">
      <view class="action-btn" bindtap="onFavoriteTap">
        <image class="action-icon" src="/assets/icons/favorite.png" />
        <text class="action-text">收藏</text>
      </view>
      <view class="action-btn" bindtap="onShareTap">
        <image class="action-icon" src="/assets/icons/share.png" />
        <text class="action-text">分享</text>
      </view>
    </view>
    <view class="buy-buttons">
      <view class="cart-btn" bindtap="onAddToCart">
        <text>加入购物车</text>
      </view>
      <view class="buy-btn" bindtap="onBuyNow">
        <text>立即购买</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-page" wx:if="{{loading}}">
  <view class="loading-content">
    <text>加载中...</text>
  </view>
</view>

<!-- 分享弹窗 -->
<view class="share-modal" wx:if="{{showShareModal}}" bindtap="onShareModalClose">
  <view class="share-content" catchtap="">
    <view class="share-title">分享到</view>
    <view class="share-options">
      <button class="share-btn" open-type="share">
        <image class="share-icon" src="/assets/icons/wechat.png" />
        <text class="share-name">微信好友</text>
      </button>
      <view class="share-btn" bindtap="onCopyLink">
        <image class="share-icon" src="/assets/icons/link.png" />
        <text class="share-name">复制链接</text>
      </view>
    </view>
    <view class="share-cancel" bindtap="onShareModalClose">取消</view>
  </view>
</view>