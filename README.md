# 美团联盟微信小程序

## 项目简介

这是一个集成美团联盟CPS API的微信小程序，为用户提供美团优惠活动信息展示和推广链接生成功能。小程序界面完全按照UI设计图1:1还原实现，包含首页、活动、分享、我的四个主要功能模块。

## 技术栈

- **前端框架**: 微信小程序原生框架
- **开发工具**: 微信开发者工具
- **API集成**: 美团联盟CPS API
- **部署方式**: 微信开发者工具直接部署，无需服务器端

## 项目结构

```
meituan-miniprogram/
├── .kiro/                          # Kiro AI助手配置
│   ├── specs/                      # 项目规格说明
│   │   └── meituan-miniprogram/    # 当前项目规格
│   │       ├── requirements.md     # 需求文档
│   │       ├── design.md          # 设计文档
│   │       └── tasks.md           # 任务清单
│   └── steering/                   # AI助手指导文档
├── docs/                          # 项目文档
│   └── api/                       # API文档
│       ├── meituan-api.md         # 美团联盟API
│       └── wechat-api.md          # 微信小程序API
├── src/                          # 源代码目录（开发时创建）
│   ├── app.js                    # 小程序入口文件
│   ├── app.json                  # 小程序配置文件
│   ├── app.wxss                  # 全局样式文件
│   ├── pages/                    # 页面目录
│   ├── components/               # 组件目录
│   ├── utils/                    # 工具类目录
│   └── assets/                   # 静态资源目录
└── re.md                         # API凭证信息
```

## 主要功能

### 1. 首页模块
- 城市选择和位置服务
- 搜索功能
- 活动横幅展示
- 品牌推广区域
- 分类标签栏
- 瀑布流商品列表
- 无限滚动加载

### 2. 活动页面
- 品牌推广区（肯德基、麦当劳等）
- 分类筛选功能
- 智能排序
- 列表式商品展示
- 抢购功能

### 3. 分享页面
- 社区内容展示
- 分类浏览
- 推荐内容四宫格
- 用户动态瀑布流
- 互动功能

### 4. 个人中心
- 用户信息管理
- 优惠统计（红包、代金券、返现）
- 功能入口（订单中心、客服等）
- 版本信息

## API集成

### 美团联盟CPS API
- **商品查询接口**: `query_coupon`
- **推广链接生成**: `get_referral_link`
- **签名算法**: HmacSHA256 + Base64
- **认证方式**: appkey/AppSecret

### 微信小程序API
- **位置服务**: `wx.getLocation`
- **本地存储**: `wx.storage`
- **网络请求**: `wx.request`
- **分享功能**: `wx.shareAppMessage`
- **小程序跳转**: `wx.navigateToMiniProgram`

## 开发指南

### 环境准备
1. 安装微信开发者工具
2. 获取小程序AppID
3. 配置美团联盟API凭证

### 开发流程
1. 使用微信开发者工具创建项目
2. 按照任务清单逐步实现功能
3. 测试API接口连接
4. 调试和优化性能
5. 提交审核和发布

### 配置说明

#### API配置 (re.md)
```
媒体ID: 1000304354
appkey: 9498b0824d214ee4b65bfab1be6dbed0
签名密钥: 2a1463cb8f364cafbfd8d2a19c48eb48
联盟ID: 1904722314357399633
SID: yjd2025
```

#### 小程序配置 (app.json)
- 页面路由配置
- tabBar导航配置
- 位置权限配置
- 美团小程序跳转配置

## UI设计规范

### 颜色系统
- 美团主黄色: `#FFD100`
- 美团橙色: `#FF6600`
- 主要文字: `#333333`
- 次要文字: `#666666`
- 价格红色: `#FF3333`

### 布局规范
- 页面边距: `32rpx`
- 卡片圆角: `16rpx`
- 按钮圆角: `8rpx`
- 列间距: `32rpx`
- 行间距: `24rpx`

### 字体规范
- 标题字体: `32rpx`
- 正文字体: `28rpx`
- 说明文字: `24rpx`
- 小字: `20rpx`

## 性能优化

### 数据缓存
- 商品数据缓存30分钟
- 活动数据缓存15分钟
- 城市数据缓存24小时

### 图片优化
- 图片懒加载
- 图片压缩
- WebP格式支持

### 渲染优化
- 虚拟滚动
- 防抖节流
- 批量setData

## 部署说明

### 开发环境
1. 使用微信开发者工具调试
2. 真机预览测试
3. 性能分析优化

### 生产环境
1. 代码压缩混淆
2. 提交微信审核
3. 发布上线

## 文档说明

- **需求文档**: `.kiro/specs/meituan-miniprogram/requirements.md`
- **设计文档**: `.kiro/specs/meituan-miniprogram/design.md`
- **任务清单**: `.kiro/specs/meituan-miniprogram/tasks.md`
- **美团API文档**: `docs/api/meituan-api.md`
- **微信API文档**: `docs/api/wechat-api.md`

## 开发进度

项目采用任务驱动的开发方式，详细的实现任务请查看 `tasks.md` 文件。每个任务都包含具体的代码实现要求和验收标准。

## 注意事项

1. **API安全**: 不要在代码中硬编码API密钥
2. **用户隐私**: 获取位置信息需要用户授权
3. **性能优化**: 合理使用缓存，避免频繁API调用
4. **错误处理**: 实现完善的错误处理和用户提示
5. **兼容性**: 测试不同设备和网络环境

## 联系方式

如有问题请查看相关文档或联系开发团队。

---

*本项目基于微信小程序原生框架开发，集成美团联盟CPS API，界面完全按照UI设计图实现。*