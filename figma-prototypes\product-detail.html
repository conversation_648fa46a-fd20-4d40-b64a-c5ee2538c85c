<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 美团联盟原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        /* 透明导航栏 */
        .product-navbar {
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 44px;
            background: transparent;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
        }

        .navbar-btn {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 商品图片轮播 */
        .product-gallery {
            position: relative;
            width: 100%;
            height: 400px;
            background: #f0f0f0;
        }

        .gallery-image {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 16px;
        }

        .gallery-indicator {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
        }

        .indicator-dot.active {
            background: white;
        }

        .gallery-counter {
            position: absolute;
            bottom: 16px;
            right: 16px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        /* 商品信息卡片 */
        .product-info-card {
            background: white;
            margin: 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-price-section {
            display: flex;
            align-items: baseline;
            gap: 8px;
            margin-bottom: 12px;
        }

        .product-current-price {
            font-size: 24px;
            font-weight: bold;
            color: #FF3333;
        }

        .product-original-price {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
        }

        .product-discount-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .discount-tag {
            background: #FF3333;
            color: white;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 12px;
        }

        .product-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .product-sold {
            font-size: 12px;
            color: #999;
            margin-bottom: 16px;
        }

        /* 商家信息 */
        .merchant-section {
            background: white;
            margin: 0 16px 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
        }

        .merchant-logo {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            margin-right: 12px;
        }

        .merchant-info {
            flex: 1;
        }

        .merchant-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .merchant-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #FF9800;
        }

        .merchant-address {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .merchant-action {
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            color: #333;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
        }

        /* 商品详情 */
        .product-detail-section {
            background: white;
            margin: 0 16px 16px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .detail-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-tab {
            flex: 1;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #666;
            position: relative;
        }

        .detail-tab.active {
            color: #FF6600;
            font-weight: 500;
        }

        .detail-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: #FF6600;
        }

        .detail-content {
            padding: 16px;
        }

        .detail-item {
            margin-bottom: 12px;
        }

        .detail-item-title {
            font-size: 14px;
            color: #999;
            margin-bottom: 4px;
        }

        .detail-item-content {
            font-size: 14px;
            color: #333;
            line-height: 1.5;
        }

        /* 底部操作栏 */
        .bottom-action-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 100;
        }

        .action-icons {
            display: flex;
            gap: 24px;
            margin-right: 16px;
        }

        .action-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .icon {
            font-size: 20px;
            color: #666;
        }

        .icon-text {
            font-size: 10px;
            color: #666;
        }

        .action-buttons {
            flex: 1;
            display: flex;
            gap: 12px;
        }

        .action-button {
            flex: 1;
            height: 40px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
        }

        .buy-button {
            background: #FFD100;
            color: #333;
            border: none;
        }

        .share-button {
            background: #FF3333;
            color: white;
            border: none;
        }

        .scrollable-content {
            height: calc(100vh - 60px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← 返回索引</button>
    
    <div class="phone-frame">
        <!-- 透明导航栏 -->
        <div class="product-navbar">
            <div class="navbar-btn">←</div>
            <div class="navbar-btn">⋮</div>
        </div>

        <div class="scrollable-content">
            <!-- 商品图片轮播 -->
            <div class="product-gallery">
                <div class="gallery-image">商品图片</div>
                <div class="gallery-indicator">
                    <div class="indicator-dot active"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                    <div class="indicator-dot"></div>
                </div>
                <div class="gallery-counter">1/4</div>
            </div>

            <!-- 商品信息卡片 -->
            <div class="product-info-card">
                <div class="product-price-section">
                    <div class="product-current-price">¥198</div>
                    <div class="product-original-price">¥298</div>
                </div>
                <div class="product-discount-tags">
                    <div class="discount-tag">限时特惠</div>
                    <div class="discount-tag">满减</div>
                </div>
                <div class="product-title">海底捞4人火锅套餐，含锅底和特色菜品，赠送小吃拼盘</div>
                <div class="product-sold">已售 3,452 | 好评率 98%</div>
            </div>

            <!-- 商家信息 -->
            <div class="merchant-section">
                <div class="merchant-logo">Logo</div>
                <div class="merchant-info">
                    <div class="merchant-name">海底捞火锅（天河城店）</div>
                    <div class="merchant-rating">
                        <span>★★★★★</span>
                        <span>4.9</span>
                    </div>
                    <div class="merchant-address">天河区天河路208号天河城百货</div>
                </div>
                <button class="merchant-action">进店</button>
            </div>

            <!-- 商品详情 -->
            <div class="product-detail-section">
                <div class="detail-tabs">
                    <div class="detail-tab active">套餐内容</div>
                    <div class="detail-tab">商家介绍</div>
                    <div class="detail-tab">用户评价</div>
                </div>
                <div class="detail-content">
                    <div class="detail-item">
                        <div class="detail-item-title">套餐包含</div>
                        <div class="detail-item-content">
                            1. 锅底（鸳鸯锅：麻辣/清汤）<br>
                            2. 肥牛卷 200g<br>
                            3. 精品羊肉卷 200g<br>
                            4. 虾滑 200g<br>
                            5. 招牌午餐肉 100g<br>
                            6. 蔬菜拼盘（上海青、菠菜、生菜）<br>
                            7. 特色小吃拼盘（赠送）
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-item-title">使用规则</div>
                        <div class="detail-item-content">
                            1. 有效期：2023年7月1日至2023年8月31日<br>
                            2. 使用时间：周一至周五，全天可用<br>
                            3. 需提前1小时预约<br>
                            4. 每桌限用1份优惠<br>
                            5. 不与店内其他优惠同享
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="bottom-action-bar">
            <div class="action-icons">
                <div class="action-icon">
                    <div class="icon">💬</div>
                    <div class="icon-text">客服</div>
                </div>
                <div class="action-icon">
                    <div class="icon">❤️</div>
                    <div class="icon-text">收藏</div>
                </div>
            </div>
            <div class="action-buttons">
                <button class="action-button buy-button">立即购买</button>
                <button class="action-button share-button">分享赚钱</button>
            </div>
        </div>
    </div>
</body>
</html>