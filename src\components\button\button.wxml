<!--自定义按钮组件-->
<button
  class="custom-button {{type}} {{size}} {{round ? 'round' : ''}} {{block ? 'block' : ''}} {{disabled ? 'disabled' : ''}} {{loading ? 'loading' : ''}}"
  style="{{color ? 'background-color: ' + color + '; border-color: ' + color : ''}}"
  disabled="{{disabled || loading}}"
  open-type="{{openType}}"
  bindtap="onTap"
  bindgetuserinfo="onGetUserInfo"
  bindgetphonenumber="onGetPhoneNumber"
  bindopensetting="onOpenSetting"
  bindcontact="onContact"
  binderror="onError">

  <!-- 加载状态 -->
  <view class="button-loading" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
  </view>

  <!-- 按钮内容 -->
  <view class="button-content" wx:else>
    <!-- 左侧图标 -->
    <image wx:if="{{icon && iconPosition === 'left'}}" class="button-icon left" src="{{icon}}" />

    <!-- 按钮文本 -->
    <text class="button-text">{{text}}</text>

    <!-- 右侧图标 -->
    <image wx:if="{{icon && iconPosition === 'right'}}" class="button-icon right" src="{{icon}}" />
  </view>
</button>
