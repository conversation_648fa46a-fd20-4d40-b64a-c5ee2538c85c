/**
 * 加密算法测试文件
 * 用于验证美团API签名算法的正确性
 */

// 由于这是小程序环境，我们需要模拟wx对象
global.wx = {
  arrayBufferToBase64: (buffer) => {
    // 简单的Base64编码实现
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  },
  base64ToArrayBuffer: (base64) => {
    const binary = atob(base64)
    const bytes = new Uint8Array(binary.length)
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i)
    }
    return bytes.buffer
  }
}

// Node.js环境下的btoa/atob实现
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

const crypto = require('../src/utils/crypto.js')

// 测试数据
const testData = {
  body: '{"latitude":39928000,"longitude":116404000,"pageNo":1,"pageSize":10}',
  method: 'POST',
  endpoint: '/cps_open/common/api/v1/query_coupon',
  appkey: '9498b0824d214ee4b65bfab1be6dbed0',
  secret: '2a1463cb8f364cafbfd8d2a19c48eb48',
  timestamp: '1753177121427'
}

console.log('=== 美团API签名算法测试 ===')

// 1. 测试Content-MD5生成
console.log('\n1. Content-MD5测试:')
console.log('输入Body:', testData.body)
const contentMD5 = crypto.generateContentMD5(testData.body)
console.log('Content-MD5结果:', contentMD5)

// 2. 测试Headers字符串构建
console.log('\n2. Headers字符串测试:')
const headers = `S-Ca-App:${testData.appkey}\nS-Ca-Timestamp:${testData.timestamp}\n`
console.log('Headers字符串:', JSON.stringify(headers))

// 3. 测试签名字符串构建
console.log('\n3. 签名字符串测试:')
const stringToSign = `${testData.method}\n${contentMD5}\n${headers}${testData.endpoint}`
console.log('签名字符串:', JSON.stringify(stringToSign))

// 4. 测试HMAC-SHA256签名
console.log('\n4. HMAC-SHA256签名测试:')
const hmacResult = crypto.hmacSHA256(stringToSign, testData.secret)
console.log('HMAC-SHA256结果:', hmacResult)

// 5. 测试最终签名
console.log('\n5. 最终签名测试:')
const signatureBytes = crypto.hexToBytes(hmacResult)
const finalSignature = crypto.base64Encode(signatureBytes)
console.log('最终签名:', finalSignature)

// 6. 测试空Body的Content-MD5
console.log('\n6. 空Body Content-MD5测试:')
const emptyContentMD5 = crypto.generateContentMD5('')
console.log('空Body Content-MD5:', emptyContentMD5)

// 7. 测试MD5算法
console.log('\n7. MD5算法测试:')
const testString = 'hello world'
const md5Result = crypto.md5(testString)
console.log(`MD5("${testString}"):`, md5Result)
console.log('预期结果: 5d41402abc4b2a76b9719d911017c592')

console.log('\n=== 测试完成 ===')