<!--个人中心页面-->
<view class="profile-page">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info" wx:if="{{hasUserInfo}}">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" />
      <view class="user-details">
        <view class="user-nickname">{{userInfo.nickName}}</view>
        <view class="user-desc">美团优惠达人</view>
      </view>
    </view>
    
    <view class="login-prompt" wx:else>
      <image class="default-avatar" src="/assets/icons/user-default.png" mode="aspectFit" />
      <view class="login-info">
        <view class="login-text">点击登录</view>
        <view class="login-desc">登录后享受更多优惠</view>
      </view>
      <button class="login-btn" bindtap="onLoginTap">登录</button>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="statistics-section">
    <view class="stat-item" data-type="redPackets" bindtap="onStatTap">
      <view class="stat-number">{{statistics.redPackets}}</view>
      <view class="stat-label">红包</view>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item" data-type="vouchers" bindtap="onStatTap">
      <view class="stat-number">{{statistics.vouchers}}</view>
      <view class="stat-label">代金券</view>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item" data-type="cashback" bindtap="onStatTap">
      <view class="stat-number">¥{{statistics.cashback}}</view>
      <view class="stat-label">返现</view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view 
      class="menu-item" 
      wx:for="{{menuList}}" 
      wx:key="id"
      data-item="{{item}}"
      bindtap="onMenuTap"
    >
      <view class="menu-left">
        <image class="menu-icon" src="/assets/icons/{{item.icon}}.png" mode="aspectFit" />
        <text class="menu-title">{{item.title}}</text>
      </view>
      <image class="menu-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit" />
    </view>
  </view>

  <!-- 其他功能 -->
  <view class="other-section">
    <view class="other-item" bindtap="onAboutTap">
      <view class="other-left">
        <image class="other-icon" src="/assets/icons/about.png" mode="aspectFit" />
        <text class="other-title">关于我们</text>
      </view>
      <view class="other-right">
        <text class="version-text">v{{version}}</text>
        <image class="other-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit" />
      </view>
    </view>
    
    <view class="other-item" bindtap="onFeedbackTap">
      <view class="other-left">
        <image class="other-icon" src="/assets/icons/feedback.png" mode="aspectFit" />
        <text class="other-title">意见反馈</text>
      </view>
      <image class="other-arrow" src="/assets/icons/arrow-right.png" mode="aspectFit" />
    </view>
  </view>

  <!-- 底部空间 -->
  <view class="bottom-space"></view>
</view>