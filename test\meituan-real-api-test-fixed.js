/**
 * 美团API真实测试 - 使用修复后的签名算法
 */

// 模拟微信小程序环境
global.wx = {
  getSystemInfoSync: () => ({ SDKVersion: '3.8.12' }),
  getAccountInfoSync: () => ({ miniProgram: { version: '1.0.0' } }),
  arrayBufferToBase64: (buffer) => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  },
  request: (options) => {
    console.log('\n=== 发送真实API请求 ===')
    console.log('URL:', options.url)
    console.log('Method:', options.method)
    console.log('Headers:')
    Object.entries(options.header).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`)
    })
    console.log('Data:', JSON.stringify(options.data, null, 2))
    
    // 使用Node.js的https模块发送真实请求
    const https = require('https')
    const url = require('url')
    
    const parsedUrl = url.parse(options.url)
    const postData = JSON.stringify(options.data)
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || 443,
      path: parsedUrl.path,
      method: options.method,
      headers: {
        ...options.header,
        'Content-Length': Buffer.byteLength(postData)
      }
    }
    
    const req = https.request(requestOptions, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        console.log('\n=== API响应 ===')
        console.log('Status Code:', res.statusCode)
        console.log('Headers:')
        Object.entries(res.headers).forEach(([key, value]) => {
          console.log(`  ${key}: ${value}`)
        })
        console.log('Response Body:', data)
        
        try {
          const responseData = JSON.parse(data)
          const response = {
            statusCode: res.statusCode,
            data: responseData,
            header: res.headers
          }
          
          if (res.statusCode === 200) {
            options.success(response)
          } else {
            options.fail({
              statusCode: res.statusCode,
              data: responseData,
              errMsg: `HTTP ${res.statusCode}`
            })
          }
        } catch (error) {
          console.error('解析响应JSON失败:', error)
          options.fail({
            statusCode: res.statusCode,
            errMsg: 'JSON parse error'
          })
        }
      })
    })
    
    req.on('error', (error) => {
      console.error('请求错误:', error)
      options.fail(error)
    })
    
    req.write(postData)
    req.end()
  }
}

// Node.js环境下的btoa/atob实现
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

const apiService = require('../src/utils/api-service-fixed.js')

console.log('=== 美团API真实测试（修复后） ===')
console.log('使用严格按照官方文档实现的签名算法\n')

// 测试查询优惠券接口
const testData = {
  latitude: 39928000,  // 北京天安门纬度 * 1000000
  longitude: 116404000, // 北京天安门经度 * 1000000
  pageNo: 1,
  pageSize: 10
}

console.log('测试数据:', testData)
console.log('开始调用API...\n')

// 记录开始时间
const startTime = Date.now()

apiService.request('/query_coupon', testData)
  .then(result => {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log('\n=== 测试结果 ===')
    console.log('✅ API调用成功!')
    console.log('响应时间:', duration, 'ms')
    console.log('响应代码:', result.code)
    console.log('响应消息:', result.message)
    
    if (result.data && result.data.length > 0) {
      console.log('获取到', result.data.length, '条优惠券数据')
      console.log('第一条数据示例:')
      console.log(JSON.stringify(result.data[0], null, 2))
    } else if (result.data) {
      console.log('响应数据结构:', Object.keys(result.data))
    } else {
      console.log('未获取到数据，可能是地区限制或其他原因')
    }
    
    console.log('\n🎉 签名验证成功！修复完成！')
    console.log('主要修复内容:')
    console.log('- ✅ 严格按照美团官方文档实现签名算法')
    console.log('- ✅ Content-MD5计算使用UTF-8编码')
    console.log('- ✅ Headers按字典排序构建')
    console.log('- ✅ URL格式正确（只包含Path）')
    console.log('- ✅ 签名字符串格式完全符合官方要求')
    console.log('- ✅ 时间戳为整数毫秒格式')
  })
  .catch(error => {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log('\n=== 测试结果 ===')
    console.log('❌ API调用失败')
    console.log('响应时间:', duration, 'ms')
    console.log('错误信息:', error.message)
    
    if (error.message.includes('请求签名验证失败')) {
      console.log('\n🔍 仍然存在签名验证失败，可能的原因:')
      console.log('1. API密钥配置错误')
      console.log('2. 服务器时间与本地时间差异过大')
      console.log('3. 网络传输过程中数据被修改')
      console.log('4. 美团服务器端验证逻辑更新')
    } else if (error.message.includes('时间戳')) {
      console.log('\n🔍 时间戳相关错误:')
      console.log('- 检查系统时间是否准确')
      console.log('- 确认时间戳格式为13位毫秒数')
      console.log('- 验证时间戳在2分钟有效期内')
    } else {
      console.log('\n🔍 其他错误:')
      console.log('- 检查网络连接')
      console.log('- 确认API配置正确')
      console.log('- 查看服务器响应详情')
    }
    
    console.log('\n建议:')
    console.log('- 对比请求头与官方文档要求')
    console.log('- 检查签名字符串构建过程')
    console.log('- 验证Content-MD5计算是否正确')
  })

// 设置超时
setTimeout(() => {
  console.log('\n⏰ 测试超时，请检查网络连接')
  process.exit(1)
}, 30000)
