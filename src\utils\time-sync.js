/**
 * 时间同步工具
 * 用于处理客户端与服务端时间不同步的问题
 */

class TimeSync {
  constructor() {
    this.offset = 0 // 时间偏移量（毫秒）
    this.lastSyncTime = 0 // 上次同步时间
    this.syncInterval = 5 * 60 * 1000 // 5分钟同步一次
    this.maxRetries = 3 // 最大重试次数
    this.syncInProgress = false // 是否正在同步
  }

  /**
   * 获取校正后的当前时间戳（秒）
   */
  getCurrentTimestamp() {
    const now = Date.now() + this.offset
    return Math.floor(now / 1000)
  }

  /**
   * 获取校正后的当前时间戳（毫秒）
   */
  getCurrentTime() {
    return Date.now() + this.offset
  }

  /**
   * 设置时间偏移
   * @param {number} offset 偏移量（毫秒）
   */
  setOffset(offset) {
    this.offset = offset
    this.lastSyncTime = Date.now()
    console.log('时间同步: 设置偏移量', offset, 'ms')
  }

  /**
   * 从HTTP响应头中同步时间
   * @param {Object} response 微信请求响应对象
   */
  syncFromResponse(response) {
    try {
      if (!response || !response.header) return

      // 尝试从不同的响应头获取服务器时间
      const serverTimeStr = response.header['Date'] || 
                           response.header['date'] || 
                           response.header['X-Timestamp'] ||
                           response.header['x-timestamp']

      if (serverTimeStr) {
        // 修复iOS日期格式兼容性问题
        const serverTime = this.parseServerTime(serverTimeStr)
        if (serverTime) {
          const clientTime = Date.now()
          const offset = serverTime - clientTime

          // 只有偏差超过3秒才调整（降低阈值）
          if (Math.abs(offset) > 3000) {
            this.setOffset(offset)
            console.log('时间同步: 从响应头同步时间', {
              serverTimeStr: serverTimeStr,
              serverTime: new Date(serverTime).toISOString(),
              clientTime: new Date(clientTime).toISOString(),
              offset: offset
            })
          }
        }
      }
    } catch (error) {
      console.warn('时间同步: 从响应头同步失败', error)
    }
  }

  /**
   * 解析服务器时间字符串，兼容iOS
   * @param {string} timeStr 时间字符串
   * @returns {number|null} 时间戳（毫秒）
   */
  parseServerTime(timeStr) {
    try {
      // 尝试直接解析
      let date = new Date(timeStr)
      if (!isNaN(date.getTime())) {
        return date.getTime()
      }

      // 如果是GMT格式，转换为iOS兼容格式
      if (timeStr.includes('GMT')) {
        // 例: "Fri, 25 Jul 2025 02:55:58 GMT"
        // 转换为: "2025-07-25T02:55:58.000Z"
        const match = timeStr.match(/(\w+),\s+(\d+)\s+(\w+)\s+(\d+)\s+(\d+):(\d+):(\d+)\s+GMT/)
        if (match) {
          const [, , day, monthStr, year, hour, minute, second] = match
          const monthMap = {
            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
            'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
            'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
          }
          const month = monthMap[monthStr]
          if (month) {
            const isoStr = `${year}-${month}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}.000Z`
            date = new Date(isoStr)
            if (!isNaN(date.getTime())) {
              return date.getTime()
            }
          }
        }
      }

      // 尝试其他格式
      const formats = [
        // ISO格式
        timeStr.replace(/\s+GMT$/, 'Z'),
        // 简单替换
        timeStr.replace(/(\w+),\s+/, '').replace(/\s+GMT$/, ' UTC')
      ]

      for (const format of formats) {
        try {
          date = new Date(format)
          if (!isNaN(date.getTime())) {
            return date.getTime()
          }
        } catch (e) {
          continue
        }
      }

      console.warn('时间同步: 无法解析时间格式', timeStr)
      return null
    } catch (error) {
      console.warn('时间同步: 解析时间失败', error)
      return null
    }
  }

  /**
   * 主动同步时间
   * 通过发送请求到时间服务器获取准确时间
   */
  async syncTime() {
    if (this.syncInProgress) return
    
    this.syncInProgress = true
    
    try {
      // 使用多个时间源进行同步
      const timeSources = [
        'https://worldtimeapi.org/api/timezone/Asia/Shanghai',
        'https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp'
      ]

      for (const url of timeSources) {
        try {
          const startTime = Date.now()
          
          const result = await new Promise((resolve, reject) => {
            wx.request({
              url: url,
              method: 'GET',
              timeout: 5000,
              success: resolve,
              fail: reject
            })
          })

          const endTime = Date.now()
          const networkDelay = (endTime - startTime) / 2 // 估算网络延迟

          let serverTime
          if (url.includes('worldtimeapi')) {
            serverTime = new Date(result.data.datetime).getTime()
          } else if (url.includes('taobao')) {
            serverTime = parseInt(result.data.data.t)
          }

          if (serverTime) {
            const clientTime = Date.now()
            const offset = serverTime - clientTime + networkDelay
            
            // 偏差超过3秒才调整
            if (Math.abs(offset) > 3000) {
              this.setOffset(offset)
              console.log('时间同步: 主动同步成功', {
                source: url,
                serverTime: new Date(serverTime).toISOString(),
                clientTime: new Date(clientTime).toISOString(),
                networkDelay: networkDelay,
                offset: offset
              })
            }
            break
          }
        } catch (error) {
          console.warn('时间同步: 同步源失败', url, error)
          continue
        }
      }
    } catch (error) {
      console.error('时间同步: 主动同步失败', error)
    } finally {
      this.syncInProgress = false
    }
  }

  /**
   * 检查是否需要同步时间
   */
  needSync() {
    return (Date.now() - this.lastSyncTime) > this.syncInterval
  }

  /**
   * 自动同步时间（如果需要）
   */
  async autoSync() {
    if (this.needSync() && !this.syncInProgress) {
      await this.syncTime()
    }
  }

  /**
   * 格式化时间戳为ISO字符串
   * @param {number} timestamp 时间戳（秒或毫秒）
   */
  formatTimestamp(timestamp) {
    // 判断是秒还是毫秒
    const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp
    return new Date(ts + this.offset).toISOString()
  }

  /**
   * 获取时间同步状态
   */
  getStatus() {
    return {
      offset: this.offset,
      lastSyncTime: this.lastSyncTime,
      needSync: this.needSync(),
      syncInProgress: this.syncInProgress,
      correctedTime: new Date(this.getCurrentTime()).toISOString()
    }
  }
}

// 创建全局实例
const timeSync = new TimeSync()

// 小程序启动时尝试同步时间
if (typeof wx !== 'undefined') {
  wx.onAppShow && wx.onAppShow(() => {
    timeSync.autoSync()
  })
}

module.exports = timeSync