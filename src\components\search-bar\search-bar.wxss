.search-bar {
  position: relative;
  background: var(--white);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: #F5F5F5;
  border-radius: 32rpx;
  padding: 0 24rpx;
  height: 64rpx;
  margin: 16rpx 24rpx;
}

.search-icon {
  font-size: 28rpx;
  color: var(--text-lighter);
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-color);
  background: transparent;
  border: none;
  outline: none;
}

.search-placeholder {
  color: var(--text-lighter);
}

.search-clear {
  font-size: 32rpx;
  color: var(--text-lighter);
  margin-left: 16rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #CCCCCC;
}

.search-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 24rpx;
  padding: 0 24rpx;
  height: 48rpx;
  font-size: 28rpx;
  margin-left: 16rpx;
}

.search-button::after {
  border: none;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border-radius: 0 0 16rpx 16rpx;
  box-shadow: var(--shadow);
  z-index: 100;
  max-height: 600rpx;
  overflow-y: auto;
}

.suggestion-section {
  padding: 24rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.suggestion-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: bold;
}

.clear-button {
  background: transparent;
  border: none;
  color: var(--text-lighter);
  font-size: 24rpx;
  padding: 0;
}

.clear-button::after {
  border: none;
}

.keyword-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.keyword-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  background: #F5F5F5;
  border-radius: 32rpx;
  font-size: 26rpx;
  color: var(--text-light);
  transition: all 0.2s ease;
}

.keyword-item:active {
  background: var(--primary-color);
  color: var(--white);
}

.keyword-item--hot {
  background: linear-gradient(135deg, #FFE5CC, #FFD1B3);
  color: var(--primary-color);
}

.keyword-item--hot:active {
  background: var(--primary-color);
  color: var(--white);
}