<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home Page - Meituan Alliance Prototype</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        /* Status Bar */
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #FF6600, #FF8533);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 4px;
            font-size: 12px;
        }

        /* City Selector */
        .city-selector {
            background: linear-gradient(135deg, #FF6600, #FF8533);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .city-name {
            color: white;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .city-arrow {
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid white;
        }

        /* Search Bar */
        .search-section {
            background: linear-gradient(135deg, #FF6600, #FF8533);
            padding: 0 16px 16px;
        }

        .search-bar {
            background: white;
            border-radius: 20px;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-icon {
            color: #999;
            font-size: 16px;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
            color: #999;
        }

        .search-button {
            background: #FFD100;
            color: #333;
            padding: 6px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            border: none;
        }

        /* Activity Banner */
        .activity-banner {
            background: #FF6600;
            margin: 16px;
            padding: 16px;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .banner-content {
            color: white;
            position: relative;
            z-index: 2;
        }

        .banner-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .banner-subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .banner-time {
            background: #FFD100;
            color: #333;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            display: inline-block;
        }

        /* Brand Grid */
        .brand-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            padding: 16px;
            background: white;
            margin: 0 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .brand-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 12px;
        }

        .brand-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .brand-icon.afternoon-tea { background: #FF6600; }
        .brand-icon.flash-sale { background: #FF3333; }
        .brand-icon.coupon-pack { background: #FFD100; color: #333; }
        .brand-icon.daily-checkin { background: #00AA90; }
        .brand-icon.free-today { background: #9C27B0; }
        .brand-icon.more { background: #666; }

        .brand-text {
            font-size: 10px;
            color: #666;
            text-align: center;
        }

        /* Category Tabs */
        .category-tabs {
            display: flex;
            padding: 12px 16px;
            background: white;
            margin: 16px 16px 0;
            border-radius: 8px;
            overflow-x: auto;
            gap: 24px;
        }

        .category-tab {
            white-space: nowrap;
            font-size: 14px;
            color: #666;
            position: relative;
            padding-bottom: 8px;
        }

        .category-tab.active {
            color: #FF6600;
            font-weight: 500;
        }

        .category-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: #FF6600;
            border-radius: 1px;
        }

        /* Product Grid */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            padding: 16px;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        .product-content {
            padding: 12px;
        }

        .product-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            line-height: 1.3;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            display: flex;
            align-items: baseline;
            gap: 6px;
            margin-bottom: 8px;
        }

        .price-current {
            color: #FF3333;
            font-size: 16px;
            font-weight: bold;
        }

        .price-original {
            color: #999;
            font-size: 12px;
            text-decoration: line-through;
        }

        .product-tags {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }

        .product-tag {
            background: #FF3333;
            color: white;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
        }

        .product-shop {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;
        }

        .shop-distance {
            color: #999;
        }

        /* Bottom Tab Bar */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding-bottom: env(safe-area-inset-bottom);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #999;
            font-size: 10px;
        }

        .tab-item.active {
            color: #FF6600;
        }

        .tab-icon {
            font-size: 20px;
        }

        .scrollable-content {
            height: calc(100vh - 104px - 60px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← Back to Index</button>
    
    <div class="phone-frame">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-time">21:23</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <div class="scrollable-content">
            <!-- City Selector -->
            <div class="city-selector">
                <div class="city-name">
                    📍 Guangzhou
                    <div class="city-arrow"></div>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="search-section">
                <div class="search-bar">
                    <div class="search-icon">🔍</div>
                    <input type="text" class="search-input" placeholder="Search for food, restaurants...">
                    <button class="search-button">Search</button>
                </div>
            </div>

            <!-- Activity Banner -->
            <div class="activity-banner">
                <div class="banner-content">
                    <div class="banner-title">New User Exclusive (Cashback on Orders)</div>
                    <div class="banner-subtitle">Get cashback on your first order</div>
                    <div class="banner-time">Activity Time: July 1-8</div>
                </div>
            </div>

            <!-- Brand Grid -->
            <div class="brand-grid">
                <div class="brand-item">
                    <div class="brand-icon afternoon-tea">🍵</div>
                    <div class="brand-text">Afternoon Tea Cashback</div>
                </div>
                <div class="brand-item">
                    <div class="brand-icon flash-sale">⚡</div>
                    <div class="brand-text">Flash Sale Today</div>
                </div>
                <div class="brand-item">
                    <div class="brand-icon coupon-pack">🎫</div>
                    <div class="brand-text">Coupon Pack</div>
                </div>
                <div class="brand-item">
                    <div class="brand-icon daily-checkin">✅</div>
                    <div class="brand-text">Daily Check-in</div>
                </div>
                <div class="brand-item">
                    <div class="brand-icon free-today">🎁</div>
                    <div class="brand-text">Free Today</div>
                </div>
                <div class="brand-item">
                    <div class="brand-icon more">⋯</div>
                    <div class="brand-text">More</div>
                </div>
            </div>

            <!-- Category Tabs -->
            <div class="category-tabs">
                <div class="category-tab active">Recommended</div>
                <div class="category-tab">Food</div>
                <div class="category-tab">Takeout</div>
                <div class="category-tab">Entertainment</div>
                <div class="category-tab">Retail</div>
            </div>

            <!-- Product Grid -->
            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-content">
                        <div class="product-title">Lazy Pig Special! Classic Delicious Set for Two</div>
                        <div class="product-price">
                            <span class="price-current">¥89</span>
                            <span class="price-original">¥128</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">Takeout</span>
                        </div>
                        <div class="product-shop">
                            <span>Lazy Pig Restaurant</span>
                            <span class="shop-distance">1.2km</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-content">
                        <div class="product-title">Premium Hotpot Experience Package</div>
                        <div class="product-price">
                            <span class="price-current">¥158</span>
                            <span class="price-original">¥228</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">Hot Deal</span>
                        </div>
                        <div class="product-shop">
                            <span>Haidilao Hotpot</span>
                            <span class="shop-distance">800m</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-content">
                        <div class="product-title">Fresh Sushi Combo Set</div>
                        <div class="product-price">
                            <span class="price-current">¥68</span>
                            <span class="price-original">¥98</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">New</span>
                        </div>
                        <div class="product-shop">
                            <span>Sushi Master</span>
                            <span class="shop-distance">1.5km</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">Product Image</div>
                    <div class="product-content">
                        <div class="product-title">Coffee & Dessert Afternoon Set</div>
                        <div class="product-price">
                            <span class="price-current">¥45</span>
                            <span class="price-original">¥68</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">Limited</span>
                        </div>
                        <div class="product-shop">
                            <span>Starbucks</span>
                            <span class="shop-distance">500m</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Tab Bar -->
        <div class="tab-bar">
            <div class="tab-item active">
                <div class="tab-icon">🏠</div>
                <div>Home</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">🎯</div>
                <div>Activity</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">📤</div>
                <div>Share</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">👤</div>
                <div>Profile</div>
            </div>
        </div>
    </div>
</body>
</html>
