# 美团联盟API使用说明

## 📋 项目概述

本项目提供了完整的美团联盟API调用解决方案，已修复时间戳过期问题，确保在微信小程序环境中稳定运行。

## 🎯 核心特性

- ✅ **完整的签名算法**：严格按照美团官方文档实现
- ✅ **智能时间同步**：自动学习服务器时间，避免时间戳过期
- ✅ **自动错误重试**：智能处理时间戳过期等临时错误
- ✅ **微信小程序兼容**：完全适配小程序环境
- ✅ **统一API接口**：简化调用方式，易于使用

## 📁 文件结构

```
src/utils/
├── meituan-api-service.js     # 主要API服务（统一版本）
└── crypto-fixed-final.js      # 加密工具（已验证正确）
```

## 🚀 快速开始

### 1. 基本使用

```javascript
// 引入API服务
const meituanApi = require('./src/utils/meituan-api-service.js')

// 查询优惠券
meituanApi.queryCoupon({
  latitude: 39928000,   // 纬度 * 1000000
  longitude: 116404000, // 经度 * 1000000
  pageNo: 1,
  pageSize: 10
}).then(result => {
  console.log('查询成功:', result)
  // 处理返回的优惠券数据
  if (result.data && result.data.length > 0) {
    result.data.forEach(coupon => {
      console.log('优惠券:', coupon.title)
    })
  }
}).catch(error => {
  console.error('查询失败:', error.message)
})
```

### 2. 获取推荐商品

```javascript
// 获取推荐商品（使用相同接口）
meituanApi.getRecommendProducts({
  latitude: 39928000,
  longitude: 116404000,
  pageNo: 1,
  pageSize: 20
}).then(result => {
  console.log('推荐商品:', result.data)
}).catch(error => {
  console.error('获取失败:', error.message)
})
```

### 3. 在页面中使用

```javascript
// pages/index/index.js
const meituanApi = require('../../src/utils/meituan-api-service.js')

Page({
  data: {
    coupons: [],
    loading: false
  },

  onLoad() {
    this.loadCoupons()
  },

  async loadCoupons() {
    this.setData({ loading: true })
    
    try {
      const result = await meituanApi.queryCoupon({
        latitude: 39928000,
        longitude: 116404000,
        pageNo: 1,
        pageSize: 10
      })
      
      this.setData({
        coupons: result.data || [],
        loading: false
      })
      
      wx.showToast({
        title: '加载成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('加载优惠券失败:', error)
      
      this.setData({ loading: false })
      
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  }
})
```

## 🔧 API配置

### 更新API密钥

如需更新API密钥，请修改 `src/utils/meituan-api-service.js` 文件中的配置：

```javascript
this.config = {
  baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
  appkey: '您的AppKey',    // 32位十六进制字符串
  secret: '您的Secret'     // 32位十六进制字符串
}
```

### 获取API密钥

1. 登录美团联盟开放平台：https://union.meituan.com/
2. 进入"开发者中心" → "应用管理"
3. 查看应用详情获取AppKey和Secret

## 📊 状态监控

### 查看服务状态

```javascript
const status = meituanApi.getStatus()
console.log('服务状态:', status)

// 输出示例：
// {
//   配置状态: '正常',
//   时间偏移: 0,
//   偏移秒数: 0,
//   同步次数: 0,
//   上次同步: '2025-01-25T10:30:00.000Z'
// }
```

## 🔍 错误处理

### 常见错误及解决方案

1. **"请求签名验证失败"**
   - 检查API密钥是否正确
   - 确认账户状态正常
   - 验证应用权限配置

2. **"请求时间戳已过期"**
   - 系统会自动处理并重试
   - 如持续出现，检查系统时间是否准确

3. **网络请求失败**
   - 检查网络连接
   - 确认API服务器可访问

### 调试信息

API服务会在控制台输出详细的调试信息：

```
🚀 美团API服务初始化完成
⏰ 时间戳生成: { 原始时间: "2025-01-25T10:30:00.000Z", ... }
📝 Content-MD5生成: { 输入内容: "{...}", ... }
🔐 开始生成签名
🔗 签名字符串构建: { 方法: "POST", ... }
🔑 签名计算完成: { HMAC结果: "...", ... }
📋 请求头构建完成: { Content-Type: "...", ... }
📡 发送请求到: https://media.meituan.com/...
📥 收到响应: { 状态码: 200, ... }
✅ API调用成功
```

## ⚠️ 注意事项

1. **API调用频率**：请遵守美团API的调用频率限制
2. **数据缓存**：建议对API响应数据进行适当缓存
3. **错误处理**：务必处理API调用可能出现的各种错误
4. **用户体验**：在数据加载时显示loading状态

## 🆘 故障排除

### 如果API调用失败

1. **检查控制台日志**：查看详细的错误信息
2. **验证API密钥**：确保AppKey和Secret正确
3. **检查网络连接**：确认能访问美团API服务器
4. **查看账户状态**：登录美团联盟后台检查账户状态

### 联系技术支持

如果问题无法解决，可以联系美团联盟技术支持，提供：
- AppKey（可以安全提供）
- 详细的错误日志
- 账户基本信息

## 📈 性能优化建议

1. **合理使用缓存**：避免频繁调用相同的API
2. **分页加载**：大量数据使用分页方式加载
3. **错误重试**：系统已内置智能重试机制
4. **监控调用**：定期检查API调用成功率

## 🎉 更新日志

### v1.0.0 (当前版本)
- ✅ 修复时间戳过期问题
- ✅ 统一API服务接口
- ✅ 优化错误处理机制
- ✅ 完善调试信息输出
- ✅ 简化项目结构

---

**项目状态**：✅ 已修复所有已知问题，可正常使用

**最后更新**：2025年1月25日
