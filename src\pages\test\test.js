// 测试页面
const apiService = require('../../utils/api-service-fixed.js')
const crypto = require('../../utils/crypto-fixed-final.js')
const cacheManager = require('../../utils/cacheManager.js')
const locationService = require('../../utils/locationService.js')
const testUtils = require('../../utils/test.js')

Page({
  data: {
    testResults: [],
    loading: false
  },

  onLoad() {
    this.addTestResult('测试页面加载完成', 'success')
  },

  // 添加测试结果
  addTestResult(message, type = 'info') {
    const result = {
      id: Date.now(),
      message,
      type, // success, error, info, warning
      time: new Date().toLocaleTimeString()
    }
    
    this.setData({
      testResults: [result, ...this.data.testResults]
    })
  },

  // 测试加密工具
  testCrypto() {
    this.addTestResult('开始测试加密工具...', 'info')
    
    try {
      const testString = 'Hello World'
      const testKey = 'test_key'
      
      // 测试MD5
      const md5Result = crypto.md5(testString)
      this.addTestResult(`MD5测试: ${md5Result}`, 'success')
      
      // 测试HMAC-SHA256
      const hmacResult = crypto.hmacSHA256(testString, testKey)
      this.addTestResult(`HMAC-SHA256测试: ${hmacResult.substring(0, 20)}...`, 'success')
      
      // 测试Base64
      const base64Result = crypto.base64Encode(testString)
      this.addTestResult(`Base64编码测试: ${base64Result}`, 'success')
      
      const base64Decoded = crypto.base64Decode(base64Result)
      this.addTestResult(`Base64解码测试: ${base64Decoded}`, 'success')
      
      this.addTestResult('加密工具测试完成', 'success')
    } catch (error) {
      this.addTestResult(`加密工具测试失败: ${error.message}`, 'error')
    }
  },

  // 测试缓存管理
  testCache() {
    this.addTestResult('开始测试缓存管理...', 'info')
    
    try {
      const testKey = 'test_cache_key'
      const testData = { name: '测试数据', value: 123, time: Date.now() }
      
      // 设置缓存
      const setResult = cacheManager.set(testKey, testData, 2000) // 2秒过期
      this.addTestResult(`设置缓存: ${setResult ? '成功' : '失败'}`, setResult ? 'success' : 'error')
      
      // 获取缓存
      const getData = cacheManager.get(testKey)
      this.addTestResult(`获取缓存: ${getData ? '成功' : '失败'}`, getData ? 'success' : 'error')
      
      if (getData) {
        this.addTestResult(`缓存数据: ${JSON.stringify(getData)}`, 'info')
      }
      
      // 测试过期
      setTimeout(() => {
        const expiredData = cacheManager.get(testKey)
        this.addTestResult(`过期测试: ${expiredData ? '未过期' : '已过期'}`, expiredData ? 'warning' : 'success')
      }, 2100)
      
      this.addTestResult('缓存管理测试完成', 'success')
    } catch (error) {
      this.addTestResult(`缓存管理测试失败: ${error.message}`, 'error')
    }
  },

  // 测试API签名
  testApiSignature() {
    this.addTestResult('开始测试API签名...', 'info')
    
    try {
      const testData = {
        latitude: 39928000,
        longitude: 116404000,
        pageNo: 1,
        pageSize: 10
      }
      
      const signature = apiService.generateSignature('POST', '/query_coupon', testData)
      this.addTestResult('API签名生成成功', 'success')
      this.addTestResult(`签名: ${signature.signature.substring(0, 20)}...`, 'info')
      this.addTestResult(`时间戳: ${signature.timestamp}`, 'info')
      this.addTestResult(`Content-MD5: ${signature.contentMD5.substring(0, 20)}...`, 'info')
      
    } catch (error) {
      this.addTestResult(`API签名测试失败: ${error.message}`, 'error')
    }
  },

  // 测试位置服务
  async testLocation() {
    this.addTestResult('开始测试位置服务...', 'info')
    
    try {
      const location = await locationService.getUserLocation()
      this.addTestResult('位置获取成功', 'success')
      this.addTestResult(`纬度: ${location.latitude}`, 'info')
      this.addTestResult(`经度: ${location.longitude}`, 'info')
      this.addTestResult(`精度: ${location.accuracy}m`, 'info')
      
      const city = locationService.getCurrentCity()
      this.addTestResult(`当前城市: ${city}`, 'info')
      
    } catch (error) {
      this.addTestResult(`位置服务测试失败: ${error.message}`, 'error')
    }
  },

  // 测试API调用
  async testApiCall() {
    this.addTestResult('开始测试API调用...', 'info')
    this.setData({ loading: true })
    
    try {
      const response = await apiService.getRecommendProducts(1, 5)
      this.addTestResult('API调用成功', 'success')
      this.addTestResult(`返回数据条数: ${response.data ? response.data.length : 0}`, 'info')
      
      if (response.data && response.data.length > 0) {
        const firstItem = response.data[0]
        this.addTestResult(`第一条数据: ${JSON.stringify(firstItem).substring(0, 50)}...`, 'info')
      }
      
    } catch (error) {
      this.addTestResult(`API调用失败: ${error.message}`, 'error')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 测试推广链接生成
  async testReferralLink() {
    this.addTestResult('开始测试推广链接生成...', 'info')
    
    try {
      const response = await apiService.getReferralLink('test_activity_id')
      this.addTestResult('推广链接生成成功', 'success')
      
      if (response && response.data) {
        this.addTestResult(`链接: ${response.data.linkUrl || '模拟链接'}`, 'info')
      }
      
    } catch (error) {
      this.addTestResult(`推广链接生成失败: ${error.message}`, 'error')
    }
  },

  // 运行所有测试
  async runAllTests() {
    this.setData({ testResults: [] })
    this.addTestResult('开始运行所有测试...', 'info')
    
    this.testCrypto()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    this.testCache()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    this.testApiSignature()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await this.testLocation()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await this.testApiCall()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await this.testReferralLink()
    await new Promise(resolve => setTimeout(resolve, 500))
    
    this.testUtilFunctions()
    
    this.addTestResult('所有测试完成!', 'success')
  },

  // 清空测试结果
  clearResults() {
    this.setData({ testResults: [] })
    this.addTestResult('测试结果已清空', 'info')
  },

  // 复制测试结果
  copyResults() {
    const results = this.data.testResults.map(item => 
      `[${item.time}] ${item.type.toUpperCase()}: ${item.message}`
    ).join('\n')
    
    wx.setClipboardData({
      data: results,
      success: () => {
        wx.showToast({
          title: '测试结果已复制',
          icon: 'success'
        })
      }
    })
  },
  
  // 测试工具函数
  testUtilFunctions() {
    this.addTestResult('开始测试工具函数...', 'info')
    
    try {
      // 测试随机字符串生成
      const randomStr = testUtils.generateRandomString(8)
      this.addTestResult(`随机字符串生成: ${randomStr}`, 'success')
      
      // 测试测试数据生成
      const userData = testUtils.generateTestData('user')
      this.addTestResult(`用户数据生成: ${JSON.stringify(userData).substring(0, 30)}...`, 'success')
      
      const productData = testUtils.generateTestData('product')
      this.addTestResult(`商品数据生成: ${JSON.stringify(productData).substring(0, 30)}...`, 'success')
      
      // 测试对象结构验证
      const validStructure = {
        id: 'number',
        name: 'string',
        price: 'number'
      }
      
      const validResult = testUtils.validateObjectStructure(productData, validStructure)
      this.addTestResult(`对象结构验证: ${validResult ? '通过' : '失败'}`, validResult ? 'success' : 'error')
      
      this.addTestResult('工具函数测试完成', 'success')
    } catch (error) {
      this.addTestResult(`工具函数测试失败: ${error.message}`, 'error')
    }
  }
})
