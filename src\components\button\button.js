/**
 * 自定义按钮组件
 */
Component({
  properties: {
    // 按钮文本
    text: {
      type: String,
      value: '按钮'
    },
    // 按钮类型：primary, secondary, outline, text
    type: {
      type: String,
      value: 'primary'
    },
    // 按钮大小：small, medium, large
    size: {
      type: String,
      value: 'medium'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    },
    // 是否圆角
    round: {
      type: Boolean,
      value: false
    },
    // 是否块级按钮
    block: {
      type: Boolean,
      value: false
    },
    // 自定义颜色
    color: {
      type: String,
      value: ''
    },
    // 图标
    icon: {
      type: String,
      value: ''
    },
    // 图标位置：left, right
    iconPosition: {
      type: String,
      value: 'left'
    },
    // 开放能力
    openType: {
      type: String,
      value: ''
    }
  },

  data: {
    // 内部状态
  },

  methods: {
    /**
     * 按钮点击事件
     */
    onTap(e) {
      if (this.data.disabled || this.data.loading) {
        return
      }
      
      this.triggerEvent('tap', e.detail)
    },

    /**
     * 获取用户信息回调
     */
    onGetUserInfo(e) {
      this.triggerEvent('getuserinfo', e.detail)
    },

    /**
     * 获取手机号回调
     */
    onGetPhoneNumber(e) {
      this.triggerEvent('getphonenumber', e.detail)
    },

    /**
     * 打开设置页回调
     */
    onOpenSetting(e) {
      this.triggerEvent('opensetting', e.detail)
    },

    /**
     * 打开客服会话回调
     */
    onContact(e) {
      this.triggerEvent('contact', e.detail)
    },

    /**
     * 分享回调
     */
    onShare(e) {
      this.triggerEvent('share', e.detail)
    },

    /**
     * 选择头像回调
     */
    onChooseAvatar(e) {
      this.triggerEvent('chooseavatar', e.detail)
    },

    /**
     * 同意隐私协议回调
     */
    onAgreePrivacyAuthorization(e) {
      this.triggerEvent('agreeprivacyauthorization', e.detail)
    },

    /**
     * 错误回调
     */
    onError(e) {
      this.triggerEvent('error', e.detail)
    }
  }
})
