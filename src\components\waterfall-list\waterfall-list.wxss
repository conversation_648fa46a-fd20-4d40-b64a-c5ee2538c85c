.waterfall-container {
  height: 100%;
  width: 100%;
}

.waterfall-content {
  display: flex;
  padding: 24rpx;
  min-height: 100%;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.waterfall-column:not(:last-child) {
  margin-right: 16rpx;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-text {
  font-size: 28rpx;
  color: var(--text-lighter);
}