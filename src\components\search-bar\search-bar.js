Component({
  properties: {
    placeholder: {
      type: String,
      value: '搜索商品'
    },
    value: {
      type: String,
      value: ''
    },
    showHistory: {
      type: Boolean,
      value: true
    },
    historyList: {
      type: Array,
      value: []
    },
    hotKeywords: {
      type: Array,
      value: ['美食', '外卖', '火锅', '奶茶', '烧烤', '川菜']
    }
  },

  data: {
    showSuggestions: false
  },

  methods: {
    onInput(e) {
      const value = e.detail.value
      this.setData({ value })
      this.triggerEvent('input', { value })
    },

    onFocus() {
      this.setData({ showSuggestions: true })
      this.triggerEvent('focus')
    },

    onBlur() {
      // 延迟隐藏，避免点击建议项时被隐藏
      setTimeout(() => {
        this.setData({ showSuggestions: false })
      }, 200)
      this.triggerEvent('blur')
    },

    onSearch() {
      const value = this.data.value.trim()
      if (!value) return

      this.setData({ showSuggestions: false })
      this.saveSearchHistory(value)
      this.triggerEvent('search', { value })
    },

    onClear() {
      this.setData({ value: '' })
      this.triggerEvent('input', { value: '' })
      this.triggerEvent('clear')
    },

    onKeywordTap(e) {
      const keyword = e.currentTarget.dataset.keyword
      this.setData({ 
        value: keyword,
        showSuggestions: false 
      })
      this.saveSearchHistory(keyword)
      this.triggerEvent('search', { value: keyword })
    },

    onClearHistory() {
      this.triggerEvent('clearhistory')
    },

    // 保存搜索历史
    saveSearchHistory(keyword) {
      try {
        let history = wx.getStorageSync('search_history') || []
        
        // 移除重复项
        history = history.filter(item => item !== keyword)
        
        // 添加到开头
        history.unshift(keyword)
        
        // 限制历史记录数量
        if (history.length > 10) {
          history = history.slice(0, 10)
        }
        
        wx.setStorageSync('search_history', history)
        
        // 更新组件数据
        this.setData({ historyList: history })
      } catch (error) {
        console.error('保存搜索历史失败:', error)
      }
    }
  },

  lifetimes: {
    attached() {
      // 加载搜索历史
      try {
        const history = wx.getStorageSync('search_history') || []
        this.setData({ historyList: history })
      } catch (error) {
        console.error('加载搜索历史失败:', error)
      }
    }
  }
})