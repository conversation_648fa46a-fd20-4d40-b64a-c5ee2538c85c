/* 全局CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #FF6600;
  --primary-light: #FF8533;
  --primary-dark: #E55A00;
  
  /* 美团品牌色 */
  --meituan-yellow: #FFD100;
  --meituan-orange: #FF6600;
  --meituan-orange-light: #FF8C42;
  
  /* 辅助色 */
  --secondary-color: #FFD100;
  --accent-color: #00AA90;
  --success-color: #52C41A;
  --warning-color: #FAAD14;
  --error-color: #FF4D4F;
  --info-color: #1890FF;
  
  /* 文字颜色 */
  --text-color: #333333;
  --text-light: #666666;
  --text-lighter: #999999;
  --text-disabled: #CCCCCC;
  
  /* 背景颜色 */
  --bg-color: #F5F5F5;
  --bg-secondary: #FAFAFA;
  --white: #FFFFFF;
  --black: #000000;
  
  /* 边框颜色 */
  --border-color: #E0E0E0;
  --border-light: #F0F0F0;
  
  /* 阴影 */
  --shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  --shadow-heavy: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  --shadow-card: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 12rpx 32rpx rgba(0, 0, 0, 0.16);
  --shadow-brand: 0 4rpx 16rpx rgba(255, 102, 0, 0.2);
  
  /* 圆角 */
  --radius-small: 8rpx;
  --radius-medium: 16rpx;
  --radius-large: 24rpx;
  --radius-round: 50%;
  
  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  
  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-loose: 1.6;
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  
  /* 层级 */
  --z-index-dropdown: 1000;
  --z-index-modal: 2000;
  --z-index-toast: 3000;
  --z-index-loading: 9999;
  
  /* 动画时间 */
  --duration-fast: 0.2s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  
  /* 动画曲线 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}
