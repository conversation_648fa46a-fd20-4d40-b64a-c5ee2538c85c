<view class="search-bar">
  <view class="search-input-wrapper">
    <view class="search-icon">🔍</view>
    <input 
      class="search-input"
      placeholder="{{placeholder}}"
      value="{{value}}"
      bind:input="onInput"
      bind:confirm="onSearch"
      bind:focus="onFocus"
      bind:blur="onBlur"
      confirm-type="search"
      placeholder-class="search-placeholder"
    />
    <view class="search-clear" wx:if="{{value}}" bind:tap="onClear">×</view>
    <button class="search-button" bind:tap="onSearch">搜索</button>
  </view>
  
  <view class="search-suggestions" wx:if="{{showSuggestions}}">
    <view class="suggestion-section" wx:if="{{historyList.length && showHistory}}">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <button class="clear-button" bind:tap="onClearHistory">清空</button>
      </view>
      <view class="keyword-list">
        <text 
          class="keyword-item" 
          wx:for="{{historyList}}" 
          wx:key="*this"
          bind:tap="onKeywordTap"
          data-keyword="{{item}}">
          {{item}}
        </text>
      </view>
    </view>
    
    <view class="suggestion-section" wx:if="{{hotKeywords.length}}">
      <text class="section-title">热门搜索</text>
      <view class="keyword-list">
        <text 
          class="keyword-item keyword-item--hot" 
          wx:for="{{hotKeywords}}" 
          wx:key="*this"
          bind:tap="onKeywordTap"
          data-keyword="{{item}}">
          {{item}}
        </text>
      </view>
    </view>
  </view>
</view>