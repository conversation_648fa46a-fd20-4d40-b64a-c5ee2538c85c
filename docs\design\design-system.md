# 设计系统

## 🎨 设计系统概述

本设计系统为美团联盟微信小程序提供统一的视觉语言和交互规范，确保整个产品的一致性和可维护性。设计系统基于美团品牌规范，结合微信小程序平台特性，提供完整的设计指导和实现标准。

## 🌈 色彩系统

### 主色调体系

#### 美团品牌色
```css
/* 主品牌色 - 美团黄 */
--meituan-yellow: #FFD100;
--meituan-yellow-light: #FFF566;
--meituan-yellow-dark: #E6BC00;
--meituan-yellow-alpha-10: rgba(255, 209, 0, 0.1);
--meituan-yellow-alpha-20: rgba(255, 209, 0, 0.2);

/* 辅助品牌色 - 美团橙 */
--meituan-orange: #FF6600;
--meituan-orange-light: #FF8533;
--meituan-orange-dark: #E65C00;
--meituan-orange-alpha-10: rgba(255, 102, 0, 0.1);
--meituan-orange-alpha-20: rgba(255, 102, 0, 0.2);

/* 成功色 - 美团绿 */
--meituan-green: #00AA90;
--meituan-green-light: #33BBA3;
--meituan-green-dark: #009981;
```

#### 功能色彩
```css
/* 状态色彩 */
--color-success: #00AA90;
--color-warning: #FF9500;
--color-error: #FF4444;
--color-info: #2196F3;

/* 中性色彩 */
--color-white: #FFFFFF;
--color-black: #000000;
--color-gray-50: #FAFAFA;
--color-gray-100: #F5F5F5;
--color-gray-200: #EEEEEE;
--color-gray-300: #E0E0E0;
--color-gray-400: #BDBDBD;
--color-gray-500: #9E9E9E;
--color-gray-600: #757575;
--color-gray-700: #616161;
--color-gray-800: #424242;
--color-gray-900: #212121;
```

#### 语义化色彩
```css
/* 文字色彩 */
--text-primary: var(--color-gray-900);      /* 主要文字 */
--text-secondary: var(--color-gray-700);    /* 次要文字 */
--text-tertiary: var(--color-gray-500);     /* 辅助文字 */
--text-disabled: var(--color-gray-400);     /* 禁用文字 */
--text-inverse: var(--color-white);         /* 反色文字 */
--text-link: var(--meituan-orange);         /* 链接文字 */
--text-price: #FF3333;                      /* 价格文字 */

/* 背景色彩 */
--bg-primary: var(--color-white);           /* 主背景 */
--bg-secondary: var(--color-gray-50);       /* 次背景 */
--bg-tertiary: var(--color-gray-100);       /* 三级背景 */
--bg-card: var(--color-white);              /* 卡片背景 */
--bg-overlay: rgba(0, 0, 0, 0.5);           /* 遮罩背景 */

/* 边框色彩 */
--border-light: var(--color-gray-200);      /* 浅色边框 */
--border-medium: var(--color-gray-300);     /* 中等边框 */
--border-dark: var(--color-gray-400);       /* 深色边框 */
--divider: var(--color-gray-200);           /* 分割线 */
```

### 渐变色彩
```css
/* 品牌渐变 */
--gradient-primary: linear-gradient(135deg, var(--meituan-yellow) 0%, var(--meituan-orange) 100%);
--gradient-orange: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);
--gradient-yellow: linear-gradient(135deg, var(--meituan-yellow) 0%, #FFA500 100%);

/* 背景渐变 */
--gradient-bg-warm: linear-gradient(180deg, #FFF8E1 0%, var(--color-white) 100%);
--gradient-bg-cool: linear-gradient(180deg, var(--color-gray-50) 0%, var(--color-white) 100%);

/* 状态栏渐变 */
--gradient-status-bar: linear-gradient(135deg, var(--meituan-orange) 0%, var(--meituan-orange-light) 100%);
```

## 📝 字体系统

### 字体族定义
```css
/* 主字体族 */
--font-family-primary: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;

/* 数字字体族 */
--font-family-number: 'Helvetica Neue', Helvetica, Arial, sans-serif;

/* 等宽字体族 */
--font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
```

### 字体大小系统
```css
/* 标题字体大小 (px) */
--font-size-h1: 24px;    /* 页面主标题 */
--font-size-h2: 20px;    /* 区块标题 */
--font-size-h3: 18px;    /* 卡片标题 */
--font-size-h4: 16px;    /* 小标题 */

/* 正文字体大小 (px) */
--font-size-xl: 18px;    /* 特大文字 */
--font-size-lg: 16px;    /* 大文字 */
--font-size-md: 14px;    /* 标准文字 */
--font-size-sm: 12px;    /* 小文字 */
--font-size-xs: 10px;    /* 极小文字 */

/* 特殊字体大小 */
--font-size-price: 18px;     /* 价格显示 */
--font-size-badge: 10px;     /* 徽章文字 */
--font-size-caption: 12px;   /* 说明文字 */
```

### 字体权重系统
```css
--font-weight-light: 300;      /* 细体 */
--font-weight-normal: 400;     /* 正常 */
--font-weight-medium: 500;     /* 中等 */
--font-weight-semibold: 600;   /* 半粗 */
--font-weight-bold: 700;       /* 粗体 */
--font-weight-black: 900;      /* 特粗 */
```

### 行高系统
```css
--line-height-tight: 1.2;      /* 紧密行高 - 标题 */
--line-height-snug: 1.3;       /* 紧凑行高 - 副标题 */
--line-height-normal: 1.4;     /* 正常行高 - 正文 */
--line-height-relaxed: 1.6;    /* 宽松行高 - 长文本 */
--line-height-loose: 1.8;      /* 松散行高 - 特殊场景 */
```

## 📏 间距系统

### 基础间距单位
```css
/* 基础间距 - 基于4px网格系统 */
--spacing-0: 0;          /* 无间距 */
--spacing-1: 4px;        /* 极小间距 */
--spacing-2: 8px;        /* 小间距 */
--spacing-3: 12px;       /* 中小间距 */
--spacing-4: 16px;       /* 中等间距 */
--spacing-5: 20px;       /* 中大间距 */
--spacing-6: 24px;       /* 大间距 */
--spacing-8: 32px;       /* 极大间距 */
--spacing-10: 40px;      /* 超大间距 */
--spacing-12: 48px;      /* 特大间距 */
--spacing-16: 64px;      /* 巨大间距 */
```

### 语义化间距
```css
/* 页面级间距 */
--spacing-page-horizontal: var(--spacing-4);    /* 页面左右边距 */
--spacing-page-vertical: var(--spacing-6);      /* 页面上下边距 */

/* 组件间距 */
--spacing-component-gap: var(--spacing-4);      /* 组件间距 */
--spacing-section-gap: var(--spacing-6);        /* 区块间距 */
--spacing-group-gap: var(--spacing-3);          /* 组内间距 */

/* 内容间距 */
--spacing-content-horizontal: var(--spacing-4); /* 内容左右间距 */
--spacing-content-vertical: var(--spacing-3);   /* 内容上下间距 */

/* 交互元素间距 */
--spacing-button-horizontal: var(--spacing-6);  /* 按钮左右内边距 */
--spacing-button-vertical: var(--spacing-3);    /* 按钮上下内边距 */
--spacing-input-horizontal: var(--spacing-4);   /* 输入框左右内边距 */
--spacing-input-vertical: var(--spacing-3);     /* 输入框上下内边距 */
```

## 🔘 圆角系统

### 圆角尺寸定义
```css
--radius-none: 0;           /* 无圆角 */
--radius-xs: 2px;           /* 极小圆角 - 标签 */
--radius-sm: 4px;           /* 小圆角 - 按钮 */
--radius-md: 8px;           /* 中等圆角 - 卡片 */
--radius-lg: 12px;          /* 大圆角 - 模态框 */
--radius-xl: 16px;          /* 极大圆角 - 特殊组件 */
--radius-2xl: 20px;         /* 超大圆角 - 搜索框 */
--radius-full: 50%;         /* 圆形 - 头像 */
```

### 语义化圆角
```css
--radius-button: var(--radius-sm);      /* 按钮圆角 */
--radius-card: var(--radius-md);        /* 卡片圆角 */
--radius-input: var(--radius-sm);       /* 输入框圆角 */
--radius-modal: var(--radius-lg);       /* 模态框圆角 */
--radius-avatar: var(--radius-full);    /* 头像圆角 */
--radius-badge: var(--radius-xs);       /* 徽章圆角 */
--radius-tag: var(--radius-2xl);        /* 标签圆角 */
```

## 🌟 阴影系统

### 阴影层级定义
```css
--shadow-none: none;
--shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
--shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
--shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
```

### 语义化阴影
```css
--shadow-card: var(--shadow-sm);        /* 卡片阴影 */
--shadow-button: var(--shadow-xs);      /* 按钮阴影 */
--shadow-modal: var(--shadow-xl);       /* 模态框阴影 */
--shadow-dropdown: var(--shadow-lg);    /* 下拉菜单阴影 */
--shadow-tooltip: var(--shadow-md);     /* 提示框阴影 */
```

### 特殊阴影效果
```css
/* 内阴影 */
--shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.06);

/* 彩色阴影 */
--shadow-orange: 0 4px 14px rgba(255, 102, 0, 0.25);
--shadow-yellow: 0 4px 14px rgba(255, 209, 0, 0.25);
--shadow-green: 0 4px 14px rgba(0, 170, 144, 0.25);

/* 发光效果 */
--glow-orange: 0 0 20px rgba(255, 102, 0, 0.3);
--glow-yellow: 0 0 20px rgba(255, 209, 0, 0.3);
```

## 🧩 组件设计规范

### 按钮组件规范

#### 按钮尺寸
```css
/* 按钮高度 */
--button-height-xs: 24px;       /* 极小按钮 */
--button-height-sm: 32px;       /* 小按钮 */
--button-height-md: 40px;       /* 标准按钮 */
--button-height-lg: 48px;       /* 大按钮 */
--button-height-xl: 56px;       /* 极大按钮 */

/* 按钮最小宽度 */
--button-min-width: 64px;       /* 最小点击区域 */
```

#### 按钮样式变体
```css
/* 主要按钮 */
.btn-primary {
  background: var(--meituan-orange);
  color: var(--text-inverse);
  border: none;
  box-shadow: var(--shadow-button);
}

.btn-primary:hover {
  background: var(--meituan-orange-light);
}

.btn-primary:active {
  background: var(--meituan-orange-dark);
  transform: translateY(1px);
}

.btn-primary:disabled {
  background: var(--color-gray-300);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 次要按钮 */
.btn-secondary {
  background: transparent;
  color: var(--meituan-orange);
  border: 1px solid var(--meituan-orange);
}

.btn-secondary:hover {
  background: var(--meituan-orange-alpha-10);
}

.btn-secondary:active {
  background: var(--meituan-orange-alpha-20);
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: var(--meituan-orange);
  border: none;
  box-shadow: none;
}

.btn-text:hover {
  background: var(--meituan-orange-alpha-10);
}

/* 危险按钮 */
.btn-danger {
  background: var(--color-error);
  color: var(--text-inverse);
  border: none;
}
```

### 输入框组件规范

#### 输入框状态
```css
/* 基础输入框 */
.input {
  height: var(--button-height-md);
  padding: 0 var(--spacing-input-horizontal);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-input);
  font-size: var(--font-size-md);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: all 0.2s ease;
}

.input::placeholder {
  color: var(--text-tertiary);
}

.input:focus {
  border-color: var(--meituan-orange);
  box-shadow: 0 0 0 2px var(--meituan-orange-alpha-20);
  outline: none;
}

.input:disabled {
  background: var(--bg-tertiary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.input.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
}

/* 搜索框 */
.input-search {
  border-radius: var(--radius-2xl);
  padding-left: var(--spacing-10);
  background-image: url('data:image/svg+xml;utf8,<svg>...</svg>');
  background-repeat: no-repeat;
  background-position: var(--spacing-3) center;
  background-size: 16px;
}
```

### 卡片组件规范

#### 卡片基础样式
```css
.card {
  background: var(--bg-card);
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-4);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card.interactive:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 商品卡片 */
.product-card {
  overflow: hidden;
  cursor: pointer;
}

.product-card__image {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: var(--radius-sm);
}

.product-card__content {
  padding: var(--spacing-3);
}

.product-card__title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-2);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card__price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.price-current {
  font-size: var(--font-size-price);
  font-weight: var(--font-weight-bold);
  color: var(--text-price);
  font-family: var(--font-family-number);
}

.price-original {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  text-decoration: line-through;
  font-family: var(--font-family-number);
}
```

### 标签组件规范

#### 标签样式变体
```css
/* 基础标签 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-badge);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

/* 优惠标签 */
.tag-discount {
  background: var(--color-error);
  color: var(--text-inverse);
}

/* 分类标签 */
.tag-category {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.tag-category.active {
  background: var(--meituan-orange);
  color: var(--text-inverse);
  border-color: var(--meituan-orange);
}

/* 状态标签 */
.tag-success {
  background: var(--color-success);
  color: var(--text-inverse);
}

.tag-warning {
  background: var(--color-warning);
  color: var(--text-inverse);
}

.tag-info {
  background: var(--color-info);
  color: var(--text-inverse);
}
```

## 📱 布局系统

### 网格系统
```css
/* 容器 */
.container {
  width: 100%;
  padding-left: var(--spacing-page-horizontal);
  padding-right: var(--spacing-page-horizontal);
  margin-left: auto;
  margin-right: auto;
}

/* 网格容器 */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flex布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}
```

### 响应式断点
```css
/* 断点定义 */
--breakpoint-xs: 320px;   /* 小屏手机 */
--breakpoint-sm: 375px;   /* 标准手机 */
--breakpoint-md: 414px;   /* 大屏手机 */
--breakpoint-lg: 768px;   /* 平板 */

/* 媒体查询 */
@media (max-width: 375px) {
  .container {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
  }
  
  .grid-cols-2-sm {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 414px) {
  .grid-cols-3-md {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 🎯 交互状态系统

### 基础交互状态
```css
/* 可交互元素 */
.interactive {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.interactive:hover {
  opacity: 0.8;
}

.interactive:active {
  transform: scale(0.98);
}

.interactive:disabled,
.interactive.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 焦点状态 */
.focusable:focus {
  outline: 2px solid var(--meituan-orange);
  outline-offset: 2px;
}

.focusable:focus:not(:focus-visible) {
  outline: none;
}
```

### 加载状态
```css
/* 加载动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes skeleton {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading-spin {
  animation: spin 1s linear infinite;
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton 1.5s infinite;
}
```

## 🔧 工具类系统

### 间距工具类
```css
/* 外边距 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

/* 方向性外边距 */
.mt-1 { margin-top: var(--spacing-1); }
.mr-1 { margin-right: var(--spacing-1); }
.mb-1 { margin-bottom: var(--spacing-1); }
.ml-1 { margin-left: var(--spacing-1); }

/* 内边距 */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

/* 方向性内边距 */
.pt-1 { padding-top: var(--spacing-1); }
.pr-1 { padding-right: var(--spacing-1); }
.pb-1 { padding-bottom: var(--spacing-1); }
.pl-1 { padding-left: var(--spacing-1); }
```

### 文本工具类
```css
/* 字体大小 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

/* 字体权重 */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文本颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }
.text-link { color: var(--text-link); }
.text-price { color: var(--text-price); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }

/* 文本截断 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
```

### 显示工具类
```css
/* 显示控制 */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

/* 可见性 */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 定位 */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* 层级 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
```

## 📋 设计令牌 (Design Tokens)

### 令牌结构
```json
{
  "color": {
    "brand": {
      "primary": {
        "value": "#FF6600",
        "type": "color",
        "description": "美团主品牌色"
      },
      "secondary": {
        "value": "#FFD100",
        "type": "color",
        "description": "美团辅助品牌色"
      }
    },
    "semantic": {
      "text": {
        "primary": {
          "value": "#212121",
          "type": "color"
        },
        "secondary": {
          "value": "#616161",
          "type": "color"
        }
      }
    }
  },
  "spacing": {
    "xs": {
      "value": "4px",
      "type": "dimension"
    },
    "sm": {
      "value": "8px",
      "type": "dimension"
    },
    "md": {
      "value": "16px",
      "type": "dimension"
    }
  },
  "typography": {
    "heading": {
      "h1": {
        "fontSize": {
          "value": "24px",
          "type": "dimension"
        },
        "fontWeight": {
          "value": "700",
          "type": "number"
        },
        "lineHeight": {
          "value": "1.2",
          "type": "number"
        }
      }
    }
  }
}
```

## 🎨 主题系统

### 亮色主题 (默认)
```css
[data-theme="light"] {
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --text-primary: #212121;
  --text-secondary: #616161;
  --border-color: #E0E0E0;
}
```

### 暗色主题 (可选)
```css
[data-theme="dark"] {
  --bg-primary: #121212;
  --bg-secondary: #1E1E1E;
  --text-primary: #FFFFFF;
  --text-secondary: #CCCCCC;
  --border-color: #333333;
}
```

### 主题切换
```javascript
// 主题切换工具
const ThemeManager = {
  // 获取当前主题
  getCurrentTheme() {
    return wx.getStorageSync('theme') || 'light'
  },
  
  // 设置主题
  setTheme(theme) {
    wx.setStorageSync('theme', theme)
    // 在小程序中，需要通过setData更新页面样式
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.setData) {
        page.setData({
          theme: theme
        })
      }
    })
  },
  
  // 切换主题
  toggleTheme() {
    const currentTheme = this.getCurrentTheme()
    const newTheme = currentTheme === 'light' ? 'dark' : 'light'
    this.setTheme(newTheme)
  }
}
```

## 📐 图标系统

### 图标规范
```css
/* 图标基础样式 */
.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  fill: currentColor;
  vertical-align: middle;
}

/* 图标尺寸 */
.icon-xs { font-size: 12px; }
.icon-sm { font-size: 14px; }
.icon-md { font-size: 16px; }
.icon-lg { font-size: 20px; }
.icon-xl { font-size: 24px; }
.icon-2xl { font-size: 32px; }

/* 图标颜色 */
.icon-primary { color: var(--meituan-orange); }
.icon-secondary { color: var(--text-secondary); }
.icon-success { color: var(--color-success); }
.icon-warning { color: var(--color-warning); }
.icon-error { color: var(--color-error); }
```

### 常用图标列表
- 🏠 home - 首页
- 🎯 activity - 活动
- 📤 share - 分享
- 👤 profile - 个人
- 🔍 search - 搜索
- 📍 location - 位置
- ❤️ favorite - 收藏
- 🛒 cart - 购物车
- ⭐ star - 评分
- 📞 phone - 电话
- 💬 message - 消息
- ⚙️ settings - 设置

## 📱 移动端适配

### 安全区域适配
```css
/* 顶部安全区域 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

/* 底部安全区域 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 左右安全区域 */
.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* 全方向安全区域 */
.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
}
```

### 触摸目标尺寸
```css
/* 最小触摸目标 */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 扩大触摸区域 */
.touch-area-expand {
  position: relative;
}

.touch-area-expand::before {
  content: '';
  position: absolute;
  top: -10px;
  right: -10px;
  bottom: -10px;
  left: -10px;
}
```

## 🔍 可访问性规范

### 颜色对比度
- 正文文字对比度 ≥ 4.5:1
- 大文字对比度 ≥ 3:1
- 非文字元素对比度 ≥ 3:1

### 焦点指示器
```css
.focus-visible {
  outline: 2px solid var(--meituan-orange);
  outline-offset: 2px;
}

.focus-visible:not(:focus-visible) {
  outline: none;
}
```

### 语义化标记
```xml
<!-- 使用语义化的role属性 -->
<view role="button" aria-label="搜索商品">
  <text>搜索</text>
</view>

<view role="navigation" aria-label="主导航">
  <!-- 导航内容 -->
</view>

<view role="main" aria-label="主要内容">
  <!-- 页面主要内容 -->
</view>
```

## 📊 设计系统使用指南

### 开发者使用指南

#### 1. 引入设计系统
```css
/* 在app.wxss中引入设计系统 */
@import "assets/styles/design-system.wxss";
```

#### 2. 使用设计令牌
```css
/* 使用CSS变量 */
.my-component {
  color: var(--text-primary);
  background: var(--bg-card);
  padding: var(--spacing-4);
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-card);
}
```

#### 3. 使用工具类
```xml
<view class="flex items-center justify-between p-4 bg-white rounded-md shadow-sm">
  <text class="text-lg font-medium text-primary">标题</text>
  <view class="text-sm text-secondary">副标题</view>
</view>
```

### 设计师使用指南

#### 1. 设计文件组织
```
Design Files/
├── Design System/
│   ├── Colors.sketch
│   ├── Typography.sketch
│   ├── Components.sketch
│   └── Icons.sketch
├── Pages/
│   ├── Home.sketch
│   ├── Activity.sketch
│   └── Profile.sketch
└── Assets/
    ├── Images/
    └── Icons/
```

#### 2. 组件库使用
- 优先使用设计系统中的现有组件
- 新组件需要符合设计系统规范
- 保持组件的一致性和可复用性

#### 3. 设计交付规范
- 提供完整的设计规范文档
- 标注详细的尺寸和间距
- 提供不同状态的设计稿
- 考虑响应式和适配需求

---

*设计系统会根据产品发展和用户反馈持续迭代和完善*