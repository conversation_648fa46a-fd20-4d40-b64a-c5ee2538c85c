/* 商品详情页面样式 */
.detail-page {
  background: var(--bg-color);
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 商品图片区域 */
.image-section {
  position: relative;
  background: var(--white);
}

.product-swiper {
  height: 600rpx;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-indicator {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  background: rgba(0, 0, 0, 0.5);
  color: var(--white);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

/* 商品信息区域 */
.info-section {
  background: var(--white);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.current-price {
  font-size: 48rpx;
  color: #FF3333;
  font-weight: bold;
}

.original-price {
  font-size: 28rpx;
  color: var(--text-lighter);
  text-decoration: line-through;
}

.discount {
  background: #FF3333;
  color: var(--white);
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.product-title {
  font-size: 32rpx;
  color: var(--text-color);
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 24rpx;
  display: block;
}

.product-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.tag {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: var(--white);
}

.tag-red {
  background: #FF3333;
}

.product-desc {
  font-size: 28rpx;
  color: var(--text-light);
  line-height: 1.5;
  display: block;
}

/* 店铺信息区域 */
.shop-section {
  display: flex;
  align-items: center;
  background: var(--white);
  padding: 32rpx;
  margin-bottom: 24rpx;
  gap: 24rpx;
}

.shop-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
}

.shop-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.shop-name {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

.shop-stats {
  display: flex;
  gap: 24rpx;
}

.shop-rating,
.shop-sales,
.shop-distance {
  font-size: 24rpx;
  color: var(--text-light);
}

.shop-arrow {
  width: 24rpx;
  height: 24rpx;
}

/* 配送信息区域 */
.delivery-section {
  background: var(--white);
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  gap: 48rpx;
}

.delivery-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.delivery-icon {
  width: 32rpx;
  height: 32rpx;
}

.delivery-text {
  font-size: 28rpx;
  color: var(--text-color);
}

/* 规格区域 */
.spec-section {
  background: var(--white);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 24rpx;
}

.spec-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.spec-name {
  font-size: 28rpx;
  color: var(--text-light);
}

.spec-value {
  font-size: 28rpx;
  color: var(--text-color);
}

/* 优惠券区域 */
.coupon-section {
  background: var(--white);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.coupon-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: linear-gradient(90deg, #FFE8E8 0%, #FFF5F5 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #FF3333;
}

.coupon-badge {
  width: 48rpx;
  height: 48rpx;
  background: #FF3333;
  color: var(--white);
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.coupon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.coupon-type {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

.coupon-condition {
  font-size: 24rpx;
  color: var(--text-light);
}

.coupon-time {
  font-size: 22rpx;
  color: var(--text-lighter);
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  padding: 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 32rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
}

.action-text {
  font-size: 20rpx;
  color: var(--text-light);
}

.buy-buttons {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.cart-btn,
.buy-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cart-btn {
  background: var(--secondary-color);
  color: var(--text-color);
}

.buy-btn {
  background: var(--primary-color);
  color: var(--white);
}

/* 加载页面 */
.loading-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-color);
}

.loading-content {
  font-size: 28rpx;
  color: var(--text-lighter);
}

/* 分享弹窗 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.share-content {
  background: var(--white);
  border-radius: 24rpx 24rpx 0 0;
  padding: 48rpx 32rpx 32rpx;
  width: 100%;
}

.share-title {
  text-align: center;
  font-size: 32rpx;
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 48rpx;
}

.share-options {
  display: flex;
  justify-content: center;
  gap: 80rpx;
  margin-bottom: 48rpx;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  background: none;
  border: none;
  padding: 0;
}

.share-btn::after {
  border: none;
}

.share-icon {
  width: 80rpx;
  height: 80rpx;
}

.share-name {
  font-size: 24rpx;
  color: var(--text-color);
}

.share-cancel {
  text-align: center;
  font-size: 32rpx;
  color: var(--text-light);
  padding: 24rpx;
  border-top: 1rpx solid var(--border-color);
  margin: 0 -32rpx -32rpx;
}