const apiService = require('../../utils/api-service-fixed')
const cacheManager = require('../../utils/cacheManager')
const locationService = require('../../utils/locationService')

Page({
  data: {
    currentCity: '广州',
    categories: [
      { id: 0, name: '全部', active: true },
      { id: 1, name: '美食', active: false },
      { id: 2, name: '外卖', active: false },
      { id: 3, name: '娱乐', active: false },
      { id: 4, name: '酒店', active: false },
      { id: 5, name: '丽人', active: false }
    ],
    sortTypes: [
      { id: 1, name: '综合排序', active: true },
      { id: 2, name: '销量优先', active: false },
      { id: 3, name: '价格最低', active: false },
      { id: 4, name: '距离最近', active: false }
    ],
    activityList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    currentCategory: 0,
    currentSort: 1
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    const currentCity = locationService.getCurrentCity()
    this.setData({ currentCity })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreActivities()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 初始化页面
  async initPage() {
    wx.showLoading({ title: '加载中...' })

    try {
      await this.loadActivities(true)
    } catch (error) {
      console.error('初始化页面失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 加载活动数据
  async loadActivities(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const page = refresh ? 1 : this.data.page

      // 调用API获取活动数据
      let response
      try {
        response = await apiService.getRecommendProducts(page, this.data.pageSize)
      } catch (apiError) {
        console.log('API调用失败，使用模拟数据:', apiError)
        // API调用失败时使用模拟数据
        response = {
          data: this.generateMockActivities(page),
          hasNext: page < 5
        }
      }

      const activities = this.formatActivityData(response.data || [])

      if (refresh) {
        this.setData({
          activityList: activities,
          page: 2,
          hasMore: response.hasNext !== false && activities.length >= this.data.pageSize
        })
      } else {
        this.setData({
          activityList: [...this.data.activityList, ...activities],
          page: page + 1,
          hasMore: response.hasNext !== false && activities.length >= this.data.pageSize
        })
      }
    } catch (error) {
      console.error('加载活动失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 格式化活动数据
  formatActivityData(rawData) {
    if (!Array.isArray(rawData)) return []

    return rawData.map((item, index) => {
      // 处理美团API返回的数据结构
      const couponDetail = item.couponPackDetail || {}
      const brandInfo = item.brandInfo || {}

      return {
        id: couponDetail.skuViewId || `activity_${index}`,
        title: couponDetail.name || `精选活动 ${index + 1}`,
        subtitle: '限时优惠，先到先得',
        image: couponDetail.headUrl || '/assets/images/placeholder.png',
        price: couponDetail.sellPrice || (Math.random() * 50 + 10).toFixed(1),
        originalPrice: couponDetail.originalPrice || (Math.random() * 100 + 50).toFixed(1),
        discount: Math.floor(Math.random() * 5 + 5),
        tags: ['限时', '热销'],
        shopName: brandInfo.brandName || `商家${index + 1}`,
        distance: `${(Math.random() * 2 + 0.5).toFixed(1)}km`,
        sales: couponDetail.saleVolume || `月销${Math.floor(Math.random() * 1000 + 100)}+`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        endTime: Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000,
        validTime: couponDetail.validTime,
        couponNum: couponDetail.couponNum
      }
    })
  },

  // 生成模拟活动数据
  generateMockActivities(page) {
    const activities = []
    const baseIndex = (page - 1) * this.data.pageSize

    for (let i = 0; i < this.data.pageSize; i++) {
      const index = baseIndex + i
      activities.push({
        id: `activity_${index}`,
        title: `精选活动 ${index + 1}`,
        subtitle: '限时优惠，先到先得',
        image: '/assets/images/activity-placeholder.png',
        price: (Math.random() * 50 + 10).toFixed(1),
        originalPrice: (Math.random() * 100 + 50).toFixed(1),
        discount: Math.floor(Math.random() * 5 + 5),
        tags: ['限时', '热销'],
        shopName: `商家${index + 1}`,
        distance: `${(Math.random() * 2 + 0.5).toFixed(1)}km`,
        sales: `月销${Math.floor(Math.random() * 1000 + 100)}+`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        endTime: Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000
      })
    }

    return activities
  },

  // 加载更多活动
  loadMoreActivities() {
    this.loadActivities(false)
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadActivities(true)
      wx.showToast({ title: '刷新成功', icon: 'success' })
    } catch (error) {
      wx.showToast({ title: '刷新失败', icon: 'none' })
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 点击分类
  onCategoryTap(e) {
    const { index } = e.currentTarget.dataset
    const categories = this.data.categories.map((item, i) => ({
      ...item,
      active: i === index
    }))

    this.setData({
      categories,
      currentCategory: categories[index].id,
      activityList: [],
      page: 1,
      hasMore: true
    })

    this.loadActivities(true)
  },

  // 点击排序
  onSortTap(e) {
    const { index } = e.currentTarget.dataset
    const sortTypes = this.data.sortTypes.map((item, i) => ({
      ...item,
      active: i === index
    }))

    this.setData({
      sortTypes,
      currentSort: sortTypes[index].id,
      activityList: [],
      page: 1,
      hasMore: true
    })

    this.loadActivities(true)
  },

  // 点击活动
  onActivityTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}&type=activity`
    })
  },

  // 点击立即抢购
  async onBuyTap(e) {
    e.stopPropagation()
    const { id } = e.currentTarget.dataset

    // 获取活动信息
    const activity = this.data.activityList.find(item => item.id === id)
    if (!activity) return

    wx.showLoading({ title: '生成推广链接...' })

    try {
      // 调用美团API生成推广链接
      const response = await apiService.getReferralLink(id)
      wx.hideLoading()

      if (response && response.data && response.data.linkUrl) {
        // 复制链接到剪贴板
        wx.setClipboardData({
          data: response.data.linkUrl,
          success: () => {
            wx.showToast({
              title: '推广链接已复制',
              icon: 'success'
            })
          }
        })
      } else {
        throw new Error('API返回数据格式错误')
      }
    } catch (error) {
      wx.hideLoading()
      console.error('生成推广链接失败:', error)

      // 降级方案：生成模拟链接
      const mockLink = `https://i.meituan.com/awp/h5/coupon/detail/${id}?sid=yjd2025`
      wx.setClipboardData({
        data: mockLink,
        success: () => {
          wx.showToast({
            title: '推广链接已复制',
            icon: 'success'
          })
        },
        fail: () => {
          wx.showToast({
            title: '复制失败，请重试',
            icon: 'none'
          })
        }
      })
    }
  },

  // 城市改变事件
  onCityChange(e) {
    const { city, location } = e.detail
    this.setData({
      currentCity: city,
      activityList: [],
      page: 1,
      hasMore: true
    })

    // 更新全局城市信息
    const app = getApp()
    app.globalData.currentCity = city
    if (location) {
      app.globalData.location = location
    }

    // 重新加载活动
    this.loadActivities(true)
  },

  // 点击城市选择
  onCityTap() {
    wx.navigateTo({
      url: '/pages/city/city'
    })
  }
})