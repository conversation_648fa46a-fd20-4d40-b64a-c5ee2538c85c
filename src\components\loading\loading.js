/**
 * 加载状态组件
 */
Component({
  properties: {
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    // 加载类型：spinner(转圈), dots(点点点), skeleton(骨架屏)
    type: {
      type: String,
      value: 'spinner'
    },
    // 大小：small, medium, large
    size: {
      type: String,
      value: 'medium'
    },
    // 颜色
    color: {
      type: String,
      value: '#FF6600'
    },
    // 是否显示文本
    showText: {
      type: Boolean,
      value: true
    },
    // 是否全屏显示
    fullscreen: {
      type: Boolean,
      value: false
    }
  },

  data: {
    dotsText: '加载中'
  },

  lifetimes: {
    attached() {
      if (this.data.type === 'dots') {
        this.startDotsAnimation()
      }
    },
    detached() {
      this.stopDotsAnimation()
    }
  },

  observers: {
    'loading': function(loading) {
      if (loading && this.data.type === 'dots') {
        this.startDotsAnimation()
      } else {
        this.stopDotsAnimation()
      }
    }
  },

  methods: {
    /**
     * 开始点点点动画
     */
    startDotsAnimation() {
      if (this.dotsTimer) return
      
      let dotCount = 0
      this.dotsTimer = setInterval(() => {
        dotCount = (dotCount + 1) % 4
        const dots = '.'.repeat(dotCount)
        this.setData({
          dotsText: this.data.text.replace(/\.+$/, '') + dots
        })
      }, 500)
    },

    /**
     * 停止点点点动画
     */
    stopDotsAnimation() {
      if (this.dotsTimer) {
        clearInterval(this.dotsTimer)
        this.dotsTimer = null
      }
    },

    /**
     * 点击事件（可用于取消加载）
     */
    onTap() {
      this.triggerEvent('tap')
    }
  }
})
