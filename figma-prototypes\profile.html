<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 美团联盟原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        /* 黄色渐变背景 */
        .profile-header {
            background: linear-gradient(135deg, #FFD100, #FF6600);
            padding: 44px 16px 24px;
            position: relative;
        }

        .logout-button {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255, 255, 255, 0.3);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .user-avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .user-details {
            color: white;
        }

        .user-id {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .user-phone {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 统计卡片 */
        .stats-card {
            background: white;
            border-radius: 12px;
            margin: -12px 16px 16px;
            padding: 16px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            border-right: 1px solid #f0f0f0;
        }

        .stat-item:last-child {
            border-right: none;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #FF3333;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        /* 功能入口列表 */
        .function-list {
            margin: 0 16px;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .function-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .function-item:last-child {
            border-bottom: none;
        }

        .function-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f8f8f8;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 12px;
        }

        .function-info {
            flex: 1;
        }

        .function-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 4px;
        }

        .function-desc {
            font-size: 12px;
            color: #999;
        }

        .function-arrow {
            color: #ccc;
            font-size: 16px;
        }

        /* 版本信息 */
        .version-info {
            text-align: center;
            margin-top: 32px;
            padding: 16px;
            font-size: 12px;
            color: #999;
        }

        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding-bottom: env(safe-area-inset-bottom);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #999;
            font-size: 10px;
        }

        .tab-item.active {
            color: #FF6600;
        }

        .tab-icon {
            font-size: 20px;
        }

        .scrollable-content {
            height: calc(100vh - 60px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← 返回索引</button>
    
    <div class="phone-frame">
        <div class="scrollable-content">
            <!-- 个人信息头部 -->
            <div class="profile-header">
                <button class="logout-button">退出登录</button>
                <div class="user-info">
                    <div class="user-avatar">👤</div>
                    <div class="user-details">
                        <div class="user-id">AGx424069683</div>
                        <div class="user-phone">186****7573</div>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-card">
                <div class="stat-item">
                    <div class="stat-value">28</div>
                    <div class="stat-label">红包/神券</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">6</div>
                    <div class="stat-label">代金券</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0.0</div>
                    <div class="stat-label">单笔返现</div>
                </div>
            </div>

            <!-- 功能入口列表 -->
            <div class="function-list">
                <div class="function-item">
                    <div class="function-icon" style="background: #FFE7E7; color: #FF6600;">📋</div>
                    <div class="function-info">
                        <div class="function-title">订单中心</div>
                        <div class="function-desc">查看我的订单</div>
                    </div>
                    <div class="function-arrow">›</div>
                </div>
                <div class="function-item">
                    <div class="function-icon" style="background: #E7F5FF; color: #2196F3;">🤖</div>
                    <div class="function-info">
                        <div class="function-title">添加官方福利君</div>
                        <div class="function-desc">默认小助手，有20元</div>
                    </div>
                    <div class="function-arrow">›</div>
                </div>
                <div class="function-item">
                    <div class="function-icon" style="background: #FFF3E0; color: #FF9800;">👥</div>
                    <div class="function-info">
                        <div class="function-title">加入省钱福利群</div>
                        <div class="function-desc">天天有20元福利</div>
                    </div>
                    <div class="function-arrow">›</div>
                </div>
                <div class="function-item">
                    <div class="function-icon" style="background: #E8F5E9; color: #4CAF50;">💬</div>
                    <div class="function-info">
                        <div class="function-title">客服</div>
                        <div class="function-desc">欢迎咨询，我们会尽快答复您哦～</div>
                    </div>
                    <div class="function-arrow">›</div>
                </div>
            </div>

            <!-- 版本信息 -->
            <div class="version-info">
                当前版本：v1.0.0正式版
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon">🏠</div>
                <div>首页</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">🎯</div>
                <div>活动</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">📤</div>
                <div>分享</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon">👤</div>
                <div>我的</div>
            </div>
        </div>
    </div>
</body>
</html>