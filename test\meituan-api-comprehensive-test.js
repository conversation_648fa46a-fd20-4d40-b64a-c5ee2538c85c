/**
 * Comprehensive Meituan API Signature Test
 * Based on official documentation: https://page.meituan.net/html/1687318722216_edeb3f/index.html
 *
 * This test validates the complete signature generation process according to official specs
 */

// WeChat Mini Program environment simulation
global.wx = {
  getSystemInfoSync: () => ({ SDKVersion: '3.8.12' }),
  getAccountInfoSync: () => ({ miniProgram: { version: '1.0.0' } }),
  arrayBufferToBase64: (buffer) => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  },
  request: (options) => {
    console.log('\n🌐 MAKING REAL API REQUEST')
    console.log('URL:', options.url)
    console.log('Method:', options.method)
    console.log('Headers:', JSON.stringify(options.header, null, 2))
    console.log('Data:', JSON.stringify(options.data, null, 2))

    // Use Node.js https module for real request
    const https = require('https')
    const url = require('url')

    const parsedUrl = url.parse(options.url)
    const postData = JSON.stringify(options.data)

    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || 443,
      path: parsedUrl.path,
      method: options.method,
      headers: {
        ...options.header,
        'Content-Length': Buffer.byteLength(postData)
      }
    }

    const req = https.request(requestOptions, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        console.log('\n📥 API RESPONSE RECEIVED')
        console.log('Status Code:', res.statusCode)
        console.log('Response Headers:', JSON.stringify(res.headers, null, 2))
        console.log('Response Body:', data)

        try {
          const responseData = JSON.parse(data)
          const response = {
            statusCode: res.statusCode,
            data: responseData,
            header: res.headers
          }

          if (res.statusCode === 200) {
            options.success(response)
          } else {
            options.fail({
              statusCode: res.statusCode,
              data: responseData,
              errMsg: `HTTP ${res.statusCode}`
            })
          }
        } catch (error) {
          console.error('❌ JSON Parse Error:', error)
          options.fail({
            statusCode: res.statusCode,
            errMsg: 'JSON parse error'
          })
        }
      })
    })

    req.on('error', (error) => {
      console.error('❌ Request Error:', error)
      options.fail(error)
    })

    req.write(postData)
    req.end()
  }
}

// Node.js btoa/atob polyfill
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

const crypto = require('../src/utils/crypto-fixed-final.js')
const timeCorrection = require('../src/utils/time-correction.js')

class MeituanApiTest {
  constructor() {
    // Official API credentials provided
    this.config = {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
    }

    this.testResults = {
      configValidation: false,
      timestampGeneration: false,
      contentMD5Calculation: false,
      signatureStringConstruction: false,
      hmacSHA256Calculation: false,
      headerConstruction: false,
      apiCall: false
    }
  }

  log(step, message, data = null) {
    console.log(`\n${step} ${message}`)
    if (data) {
      console.log(JSON.stringify(data, null, 2))
    }
  }

  // Test 1: Validate API Configuration
  testConfigValidation() {
    this.log('🔧', 'STEP 1: API Configuration Validation')

    const appkeyValid = /^[a-f0-9]{32}$/.test(this.config.appkey)
    const secretValid = /^[a-f0-9]{32}$/.test(this.config.secret)

    const configData = {
      appkey: this.config.appkey,
      appkeyLength: this.config.appkey.length,
      appkeyFormat: appkeyValid ? '✅ Valid (32-char hex)' : '❌ Invalid format',
      secret: this.config.secret.substring(0, 8) + '...',
      secretLength: this.config.secret.length,
      secretFormat: secretValid ? '✅ Valid (32-char hex)' : '❌ Invalid format'
    }

    this.log('📋', 'Configuration Details:', configData)

    this.testResults.configValidation = appkeyValid && secretValid

    if (this.testResults.configValidation) {
      console.log('✅ Configuration validation PASSED')
    } else {
      console.log('❌ Configuration validation FAILED')
      console.log('   - AppKey must be 32-character hexadecimal string')
      console.log('   - Secret must be 32-character hexadecimal string')
    }

    return this.testResults.configValidation
  }

  // Test 2: Timestamp Generation (Official Spec: 13-digit milliseconds)
  testTimestampGeneration() {
    this.log('⏰', 'STEP 2: Timestamp Generation')

    // Use corrected time to handle system time issues
    const correctedTime = timeCorrection.getCorrectedTimestamp()
    const timestamp = Math.floor(correctedTime)

    const timestampData = {
      rawSystemTime: new Date().toISOString(),
      correctedTime: new Date(correctedTime).toISOString(),
      finalTimestamp: timestamp,
      timestampString: timestamp.toString(),
      length: timestamp.toString().length,
      format: timestamp.toString().length === 13 ? '✅ 13-digit milliseconds' : '❌ Invalid format',
      isInteger: Number.isInteger(timestamp) ? '✅ Integer' : '❌ Not integer',
      withinValidRange: this.validateTimestampRange(timestamp)
    }

    this.log('📊', 'Timestamp Details:', timestampData)

    this.testResults.timestampGeneration =
      timestamp.toString().length === 13 &&
      Number.isInteger(timestamp) &&
      timestampData.withinValidRange

    if (this.testResults.timestampGeneration) {
      console.log('✅ Timestamp generation PASSED')
    } else {
      console.log('❌ Timestamp generation FAILED')
    }

    this.currentTimestamp = timestamp
    return this.testResults.timestampGeneration
  }

  validateTimestampRange(timestamp) {
    const now = Date.now()
    const diff = Math.abs(now - timestamp)
    const withinTwoMinutes = diff < 120000 // 2 minutes in milliseconds

    console.log(`   Time difference: ${Math.floor(diff / 1000)} seconds`)
    console.log(`   Within 2-minute validity: ${withinTwoMinutes ? '✅ Yes' : '❌ No'}`)

    return withinTwoMinutes
  }

  // Test 3: Content-MD5 Calculation (Official Spec: Base64.encodeBase64(MD5(bodyStream.getbytes("UTF-8"))))
  testContentMD5Calculation() {
    this.log('🔐', 'STEP 3: Content-MD5 Calculation')

    const testBody = '{"latitude":39928000,"longitude":116404000,"pageNo":1,"pageSize":10}'

    console.log('   Input body string:', testBody)
    console.log('   Body length:', testBody.length)

    // According to official spec: Base64.encodeBase64(MD5(bodyStream.getbytes("UTF-8")))
    const contentMD5 = crypto.generateContentMD5(testBody)

    const md5Data = {
      inputBody: testBody,
      bodyLength: testBody.length,
      contentMD5: contentMD5,
      md5Length: contentMD5.length,
      expectedLength: 24, // Base64 encoded MD5 should be 24 characters
      formatValid: contentMD5.length === 24 ? '✅ Valid length' : '❌ Invalid length'
    }

    this.log('🔍', 'Content-MD5 Details:', md5Data)

    this.testResults.contentMD5Calculation = contentMD5.length === 24

    if (this.testResults.contentMD5Calculation) {
      console.log('✅ Content-MD5 calculation PASSED')
    } else {
      console.log('❌ Content-MD5 calculation FAILED')
    }

    this.currentContentMD5 = contentMD5
    this.currentBody = testBody
    return this.testResults.contentMD5Calculation
  }

  // Test 4: Signature String Construction (Official Spec: HTTPMethod + "\n" + Content-MD5 + "\n" + Headers + Url)
  testSignatureStringConstruction() {
    this.log('📝', 'STEP 4: Signature String Construction')

    const method = 'POST'
    const endpoint = '/query_coupon'

    // Headers construction according to official spec (dictionary sorted)
    const headers = `S-Ca-App:${this.config.appkey}\nS-Ca-Timestamp:${this.currentTimestamp}\n`

    // URL construction (Path only, no query parameters for this endpoint)
    const url = `/cps_open/common/api/v1${endpoint}`

    // Official signature string format: HTTPMethod + "\n" + Content-MD5 + "\n" + Headers + Url
    const stringToSign = `${method}\n${this.currentContentMD5}\n${headers}${url}`

    const signatureData = {
      method: method,
      contentMD5: this.currentContentMD5,
      headers: headers.replace(/\n/g, '\\n'),
      url: url,
      completeSignatureString: stringToSign.replace(/\n/g, '\\n'),
      stringLength: stringToSign.length,
      components: {
        methodPart: method,
        md5Part: this.currentContentMD5,
        headersPart: headers.replace(/\n/g, '\\n'),
        urlPart: url
      }
    }

    this.log('🔗', 'Signature String Details:', signatureData)

    // Validate signature string construction
    const expectedParts = [method, this.currentContentMD5, headers + url]
    const actualParts = stringToSign.split('\n')

    const constructionValid =
      actualParts[0] === method &&
      actualParts[1] === this.currentContentMD5 &&
      stringToSign.includes(headers) &&
      stringToSign.endsWith(url)

    this.testResults.signatureStringConstruction = constructionValid

    if (this.testResults.signatureStringConstruction) {
      console.log('✅ Signature string construction PASSED')
    } else {
      console.log('❌ Signature string construction FAILED')
      console.log('   Expected format: HTTPMethod + "\\n" + Content-MD5 + "\\n" + Headers + Url')
    }

    this.currentStringToSign = stringToSign
    return this.testResults.signatureStringConstruction
  }

  // Test 5: HMAC-SHA256 Calculation (Official Spec: HmacSHA256 with Base64 encoding)
  testHmacSHA256Calculation() {
    this.log('🔑', 'STEP 5: HMAC-SHA256 Signature Calculation')

    console.log('   Signature string:', this.currentStringToSign.replace(/\n/g, '\\n'))
    console.log('   Secret key:', this.config.secret.substring(0, 8) + '...')

    // Calculate HMAC-SHA256 according to official spec
    const hmacResult = crypto.hmacSHA256(this.currentStringToSign, this.config.secret)
    console.log('   HMAC-SHA256 hex:', hmacResult)

    // Convert to bytes and Base64 encode
    const signatureBytes = crypto.hexToBytes(hmacResult)
    const finalSignature = crypto.base64Encode(signatureBytes)

    const hmacData = {
      inputString: this.currentStringToSign.replace(/\n/g, '\\n'),
      secretKey: this.config.secret.substring(0, 8) + '...',
      hmacHex: hmacResult,
      hmacHexLength: hmacResult.length,
      signatureBytes: signatureBytes.length + ' bytes',
      finalSignature: finalSignature,
      signatureLength: finalSignature.length,
      expectedLength: 44, // Base64 encoded SHA256 should be 44 characters
      formatValid: finalSignature.length === 44 ? '✅ Valid length' : '❌ Invalid length'
    }

    this.log('🔐', 'HMAC-SHA256 Details:', hmacData)

    this.testResults.hmacSHA256Calculation =
      hmacResult.length === 64 && // SHA256 hex should be 64 chars
      finalSignature.length === 44 // Base64 encoded should be 44 chars

    if (this.testResults.hmacSHA256Calculation) {
      console.log('✅ HMAC-SHA256 calculation PASSED')
    } else {
      console.log('❌ HMAC-SHA256 calculation FAILED')
    }

    this.currentSignature = finalSignature
    return this.testResults.hmacSHA256Calculation
  }

  // Test 6: Request Header Construction (Official Spec compliance)
  testHeaderConstruction() {
    this.log('📋', 'STEP 6: Request Header Construction')

    const headers = {
      'Content-Type': 'application/json;charset=utf-8',
      'S-Ca-App': this.config.appkey,
      'S-Ca-Timestamp': this.currentTimestamp.toString(),
      'S-Ca-Signature': this.currentSignature,
      'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
      'Content-MD5': this.currentContentMD5
    }

    // Validate each header according to official spec
    const validations = {
      'Content-Type': headers['Content-Type'] === 'application/json;charset=utf-8',
      'S-Ca-App': headers['S-Ca-App'].length === 32,
      'S-Ca-Timestamp': /^\d{13}$/.test(headers['S-Ca-Timestamp']),
      'S-Ca-Signature': headers['S-Ca-Signature'].length === 44,
      'S-Ca-Signature-Headers': headers['S-Ca-Signature-Headers'] === 'S-Ca-App,S-Ca-Timestamp',
      'Content-MD5': headers['Content-MD5'].length === 24
    }

    const headerData = {
      headers: headers,
      validations: Object.entries(validations).map(([key, valid]) => ({
        header: key,
        value: headers[key],
        valid: valid ? '✅ Valid' : '❌ Invalid'
      }))
    }

    this.log('📊', 'Header Construction Details:', headerData)

    this.testResults.headerConstruction = Object.values(validations).every(v => v)

    if (this.testResults.headerConstruction) {
      console.log('✅ Header construction PASSED')
    } else {
      console.log('❌ Header construction FAILED')
      Object.entries(validations).forEach(([key, valid]) => {
        if (!valid) {
          console.log(`   - ${key}: Invalid format or value`)
        }
      })
    }

    this.currentHeaders = headers
    return this.testResults.headerConstruction
  }

  // Test 7: Real API Call
  async testApiCall() {
    this.log('🌐', 'STEP 7: Real API Call Test')

    const testData = {
      latitude: 39928000,  // Beijing Tiananmen latitude * 1000000
      longitude: 116404000, // Beijing Tiananmen longitude * 1000000
      pageNo: 1,
      pageSize: 10
    }

    const url = `${this.config.baseUrl}/query_coupon`

    console.log('   Making request to:', url)
    console.log('   Request data:', JSON.stringify(testData, null, 2))

    return new Promise((resolve) => {
      wx.request({
        url: url,
        method: 'POST',
        header: this.currentHeaders,
        data: testData,
        success: (response) => {
          this.log('✅', 'API Response Received')

          const responseData = {
            statusCode: response.statusCode,
            responseData: response.data,
            serverTime: response.header?.date || response.header?.Date,
            success: response.statusCode === 200 && response.data?.code === 0
          }

          console.log('   Status Code:', response.statusCode)
          console.log('   Response Code:', response.data?.code)
          console.log('   Response Message:', response.data?.message)

          if (responseData.success) {
            console.log('✅ API call PASSED - Signature verification successful!')
            console.log('   Data received:', response.data?.data ? 'Yes' : 'No')
            this.testResults.apiCall = true
          } else {
            console.log('❌ API call FAILED')
            if (response.data?.message?.includes('签名验证失败')) {
              console.log('   🔍 SIGNATURE VERIFICATION FAILED')
              console.log('   This indicates either:')
              console.log('   1. API credentials are incorrect')
              console.log('   2. Account status issues')
              console.log('   3. Permission problems')
            }
            this.testResults.apiCall = false
          }

          this.apiResponse = responseData
          resolve(this.testResults.apiCall)
        },
        fail: (error) => {
          console.log('❌ API call FAILED - Network error')
          console.log('   Error:', error)
          this.testResults.apiCall = false
          this.apiResponse = { error: error }
          resolve(false)
        }
      })
    })
  }

  // Generate comprehensive test report
  generateReport() {
    this.log('📊', 'COMPREHENSIVE TEST REPORT')

    const overallSuccess = Object.values(this.testResults).every(result => result)

    console.log('\n=== TEST RESULTS SUMMARY ===')
    Object.entries(this.testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED'
      console.log(`${test.padEnd(25)}: ${status}`)
    })

    console.log('\n=== OVERALL RESULT ===')
    if (overallSuccess) {
      console.log('🎉 ALL TESTS PASSED!')
      console.log('   The technical implementation is CORRECT')
      console.log('   Signature generation follows official specification')
      console.log('   API call was successful')
    } else {
      console.log('⚠️  SOME TESTS FAILED')

      // Provide specific diagnostic information
      if (!this.testResults.configValidation) {
        console.log('\n🔧 CONFIGURATION ISSUES:')
        console.log('   - Check API credentials format')
        console.log('   - Ensure AppKey and Secret are 32-character hex strings')
      }

      if (!this.testResults.timestampGeneration) {
        console.log('\n⏰ TIMESTAMP ISSUES:')
        console.log('   - System time may be incorrect')
        console.log('   - Timestamp not in 13-digit millisecond format')
      }

      if (!this.testResults.contentMD5Calculation) {
        console.log('\n🔐 CONTENT-MD5 ISSUES:')
        console.log('   - MD5 calculation not following official spec')
        console.log('   - Should be Base64.encodeBase64(MD5(bodyStream.getbytes("UTF-8")))')
      }

      if (!this.testResults.signatureStringConstruction) {
        console.log('\n📝 SIGNATURE STRING ISSUES:')
        console.log('   - String format not following official spec')
        console.log('   - Should be: HTTPMethod + "\\n" + Content-MD5 + "\\n" + Headers + Url')
      }

      if (!this.testResults.hmacSHA256Calculation) {
        console.log('\n🔑 HMAC-SHA256 ISSUES:')
        console.log('   - HMAC calculation or Base64 encoding incorrect')
      }

      if (!this.testResults.headerConstruction) {
        console.log('\n📋 HEADER ISSUES:')
        console.log('   - Request headers not following official spec')
      }

      if (!this.testResults.apiCall && this.apiResponse) {
        console.log('\n🌐 API CALL ISSUES:')
        if (this.apiResponse.error) {
          console.log('   - Network connectivity problem')
        } else if (this.apiResponse.responseData?.message?.includes('签名验证失败')) {
          console.log('   - SIGNATURE VERIFICATION FAILED on server side')
          console.log('   - This suggests:')
          console.log('     1. API credentials (AppKey/Secret) are incorrect')
          console.log('     2. Account not properly authenticated')
          console.log('     3. Application not approved')
          console.log('     4. Missing API permissions')
          console.log('   - RECOMMENDED ACTION: Verify credentials in Meituan Union backend')
        }
      }
    }

    console.log('\n=== NEXT STEPS ===')
    if (overallSuccess) {
      console.log('✅ Implementation is correct - you can use this in production')
    } else {
      console.log('🔧 Focus on fixing the failed test areas above')
      console.log('📞 If technical implementation passes but API call fails,')
      console.log('   contact Meituan Union technical support with:')
      console.log('   - Your AppKey (safe to share)')
      console.log('   - This test report')
      console.log('   - Account verification status')
    }

    return overallSuccess
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 STARTING COMPREHENSIVE MEITUAN API SIGNATURE TEST')
    console.log('📖 Based on official documentation: https://page.meituan.net/html/1687318722216_edeb3f/index.html')
    console.log('🔑 Using provided credentials')

    try {
      // Run all tests in sequence
      this.testConfigValidation()
      this.testTimestampGeneration()
      this.testContentMD5Calculation()
      this.testSignatureStringConstruction()
      this.testHmacSHA256Calculation()
      this.testHeaderConstruction()
      await this.testApiCall()

      // Generate final report
      return this.generateReport()

    } catch (error) {
      console.error('❌ TEST EXECUTION FAILED:', error)
      console.error('Stack trace:', error.stack)
      return false
    }
  }
}

// Export for use in WeChat Mini Program or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MeituanApiTest
}

// Auto-run if executed directly
if (require.main === module) {
  const test = new MeituanApiTest()
  test.runAllTests().then(success => {
    console.log(`\n🏁 TEST COMPLETED: ${success ? 'SUCCESS' : 'FAILURE'}`)
    process.exit(success ? 0 : 1)
  })
}
