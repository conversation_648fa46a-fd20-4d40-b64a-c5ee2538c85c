/**
 * 美团API小程序调试版本
 * 专门用于小程序环境的详细调试
 */

const crypto = require('./crypto-fixed-final.js')
const meituanTimeFix = require('./meituan-time-fix-official.js')

class ApiServiceDebug {
  constructor() {
    this.config = {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
    }
    
    console.log('🔧 API服务调试版本初始化')
    console.log('环境检测:', {
      isWechat: typeof wx !== 'undefined',
      hasArrayBufferToBase64: typeof wx !== 'undefined' && typeof wx.arrayBufferToBase64 === 'function',
      cryptoLoaded: typeof crypto !== 'undefined',
      meituanTimeFixLoaded: typeof meituanTimeFix !== 'undefined'
    })
  }

  generateContentMD5(body) {
    console.log('📝 开始生成Content-MD5')
    
    let bodyStr = ''
    if (body && typeof body === 'object' && Object.keys(body).length > 0) {
      bodyStr = JSON.stringify(body)
    } else if (body && typeof body === 'string' && body.trim() !== '') {
      bodyStr = body
    }
    
    console.log('Body字符串:', bodyStr)
    console.log('Body长度:', bodyStr.length)
    
    try {
      const result = crypto.generateContentMD5(bodyStr)
      console.log('✅ Content-MD5生成成功:', result)
      return result
    } catch (error) {
      console.error('❌ Content-MD5生成失败:', error)
      throw error
    }
  }

  generateTimestamp() {
    console.log('⏰ 开始生成时间戳')
    
    try {
      const timestamp = meituanTimeFix.getMeituanTimestamp()
      console.log('✅ 时间戳生成成功:', {
        timestamp: timestamp,
        type: typeof timestamp,
        length: timestamp.toString().length,
        date: new Date(timestamp).toISOString(),
        isInteger: Number.isInteger(timestamp)
      })
      return timestamp
    } catch (error) {
      console.error('❌ 时间戳生成失败:', error)
      throw error
    }
  }

  generateSignature(method, endpoint, body = {}) {
    console.log('🔐 开始生成签名')
    console.log('请求参数:', { method, endpoint, body })
    
    try {
      // 1. 生成时间戳
      const timestamp = this.generateTimestamp()
      
      // 2. 生成Content-MD5
      const bodyStr = typeof body === 'object' ? JSON.stringify(body) : body
      const contentMD5 = this.generateContentMD5(bodyStr)
      
      // 3. 构建Headers字符串
      const headers = `S-Ca-App:${this.config.appkey}\nS-Ca-Timestamp:${timestamp}\n`
      console.log('Headers字符串:', headers.replace(/\n/g, '\\n'))
      
      // 4. 构建URL
      const url = `/cps_open/common/api/v1${endpoint}`
      console.log('URL:', url)
      
      // 5. 构建签名字符串
      const stringToSign = `${method}\n${contentMD5}\n${headers}${url}`
      console.log('签名字符串:', stringToSign.replace(/\n/g, '\\n'))
      console.log('签名字符串长度:', stringToSign.length)
      
      // 6. 计算HMAC-SHA256
      const hmacResult = crypto.hmacSHA256(stringToSign, this.config.secret)
      console.log('HMAC结果:', hmacResult)
      
      // 7. 转换为字节数组
      const signatureBytes = crypto.hexToBytes(hmacResult)
      console.log('签名字节数组长度:', signatureBytes.length)
      
      // 8. Base64编码
      const signature = crypto.base64Encode(signatureBytes)
      console.log('✅ 最终签名:', signature)
      console.log('签名长度:', signature.length)
      
      return {
        signature,
        timestamp: timestamp.toString(),
        contentMD5
      }
    } catch (error) {
      console.error('❌ 签名生成失败:', error)
      throw error
    }
  }

  async request(endpoint, data = {}) {
    console.log('🚀 开始API请求')
    console.log('端点:', endpoint)
    console.log('数据:', data)
    
    return new Promise((resolve, reject) => {
      try {
        // 生成签名
        const signatureResult = this.generateSignature('POST', endpoint, data)
        
        // 构建请求头
        const headers = {
          'Content-Type': 'application/json;charset=utf-8',
          'S-Ca-App': this.config.appkey,
          'S-Ca-Timestamp': signatureResult.timestamp,
          'S-Ca-Signature': signatureResult.signature,
          'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
          'Content-MD5': signatureResult.contentMD5
        }
        
        console.log('📋 请求头详情:')
        Object.entries(headers).forEach(([key, value]) => {
          console.log(`  ${key}: ${value}`)
        })
        
        // 验证请求头格式
        const validations = {
          'Content-Type正确': headers['Content-Type'] === 'application/json;charset=utf-8',
          'S-Ca-App长度': headers['S-Ca-App'].length === 32,
          'S-Ca-Timestamp格式': /^\d{13}$/.test(headers['S-Ca-Timestamp']),
          'S-Ca-Signature长度': headers['S-Ca-Signature'].length === 44,
          'S-Ca-Signature-Headers格式': headers['S-Ca-Signature-Headers'] === 'S-Ca-App,S-Ca-Timestamp',
          'Content-MD5长度': headers['Content-MD5'].length === 24
        }
        
        console.log('🔍 请求头验证:')
        Object.entries(validations).forEach(([check, result]) => {
          console.log(`  ${check}: ${result ? '✅' : '❌'}`)
        })
        
        const allValid = Object.values(validations).every(v => v)
        if (!allValid) {
          console.error('❌ 请求头格式验证失败')
          reject(new Error('请求头格式验证失败'))
          return
        }
        
        // 发送请求
        const url = `${this.config.baseUrl}${endpoint}`
        console.log('📡 发送请求到:', url)
        
        wx.request({
          url: url,
          method: 'POST',
          header: headers,
          data: data,
          success: (response) => {
            console.log('📥 收到响应:')
            console.log('状态码:', response.statusCode)
            console.log('响应头:', response.header)
            console.log('响应数据:', response.data)
            
            if (response.statusCode === 200) {
              const result = response.data
              if (result.code === 0) {
                console.log('✅ API调用成功')
                resolve(result)
              } else {
                console.error('❌ API返回错误:', result.message)
                
                // 详细分析错误
                if (result.message && result.message.includes('签名验证失败')) {
                  console.log('🔍 签名验证失败详细分析:')
                  console.log('1. 检查AppKey是否正确:', this.config.appkey)
                  console.log('2. 检查Secret是否正确:', this.config.secret.substring(0, 8) + '...')
                  console.log('3. 检查时间戳:', signatureResult.timestamp)
                  console.log('4. 检查Content-MD5:', signatureResult.contentMD5)
                  console.log('5. 检查签名:', signatureResult.signature)
                  
                  // 时间戳分析
                  const timestampDate = new Date(parseInt(signatureResult.timestamp))
                  const now = new Date()
                  const timeDiff = Math.abs(now.getTime() - timestampDate.getTime())
                  console.log('时间戳分析:', {
                    timestampDate: timestampDate.toISOString(),
                    currentTime: now.toISOString(),
                    timeDiffSeconds: Math.floor(timeDiff / 1000),
                    isWithin2Minutes: timeDiff < 120000
                  })
                }
                
                reject(new Error(result.message))
              }
            } else {
              console.error('❌ HTTP错误:', response.statusCode)
              reject(new Error(`HTTP ${response.statusCode}`))
            }
          },
          fail: (error) => {
            console.error('❌ 请求失败:', error)
            reject(new Error('网络请求失败'))
          }
        })
        
      } catch (error) {
        console.error('❌ 请求准备失败:', error)
        reject(error)
      }
    })
  }

  // 测试方法
  async testAPI() {
    console.log('🧪 开始API测试')
    
    const testData = {
      latitude: 39928000,
      longitude: 116404000,
      pageNo: 1,
      pageSize: 10
    }
    
    try {
      const result = await this.request('/query_coupon', testData)
      console.log('🎉 测试成功:', result)
      return result
    } catch (error) {
      console.error('💥 测试失败:', error.message)
      throw error
    }
  }
}

module.exports = new ApiServiceDebug()
