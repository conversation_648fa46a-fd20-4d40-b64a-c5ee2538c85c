/**
 * 真实API测试
 * 测试修复后的美团API调用
 */

// 模拟微信小程序环境
global.wx = {
  getSystemInfoSync: () => ({ SDKVersion: '3.8.12' }),
  getAccountInfoSync: () => ({ miniProgram: { version: '1.0.0' } }),
  arrayBufferToBase64: (buffer) => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  },
  request: (options) => {
    console.log('\n=== 发送真实API请求 ===')
    console.log('URL:', options.url)
    console.log('Method:', options.method)
    console.log('Headers:')
    Object.entries(options.header).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`)
    })
    console.log('Data:', JSON.stringify(options.data, null, 2))
    
    // 使用Node.js的https模块发送真实请求
    const https = require('https')
    const url = require('url')
    
    const parsedUrl = url.parse(options.url)
    const postData = JSON.stringify(options.data)
    
    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || 443,
      path: parsedUrl.path,
      method: options.method,
      headers: {
        ...options.header,
        'Content-Length': Buffer.byteLength(postData)
      }
    }
    
    const req = https.request(requestOptions, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        console.log('\n=== API响应 ===')
        console.log('Status Code:', res.statusCode)
        console.log('Headers:')
        Object.entries(res.headers).forEach(([key, value]) => {
          console.log(`  ${key}: ${value}`)
        })
        console.log('Response Body:', data)
        
        try {
          const responseData = JSON.parse(data)
          const response = {
            statusCode: res.statusCode,
            data: responseData,
            header: res.headers
          }
          
          if (res.statusCode === 200) {
            options.success(response)
          } else {
            options.fail({
              statusCode: res.statusCode,
              data: responseData,
              errMsg: `HTTP ${res.statusCode}`
            })
          }
        } catch (error) {
          console.error('解析响应JSON失败:', error)
          options.fail({
            statusCode: res.statusCode,
            errMsg: 'JSON parse error'
          })
        }
      })
    })
    
    req.on('error', (error) => {
      console.error('请求错误:', error)
      options.fail(error)
    })
    
    req.write(postData)
    req.end()
  }
}

// Node.js环境下的btoa/atob实现
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

const apiService = require('../src/utils/api-service-fixed.js')

console.log('=== 美团API真实测试 ===')
console.log('开始测试修复后的API调用...\n')

// 测试查询优惠券接口
const testData = {
  latitude: 39928000,  // 北京天安门纬度 * 1000000
  longitude: 116404000, // 北京天安门经度 * 1000000
  pageNo: 1,
  pageSize: 10
}

console.log('测试数据:', testData)
console.log('开始调用API...\n')

apiService.request('/query_coupon', testData)
  .then(result => {
    console.log('\n=== 测试结果 ===')
    console.log('✅ API调用成功!')
    console.log('响应代码:', result.code)
    console.log('响应消息:', result.message)
    
    if (result.data && result.data.length > 0) {
      console.log('获取到', result.data.length, '条优惠券数据')
      console.log('第一条数据示例:')
      console.log(JSON.stringify(result.data[0], null, 2))
    } else {
      console.log('未获取到优惠券数据，可能是地区限制或其他原因')
    }
    
    console.log('\n🎉 签名验证成功，时间修正工具工作正常!')
  })
  .catch(error => {
    console.log('\n=== 测试结果 ===')
    console.log('❌ API调用失败')
    console.log('错误信息:', error.message)
    
    if (error.message.includes('请求签名验证失败')) {
      console.log('\n🔍 签名验证仍然失败，可能的原因:')
      console.log('1. 时间戳仍然不准确')
      console.log('2. 签名算法有误')
      console.log('3. 请求头格式不正确')
      console.log('4. API密钥配置错误')
    } else if (error.message.includes('时间戳')) {
      console.log('\n🔍 时间戳相关错误，需要进一步调整时间修正')
    } else {
      console.log('\n🔍 其他错误，可能是网络或服务器问题')
    }
    
    console.log('\n建议检查:')
    console.log('- 系统时间是否正确')
    console.log('- 网络连接是否正常')
    console.log('- API配置是否正确')
  })

// 设置超时
setTimeout(() => {
  console.log('\n⏰ 测试超时，请检查网络连接')
  process.exit(1)
}, 30000)
