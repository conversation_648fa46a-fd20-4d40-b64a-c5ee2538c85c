/**
 * 美团联盟API服务 - 统一版本
 * 基于官方文档：https://page.meituan.net/html/1687318722216_edeb3f/index.html
 * 
 * 功能特性：
 * - 完整的签名算法实现
 * - 智能时间同步机制
 * - 自动错误重试
 * - 微信小程序兼容
 */

const crypto = require('./crypto-fixed-final.js')

class MeituanApiService {
  constructor() {
    // API配置
    this.config = {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
    }
    
    // 时间同步配置
    this.timeSync = {
      serverOffset: 0,        // 服务器时间偏移量（毫秒）
      lastSyncTime: 0,        // 上次同步时间
      syncAttempts: 0,        // 同步尝试次数
      maxSyncAttempts: 3      // 最大同步尝试次数
    }
    
    console.log('🚀 美团API服务初始化完成')
  }

  /**
   * 生成时间戳
   * 修复：直接使用系统时间，避免过度修正导致的时间戳过期问题
   */
  generateTimestamp() {
    // 使用系统时间 + 服务器偏移量
    const timestamp = Date.now() + this.timeSync.serverOffset
    
    console.log('⏰ 时间戳生成:', {
      原始时间: new Date().toISOString(),
      服务器偏移: this.timeSync.serverOffset,
      最终时间戳: timestamp,
      对应时间: new Date(timestamp).toISOString(),
      时间戳长度: timestamp.toString().length
    })
    
    return Math.floor(timestamp)
  }

  /**
   * 生成Content-MD5
   * 按照官方规范：Base64.encodeBase64(MD5(bodyStream.getbytes("UTF-8")))
   */
  generateContentMD5(body) {
    let bodyStr = ''
    if (body && typeof body === 'object' && Object.keys(body).length > 0) {
      bodyStr = JSON.stringify(body)
    } else if (body && typeof body === 'string' && body.trim() !== '') {
      bodyStr = body
    }
    
    const result = crypto.generateContentMD5(bodyStr)
    
    console.log('📝 Content-MD5生成:', {
      输入内容: bodyStr,
      内容长度: bodyStr.length,
      MD5结果: result,
      结果长度: result.length
    })
    
    return result
  }

  /**
   * 生成签名
   * 严格按照美团官方文档实现
   */
  generateSignature(method, endpoint, body = {}) {
    console.log('🔐 开始生成签名')
    
    // 1. 生成时间戳
    const timestamp = this.generateTimestamp()
    
    // 2. 生成Content-MD5
    const bodyStr = typeof body === 'object' ? JSON.stringify(body) : body
    const contentMD5 = this.generateContentMD5(bodyStr)
    
    // 3. 构建Headers字符串（按字典排序）
    const headers = `S-Ca-App:${this.config.appkey}\nS-Ca-Timestamp:${timestamp}\n`
    
    // 4. 构建URL（只包含路径）
    const url = `/cps_open/common/api/v1${endpoint}`
    
    // 5. 构建签名字符串
    const stringToSign = `${method}\n${contentMD5}\n${headers}${url}`
    
    console.log('🔗 签名字符串构建:', {
      方法: method,
      ContentMD5: contentMD5,
      请求头: headers.replace(/\n/g, '\\n'),
      URL: url,
      完整签名字符串: stringToSign.replace(/\n/g, '\\n')
    })
    
    // 6. 计算HMAC-SHA256签名
    const hmacResult = crypto.hmacSHA256(stringToSign, this.config.secret)
    const signatureBytes = crypto.hexToBytes(hmacResult)
    const signature = crypto.base64Encode(signatureBytes)
    
    console.log('🔑 签名计算完成:', {
      HMAC结果: hmacResult,
      最终签名: signature,
      签名长度: signature.length
    })
    
    return {
      signature,
      timestamp: timestamp.toString(),
      contentMD5
    }
  }

  /**
   * 构建请求头
   */
  buildHeaders(signatureResult) {
    const headers = {
      'Content-Type': 'application/json;charset=utf-8',
      'S-Ca-App': this.config.appkey,
      'S-Ca-Timestamp': signatureResult.timestamp,
      'S-Ca-Signature': signatureResult.signature,
      'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
      'Content-MD5': signatureResult.contentMD5
    }
    
    console.log('📋 请求头构建完成:', headers)
    
    return headers
  }

  /**
   * 从服务器响应中学习时间偏移
   */
  learnServerTime(response) {
    if (!response || !response.header) return
    
    const serverTimeStr = response.header['Date'] || 
                         response.header['date'] || 
                         response.header['Server-Time'] || 
                         response.header['server-time']
    
    if (serverTimeStr) {
      const serverTime = new Date(serverTimeStr).getTime()
      const clientTime = Date.now()
      const newOffset = serverTime - clientTime
      
      // 只有在偏移量合理时才应用（避免过度调整）
      if (Math.abs(newOffset) < 300000) { // 5分钟内的偏移是合理的
        this.timeSync.serverOffset = newOffset
        this.timeSync.lastSyncTime = clientTime
        
        console.log('🕐 服务器时间同步:', {
          服务器时间: new Date(serverTime).toISOString(),
          客户端时间: new Date(clientTime).toISOString(),
          时间偏移: newOffset,
          偏移秒数: Math.round(newOffset / 1000)
        })
      }
    }
  }

  /**
   * 处理时间戳过期错误
   */
  handleTimestampError() {
    this.timeSync.syncAttempts++
    
    if (this.timeSync.syncAttempts <= this.timeSync.maxSyncAttempts) {
      // 渐进式调整策略
      let adjustment = 0
      if (this.timeSync.syncAttempts === 1) {
        adjustment = 30000  // 向前调整30秒
      } else if (this.timeSync.syncAttempts === 2) {
        adjustment = -30000 // 向后调整30秒
      } else {
        adjustment = 0      // 重置偏移
      }
      
      this.timeSync.serverOffset += adjustment
      
      console.log(`⚠️ 时间戳过期处理 第${this.timeSync.syncAttempts}次:`, {
        调整量: adjustment,
        新偏移: this.timeSync.serverOffset,
        调整秒数: Math.abs(adjustment) / 1000
      })
      
      return true
    }
    
    return false
  }

  /**
   * 发送API请求
   */
  async request(endpoint, data = {}) {
    console.log('🚀 开始API请求:', { endpoint, data })
    
    return new Promise((resolve, reject) => {
      const makeRequest = () => {
        try {
          // 生成签名
          const signatureResult = this.generateSignature('POST', endpoint, data)
          
          // 构建请求头
          const headers = this.buildHeaders(signatureResult)
          
          // 构建完整URL
          const url = `${this.config.baseUrl}${endpoint}`
          
          console.log('📡 发送请求到:', url)
          
          // 发送微信小程序请求
          wx.request({
            url: url,
            method: 'POST',
            header: headers,
            data: data,
            success: (response) => {
              console.log('📥 收到响应:', {
                状态码: response.statusCode,
                响应数据: response.data
              })
              
              // 学习服务器时间
              this.learnServerTime(response)
              
              if (response.statusCode === 200) {
                const result = response.data
                if (result.code === 0) {
                  console.log('✅ API调用成功')
                  this.timeSync.syncAttempts = 0 // 重置重试次数
                  resolve(result)
                } else {
                  console.error('❌ API返回错误:', result.message)
                  
                  // 处理时间戳过期错误
                  if (result.message && result.message.includes('时间戳已过期')) {
                    if (this.handleTimestampError()) {
                      console.log('🔄 重试API请求')
                      setTimeout(makeRequest, 1000) // 1秒后重试
                      return
                    }
                  }
                  
                  reject(new Error(result.message))
                }
              } else {
                console.error('❌ HTTP错误:', response.statusCode)
                reject(new Error(`HTTP ${response.statusCode}`))
              }
            },
            fail: (error) => {
              console.error('❌ 请求失败:', error)
              reject(new Error('网络请求失败'))
            }
          })
          
        } catch (error) {
          console.error('❌ 请求准备失败:', error)
          reject(error)
        }
      }
      
      makeRequest()
    })
  }

  /**
   * 查询优惠券
   */
  async queryCoupon(params) {
    return this.request('/query_coupon', params)
  }

  /**
   * 获取推荐商品（使用相同的查询优惠券接口）
   */
  async getRecommendProducts(params) {
    return this.request('/query_coupon', params)
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      配置状态: '正常',
      时间偏移: this.timeSync.serverOffset,
      偏移秒数: Math.round(this.timeSync.serverOffset / 1000),
      同步次数: this.timeSync.syncAttempts,
      上次同步: this.timeSync.lastSyncTime ? new Date(this.timeSync.lastSyncTime).toISOString() : '未同步'
    }
  }
}

// 创建单例实例
const meituanApiService = new MeituanApiService()

module.exports = meituanApiService
