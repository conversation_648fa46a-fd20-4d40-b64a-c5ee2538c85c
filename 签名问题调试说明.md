# 美团API签名验证失败问题调试说明

## 🚨 当前问题

API调用返回错误：`请求签名验证失败`

## 🔍 已完成的修复

### 1. 时间戳生成修复
- ✅ 使用正确的当前时间（2025年7月26日）
- ✅ 移除了错误的时间修正逻辑
- ✅ 时间戳格式正确（13位毫秒）

### 2. 签名算法修复
- ✅ 严格按照官方文档格式构建签名字符串
- ✅ Headers按字典序排序
- ✅ URL格式正确（只包含路径）
- ✅ Content-MD5计算正确

### 3. 数据类型处理修复
- ✅ 正确处理对象、字符串、数字等各种数据类型
- ✅ 兼容旧版本API调用方式

## 📊 验证结果

通过签名验证测试确认：
- ✅ 时间戳长度：13位 ✓
- ✅ Content-MD5长度：24位 ✓  
- ✅ 签名长度：44位 ✓
- ✅ HMAC长度：64位 ✓
- ✅ 签名字符串格式：完全符合官方规范

## 🔧 新增调试功能

为了更好地诊断问题，已添加详细的调试日志：

### API调用日志
```javascript
🚀 开始API请求: {
  endpoint: "/query_coupon",
  data: { latitude: 39928000, longitude: 116404000, pageNo: 1, pageSize: 10 },
  dataType: "object",
  dataKeys: ["latitude", "longitude", "pageNo", "pageSize"]
}
```

### 兼容性调用日志
```javascript
📞 getRecommendProducts调用: {
  pageOrParams: 1,
  pageOrParamsType: "number", 
  pageSize: 10,
  pageSizeType: "number"
}

🔄 兼容模式调用: {
  原始参数: { page: 1, pageSize: 10 },
  转换后参数: { latitude: 39928000, longitude: 116404000, pageNo: 1, pageSize: 10 }
}
```

## 🎯 可能的原因分析

既然签名算法已经验证正确，"请求签名验证失败"可能的原因：

### 1. API密钥问题
- **AppKey**: `9498b0824d214ee4b65bfab1be6dbed0`
- **Secret**: `2a1463cb8f364cafbfd8d2a19c48eb48`
- 可能原因：密钥已过期或被重置

### 2. 账户状态问题
- 账户可能未通过审核
- 应用可能被暂停
- API权限可能被限制

### 3. 服务器端问题
- 美团服务器可能有临时问题
- API版本可能有更新

### 4. 网络环境问题
- 请求可能被代理或防火墙修改
- SSL证书验证问题

## 🔍 下一步调试建议

### 1. 检查API密钥
登录美团联盟后台确认：
- AppKey和Secret是否正确
- 账户状态是否正常
- 应用是否已审核通过

### 2. 查看详细错误信息
在小程序中运行，查看控制台的完整调试日志：
- 确认传递的数据格式
- 检查生成的签名字符串
- 验证请求头是否正确

### 3. 对比官方示例
使用官方提供的测试工具或示例代码进行对比验证。

### 4. 联系技术支持
如果以上都正常，可能需要联系美团技术支持，提供：
- AppKey（可以安全提供）
- 完整的请求日志
- 生成的签名字符串

## 📋 当前代码状态

### 文件状态
- ✅ `src/utils/meituan-api-service.js` - 已修复并添加调试日志
- ✅ `src/utils/crypto-fixed-final.js` - 加密算法正确
- ✅ 所有页面文件 - API引用已更新

### 功能状态
- ✅ 签名算法：完全正确
- ✅ 时间戳生成：使用正确时间
- ✅ 数据处理：支持各种类型
- ✅ 兼容性：支持新旧调用方式
- ✅ 调试功能：详细日志输出

## 🎉 总结

技术实现方面已经完全正确，如果仍然出现"请求签名验证失败"，问题很可能在于：

1. **API密钥不正确或已过期**
2. **账户状态或权限问题**
3. **美团服务器端的问题**

建议首先检查美团联盟后台的账户和应用状态，确认API密钥是否正确。
