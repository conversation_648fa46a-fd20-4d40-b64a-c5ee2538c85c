<!--搜索页面-->
<view class="search-page">
  <!-- 搜索栏 -->
  <view class="search-header">
    <view class="search-bar">
      <image class="search-icon" src="/assets/icons/search.png" />
      <input class="search-input" 
             placeholder="搜主菜站" 
             value="{{keyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchSubmit"
             focus="{{!showResults}}" />
      <view class="search-btn" bindtap="onSearchSubmit">
        <text>搜索</text>
      </view>
    </view>
  </view>

  <!-- 搜索建议页面 -->
  <view class="search-suggest" wx:if="{{!showResults}}">
    <!-- 搜索历史 -->
    <view class="history-section" wx:if="{{searchHistory.length > 0}}">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <view class="clear-btn" bindtap="onClearHistory">
          <image class="clear-icon" src="/assets/icons/delete.png" />
        </view>
      </view>
      <view class="history-list">
        <view class="history-item" wx:for="{{searchHistory}}" wx:key="*this"
              data-keyword="{{item}}" bindtap="onHistoryTap">
          <text class="history-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view class="hot-section">
      <view class="section-title">热门搜索</view>
      <view class="hot-keywords">
        <view class="hot-item" wx:for="{{hotKeywords}}" wx:key="*this"
              data-keyword="{{item}}" bindtap="onHotKeywordTap">
          <text class="hot-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索结果页面 -->
  <view class="search-results" wx:if="{{showResults}}">
    <!-- 结果统计 -->
    <view class="result-header" wx:if="{{searchResults.length > 0}}">
      <text class="result-count">找到 {{searchResults.length}} 个相关商品</text>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view class="product-item" wx:for="{{searchResults}}" wx:key="id"
            data-id="{{item.id}}" bindtap="onProductTap">
        <image class="product-image" src="{{item.image}}" mode="aspectFill" lazy-load />
        <view class="product-info">
          <text class="product-title">{{item.title}}</text>
          <view class="product-price">
            <text class="price">¥{{item.price}}</text>
            <text class="price-original">¥{{item.originalPrice}}</text>
          </view>
          <view class="product-tags">
            <text class="tag tag-primary" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
          </view>
          <text class="product-shop">{{item.shopName}} · {{item.sales}}</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="loading" wx:if="{{!hasMore && searchResults.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && searchResults.length === 0}}">
      <image class="empty-icon" src="/assets/icons/empty.png" />
      <text class="empty-text">没有找到相关商品</text>
      <text class="empty-tip">试试其他关键词吧</text>
    </view>
  </view>
</view>