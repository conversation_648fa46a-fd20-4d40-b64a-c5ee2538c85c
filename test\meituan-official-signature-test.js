/**
 * 美团官方签名算法测试
 * 严格按照官方文档实现
 */

// 模拟微信小程序环境
global.wx = {
  getSystemInfoSync: () => ({ SDKVersion: '3.8.12' }),
  getAccountInfoSync: () => ({ miniProgram: { version: '1.0.0' } }),
  arrayBufferToBase64: (buffer) => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }
}

// Node.js环境下的btoa/atob实现
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

const crypto = require('../src/utils/crypto-fixed-final.js')

console.log('=== 美团官方签名算法测试 ===\n')

// 测试配置
const config = {
  appkey: '9498b0824d214ee4b65bfab1be6dbed0',
  secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
}

// 测试数据
const testData = {
  method: 'POST',
  endpoint: '/query_coupon',
  body: '{"latitude":********,"longitude":*********,"pageNo":1,"pageSize":10}',
  timestamp: '*************' // 固定时间戳用于测试
}

console.log('1. 测试Content-MD5计算（按官方文档）:')
console.log('Body内容:', testData.body)

// 按官方文档计算Content-MD5: Base64.encodeBase64(MD5(bodyStream.getbytes("UTF-8")))
const utf8Bytes = Buffer.from(testData.body, 'utf8')
console.log('UTF-8字节长度:', utf8Bytes.length)

const md5Hash = crypto.md5Raw(utf8Bytes.toString('binary'))
console.log('MD5哈希:', md5Hash)

const md5Bytes = crypto.hexToBytes(md5Hash)
const contentMD5 = crypto.base64Encode(md5Bytes)
console.log('Content-MD5结果:', contentMD5)

console.log('\n2. 测试Headers构建（按官方文档）:')
// Headers按字典排序：S-Ca-App在S-Ca-Timestamp之前
const headers = `S-Ca-App:${config.appkey}\nS-Ca-Timestamp:${testData.timestamp}\n`
console.log('Headers字符串:')
console.log(headers.replace(/\n/g, '\\n'))

console.log('\n3. 测试URL构建（按官方文档）:')
// URL只是Path，不包含域名
const url = `/cps_open/common/api/v1${testData.endpoint}`
console.log('URL:', url)

console.log('\n4. 测试签名字符串构建（按官方文档）:')
// 格式：HTTPMethod + "\n" + Content-MD5 + "\n" + Headers + Url
const stringToSign = `${testData.method}\n${contentMD5}\n${headers}${url}`
console.log('签名字符串:')
console.log(stringToSign.replace(/\n/g, '\\n'))
console.log('签名字符串长度:', stringToSign.length)

console.log('\n5. 测试HMAC-SHA256计算（按官方文档）:')
const hmacResult = crypto.hmacSHA256(stringToSign, config.secret)
console.log('HMAC-SHA256结果:', hmacResult)
console.log('HMAC长度:', hmacResult.length)

console.log('\n6. 测试最终签名生成（按官方文档）:')
const signatureBytes = crypto.hexToBytes(hmacResult)
const finalSignature = crypto.base64Encode(signatureBytes)
console.log('最终签名:', finalSignature)
console.log('签名长度:', finalSignature.length)

console.log('\n7. 完整请求头测试:')
const requestHeaders = {
  'Content-Type': 'application/json;charset=utf-8',
  'S-Ca-App': config.appkey,
  'S-Ca-Timestamp': testData.timestamp,
  'S-Ca-Signature': finalSignature,
  'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
  'Content-MD5': contentMD5
}

console.log('请求头:')
Object.entries(requestHeaders).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`)
})

console.log('\n8. 官方文档要求验证:')
const validations = {
  'S-Ca-App存在': !!requestHeaders['S-Ca-App'],
  'S-Ca-Signature存在': !!requestHeaders['S-Ca-Signature'],
  'S-Ca-Timestamp存在': !!requestHeaders['S-Ca-Timestamp'],
  'Content-MD5存在': !!requestHeaders['Content-MD5'],
  'S-Ca-Signature-Headers存在': !!requestHeaders['S-Ca-Signature-Headers'],
  'S-Ca-Signature-Headers包含S-Ca-Timestamp': requestHeaders['S-Ca-Signature-Headers'].includes('S-Ca-Timestamp'),
  '时间戳格式正确': /^\d{13}$/.test(requestHeaders['S-Ca-Timestamp']),
  'Content-MD5格式正确': requestHeaders['Content-MD5'].length === 24,
  '签名格式正确': requestHeaders['S-Ca-Signature'].length === 44
}

Object.entries(validations).forEach(([check, result]) => {
  console.log(`  ${check}: ${result ? '✅' : '❌'}`)
})

console.log('\n=== 测试完成 ===')

// 使用API服务测试
console.log('\n9. 使用API服务测试:')
const apiService = require('../src/utils/api-service-fixed.js')

// 生成签名
const signatureResult = apiService.generateSignature('POST', '/query_coupon', {
  latitude: ********,
  longitude: *********,
  pageNo: 1,
  pageSize: 10
})

console.log('API服务生成的签名结果:')
console.log('- 时间戳:', signatureResult.timestamp)
console.log('- Content-MD5:', signatureResult.contentMD5)
console.log('- 签名:', signatureResult.signature)
console.log('- 签名长度:', signatureResult.signature.length)

const allValid = Object.values(validations).every(v => v)
console.log('\n总结:', allValid ? '✅ 所有验证通过' : '❌ 存在问题需要修复')
