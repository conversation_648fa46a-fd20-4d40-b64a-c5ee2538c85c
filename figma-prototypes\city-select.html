<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市选择 - 美团联盟原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #f5f5f5;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        /* 导航栏 */
        .city-navbar {
            background: #FF6600;
            height: 44px;
            display: flex;
            align-items: center;
            padding: 0 16px;
            color: white;
            position: relative;
        }

        .city-back-btn {
            font-size: 20px;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
        }

        .city-navbar-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 16px;
            font-weight: 500;
        }

        /* 当前定位区域 */
        .location-section {
            background: white;
            margin: 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .location-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
        }

        .location-city {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
        }

        .location-city-name {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            color: #333;
        }

        .location-icon {
            color: #FF6600;
            font-size: 16px;
        }

        .relocate-btn {
            color: #FF6600;
            font-size: 12px;
            background: transparent;
            border: none;
        }

        /* 搜索框 */
        .city-search {
            margin: 0 16px 16px;
            position: relative;
        }

        .city-search-input {
            width: 100%;
            height: 40px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 0 16px 0 40px;
            font-size: 14px;
        }

        .city-search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
        }

        /* 热门城市 */
        .hot-cities-section {
            background: white;
            margin: 0 16px 16px;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .hot-cities-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .hot-cities-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .hot-city-item {
            height: 40px;
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #333;
        }

        .hot-city-item.active {
            background: #FF6600;
            color: white;
            border-color: #FF6600;
        }

        /* 全部城市列表 */
        .all-cities-section {
            background: white;
            margin: 0 16px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .all-cities-title {
            padding: 16px;
            font-size: 16px;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
        }

        .city-group {
            border-bottom: 1px solid #e0e0e0;
        }

        .city-group:last-child {
            border-bottom: none;
        }

        .group-letter {
            background: #f5f5f5;
            padding: 8px 16px;
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .city-item {
            height: 44px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .city-item:last-child {
            border-bottom: none;
        }

        /* 字母索引 */
        .letter-index {
            position: fixed;
            right: calc(50% - 187.5px + 8px);
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 4px;
            padding: 4px;
            z-index: 100;
        }

        .letter-item {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            margin-bottom: 2px;
        }

        .letter-item:last-child {
            margin-bottom: 0;
        }

        .letter-item.active {
            background: #FF6600;
            border-radius: 2px;
        }

        .scrollable-content {
            height: calc(100vh - 44px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← 返回索引</button>
    
    <div class="phone-frame">
        <!-- 导航栏 -->
        <div class="city-navbar">
            <div class="city-back-btn">←</div>
            <div class="city-navbar-title">选择城市</div>
        </div>

        <div class="scrollable-content">
            <!-- 当前定位区域 -->
            <div class="location-section">
                <div class="location-title">当前位置</div>
                <div class="location-city">
                    <div class="location-city-name">
                        <span class="location-icon">📍</span>
                        <span>广州</span>
                    </div>
                    <button class="relocate-btn">重新定位</button>
                </div>
            </div>

            <!-- 搜索框 -->
            <div class="city-search">
                <input type="text" class="city-search-input" placeholder="请输入城市名称">
                <div class="city-search-icon">🔍</div>
            </div>

            <!-- 热门城市 -->
            <div class="hot-cities-section">
                <div class="hot-cities-title">热门城市</div>
                <div class="hot-cities-grid">
                    <div class="hot-city-item">北京</div>
                    <div class="hot-city-item">上海</div>
                    <div class="hot-city-item active">广州</div>
                    <div class="hot-city-item">深圳</div>
                    <div class="hot-city-item">杭州</div>
                    <div class="hot-city-item">南京</div>
                    <div class="hot-city-item">成都</div>
                    <div class="hot-city-item">武汉</div>
                    <div class="hot-city-item">西安</div>
                </div>
            </div>

            <!-- 全部城市列表 -->
            <div class="all-cities-section">
                <div class="all-cities-title">全部城市</div>
                
                <div class="city-group">
                    <div class="group-letter">A</div>
                    <div class="city-item">安庆</div>
                    <div class="city-item">鞍山</div>
                </div>

                <div class="city-group">
                    <div class="group-letter">B</div>
                    <div class="city-item">北京</div>
                    <div class="city-item">保定</div>
                    <div class="city-item">包头</div>
                </div>

                <div class="city-group">
                    <div class="group-letter">C</div>
                    <div class="city-item">长沙</div>
                    <div class="city-item">长春</div>
                    <div class="city-item">成都</div>
                    <div class="city-item">重庆</div>
                </div>

                <div class="city-group">
                    <div class="group-letter">G</div>
                    <div class="city-item">广州</div>
                    <div class="city-item">贵阳</div>
                </div>

                <div class="city-group">
                    <div class="group-letter">H</div>
                    <div class="city-item">杭州</div>
                    <div class="city-item">合肥</div>
                    <div class="city-item">哈尔滨</div>
                </div>
            </div>
        </div>

        <!-- 字母索引 -->
        <div class="letter-index">
            <div class="letter-item">A</div>
            <div class="letter-item">B</div>
            <div class="letter-item">C</div>
            <div class="letter-item">D</div>
            <div class="letter-item active">G</div>
            <div class="letter-item">H</div>
            <div class="letter-item">J</div>
            <div class="letter-item">K</div>
            <div class="letter-item">L</div>
            <div class="letter-item">M</div>
            <div class="letter-item">N</div>
            <div class="letter-item">S</div>
            <div class="letter-item">T</div>
            <div class="letter-item">W</div>
            <div class="letter-item">X</div>
            <div class="letter-item">Y</div>
            <div class="letter-item">Z</div>
        </div>
    </div>
</body>
</html>