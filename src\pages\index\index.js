const apiService = require('../../utils/meituan-api-service')
const cacheManager = require('../../utils/cacheManager')
const locationService = require('../../utils/locationService')

Page({
  data: {
    currentCity: '广州',
    searchKeyword: '',
    bannerList: [
      {
        id: 1,
        title: '新用户专享（下单返现）',
        subtitle: '活动时间：7月1日-7月8日',
        bgColor: '#FF6600',
        image: '/assets/images/banner1.png'
      }
    ],
    quickEntries: [
      { id: 1, name: '下午茶返现', icon: '/assets/icons/tea.png' },
      { id: 2, name: '今日秒杀', icon: '/assets/icons/seckill.png' },
      { id: 3, name: '神券包', icon: '/assets/icons/coupon.png' },
      { id: 4, name: '每日签到', icon: '/assets/icons/signin.png' },
      { id: 5, name: '今日免单', icon: '/assets/icons/free.png' }
    ],
    categories: [
      { id: 0, name: '为你推荐', active: true },
      { id: 1, name: '美食', active: false },
      { id: 2, name: '外卖券', active: false },
      { id: 3, name: '休闲娱乐', active: false },
      { id: 4, name: '零售', active: false }
    ],
    productList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 更新当前城市
    const currentCity = locationService.getCurrentCity()
    this.setData({ currentCity })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreProducts()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 初始化页面
  async initPage() {
    wx.showLoading({ title: '加载中...' })

    try {
      // 获取用户位置
      await this.getUserLocation()
      // 加载商品数据
      await this.loadProducts()
    } catch (error) {
      console.error('初始化页面失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 获取用户位置
  async getUserLocation() {
    try {
      await locationService.getUserLocation()
      const city = locationService.getCurrentCity()
      this.setData({ currentCity: city })
    } catch (error) {
      console.log('获取位置失败，使用默认城市')
    }
  },

  // 加载商品数据
  async loadProducts(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const page = refresh ? 1 : this.data.page

      // 调用API获取商品数据
      const response = await apiService.getRecommendProducts(page, this.data.pageSize)
      const products = this.formatProductData(response.data || [])

      if (refresh) {
        this.setData({
          productList: products,
          page: 2,
          hasMore: response.hasNext !== false && products.length >= this.data.pageSize
        })
      } else {
        this.setData({
          productList: [...this.data.productList, ...products],
          page: page + 1,
          hasMore: response.hasNext !== false && products.length >= this.data.pageSize
        })
      }
    } catch (error) {
      console.error('加载商品失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 格式化商品数据
  formatProductData(rawData) {
    if (!Array.isArray(rawData)) return []

    return rawData.map(item => {
      // 处理美团API返回的数据结构
      const couponDetail = item.couponPackDetail || {}
      const brandInfo = item.brandInfo || {}

      return {
        id: couponDetail.skuViewId || Math.random().toString(36).substr(2, 9),
        title: couponDetail.name || '美团优惠商品',
        image: couponDetail.headUrl || '/assets/images/placeholder.png',
        price: couponDetail.sellPrice || '19.9',
        originalPrice: couponDetail.originalPrice || '39.9',
        tags: ['优惠券'],
        shopName: brandInfo.brandName || '美团商家',
        sales: couponDetail.saleVolume || '月销100+',
        rating: '4.8',
        validTime: couponDetail.validTime,
        couponNum: couponDetail.couponNum
      }
    })
  },

  // 加载更多商品
  loadMoreProducts() {
    this.loadProducts(false)
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadProducts(true)
      wx.showToast({ title: '刷新成功', icon: 'success' })
    } catch (error) {
      wx.showToast({ title: '刷新失败', icon: 'none' })
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 城市改变事件
  onCityChange(e) {
    const { city, location } = e.detail
    this.setData({
      currentCity: city,
      productList: [],
      page: 1,
      hasMore: true
    })

    // 更新全局城市信息
    const app = getApp()
    app.globalData.currentCity = city
    if (location) {
      app.globalData.location = location
    }

    // 重新加载商品
    this.loadProducts(true)
  },

  // 点击城市选择
  onCityTap() {
    wx.navigateTo({
      url: '/pages/city/city'
    })
  },

  // 搜索事件
  onSearch(e) {
    const { keyword } = e.detail
    if (!keyword.trim()) return

    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
    })
  },

  // 点击搜索框
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 点击分类标签
  onCategoryTap(e) {
    const { index } = e.currentTarget.dataset
    const categories = this.data.categories.map((item, i) => ({
      ...item,
      active: i === index
    }))

    this.setData({
      categories,
      productList: [],
      page: 1,
      hasMore: true
    })

    // 重新加载对应分类的商品
    this.loadCategoryProducts(categories[index].id)
  },

  // 加载分类商品
  async loadCategoryProducts(categoryId) {
    this.setData({ loading: true })

    try {
      let response
      if (categoryId === 0) {
        // 推荐商品
        response = await apiService.getRecommendProducts(1, this.data.pageSize)
      } else {
        // 分类商品
        response = await apiService.getCategoryProducts(categoryId, 1, this.data.pageSize)
      }

      const products = this.formatProductData(response.data || [])

      this.setData({
        productList: products,
        page: 2,
        hasMore: response.hasNext !== false && products.length >= this.data.pageSize
      })
    } catch (error) {
      console.error('加载分类商品失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 点击商品
  onProductTap(e) {
    const { item } = e.detail
    const id = item.id || item.skuViewId
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 瀑布流加载更多
  onWaterfallLoadMore() {
    this.loadMoreProducts()
  },

  // 点击快速入口
  onQuickEntryTap(e) {
    const { id } = e.currentTarget.dataset
    wx.showToast({ title: '功能开发中', icon: 'none' })
  }
})