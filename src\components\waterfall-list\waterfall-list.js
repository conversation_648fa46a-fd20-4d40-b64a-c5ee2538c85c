/**
 * 瀑布流列表组件
 */
Component({
  properties: {
    items: {
      type: Array,
      value: []
    },
    columnCount: {
      type: Number,
      value: 2
    },
    columnGap: {
      type: Number,
      value: 16
    },
    loading: {
      type: Boolean,
      value: false
    },
    hasMore: {
      type: Boolean,
      value: true
    }
  },

  data: {
    columns: [],
    columnHeights: []
  },

  observers: {
    'items': function(items) {
      this.buildWaterfall(items)
    }
  },

  methods: {
    /**
     * 构建瀑布流布局
     */
    buildWaterfall(items) {
      if (!items || items.length === 0) {
        this.setData({
          columns: [],
          columnHeights: []
        })
        return
      }

      const columnCount = this.data.columnCount
      const columns = Array.from({ length: columnCount }, () => [])
      const columnHeights = Array.from({ length: columnCount }, () => 0)

      items.forEach((item, index) => {
        // 找到高度最小的列
        const minHeightIndex = columnHeights.indexOf(Math.min(...columnHeights))
        
        // 添加到该列
        columns[minHeightIndex].push({
          ...item,
          index
        })
        
        // 估算高度（实际项目中可以根据内容动态计算）
        const estimatedHeight = this.estimateItemHeight(item)
        columnHeights[minHeightIndex] += estimatedHeight
      })

      this.setData({
        columns,
        columnHeights
      })
    },

    /**
     * 估算商品卡片高度
     */
    estimateItemHeight(item) {
      // 基础高度：图片高度 + 内容区域高度
      let height = 240 // 图片高度

      // 标题高度（根据字数估算）
      const titleLines = Math.ceil((item.title || '').length / 15)
      height += Math.min(titleLines, 2) * 40 // 最多显示2行

      // 价格区域高度
      height += 50

      // 底部信息高度
      height += 40

      // 内边距
      height += 48

      return height
    },

    /**
     * 商品点击事件
     */
    onItemTap(e) {
      const { item } = e.currentTarget.dataset
      this.triggerEvent('itemtap', { 
        item: item,
        index: item.index 
      })
    },

    /**
     * 商品操作事件
     */
    onItemAction(e) {
      const { item } = e.currentTarget.dataset
      this.triggerEvent('itemaction', { 
        item: item,
        index: item.index 
      })
    },

    /**
     * 滚动到底部加载更多
     */
    onScrollToLower() {
      if (this.data.hasMore && !this.data.loading) {
        this.triggerEvent('loadmore')
      }
    },

    /**
     * 刷新数据
     */
    refresh() {
      this.setData({
        columns: [],
        columnHeights: []
      })
      this.buildWaterfall(this.data.items)
    }
  }
})