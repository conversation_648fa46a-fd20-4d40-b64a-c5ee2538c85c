<view class="product-card product-card--{{layout}}" bind:tap="onCardTap">
  <view class="product-image-wrapper">
    <image 
      class="product-image" 
      src="{{product.image || '/assets/images/placeholder.png'}}" 
      mode="aspectFill" 
      lazy-load
      bind:load="onImageLoad"
      bind:error="onImageError"
    />
    <view class="image-placeholder" wx:if="{{!imageLoaded && !imageError}}">
      <text class="placeholder-text">加载中...</text>
    </view>
    <view class="image-error" wx:if="{{imageError}}">
      <text class="error-text">图片加载失败</text>
    </view>
    
    <!-- 商品标签 -->
    <view class="product-badges" wx:if="{{showTags && product.tags && product.tags.length}}">
      <text class="product-badge" wx:for="{{product.tags}}" wx:key="*this">{{item}}</text>
    </view>
  </view>
  
  <view class="product-content">
    <text class="product-title">{{product.title || '商品标题'}}</text>
    
    <view class="product-price">
      <text class="price-current">¥{{formattedPrice || '0.00'}}</text>
      <text class="price-original" wx:if="{{formattedOriginalPrice}}">¥{{formattedOriginalPrice}}</text>
      <text class="price-discount" wx:if="{{product.discount}}">{{product.discount}}折</text>
    </view>
    
    <view class="product-footer">
      <view class="shop-info">
        <text class="shop-name">{{product.shopName || '商家名称'}}</text>
        <text class="shop-distance" wx:if="{{showDistance && formattedDistance}}">{{formattedDistance}}</text>
      </view>
      
      <view class="product-sales" wx:if="{{product.sales}}">
        <text class="sales-text">已售{{product.sales}}</text>
      </view>
    </view>
  </view>
  
  <!-- 列表模式下的操作按钮 -->
  <view class="product-action" wx:if="{{layout === 'list'}}">
    <button class="action-button" bind:tap="onActionTap">抢</button>
  </view>
</view>