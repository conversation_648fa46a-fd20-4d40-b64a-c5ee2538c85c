<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Page - Meituan Alliance Prototype</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #FF6600, #FF8533);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .city-selector {
            background: linear-gradient(135deg, #FF6600, #FF8533);
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .city-name {
            color: white;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .search-section {
            background: linear-gradient(135deg, #FF6600, #FF8533);
            padding: 0 16px 16px;
        }

        .search-bar {
            background: white;
            border-radius: 20px;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
            color: #999;
        }

        .search-button {
            background: #FFD100;
            color: #333;
            padding: 6px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            border: none;
        }

        /* Brand Promotion Section */
        .brand-promotion {
            background: white;
            margin: 16px;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .brand-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
        }

        .brand-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
        }

        .brand-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .kfc { background: #E4002B; }
        .mcdonalds { background: #FFC72C; color: #333; }
        .starbucks { background: #00704A; }
        .pizzahut { background: #EE3124; }
        .chayanyuese { background: #FF69B4; }

        .brand-name {
            font-size: 10px;
            color: #666;
            text-align: center;
        }

        /* Category Tabs */
        .category-section {
            background: white;
            margin: 0 16px 16px;
            border-radius: 8px;
            overflow: hidden;
        }

        .category-tabs {
            display: flex;
            padding: 12px 16px;
            overflow-x: auto;
            gap: 24px;
        }

        .category-tab {
            white-space: nowrap;
            font-size: 14px;
            color: #666;
            position: relative;
            padding-bottom: 8px;
        }

        .category-tab.active {
            color: #FF6600;
            font-weight: 500;
        }

        .category-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: #FF6600;
            border-radius: 1px;
        }

        /* Filter Bar */
        .filter-bar {
            background: white;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            height: 44px;
        }

        .filter-item {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 14px;
            color: #333;
            border-right: 1px solid #f0f0f0;
        }

        .filter-item:last-child {
            border-right: none;
        }

        .filter-item.active {
            color: #FF6600;
        }

        /* Product List */
        .product-list {
            padding: 16px;
        }

        .product-item {
            display: flex;
            background: white;
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .product-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .product-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .product-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .product-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-price {
            display: flex;
            align-items: baseline;
            gap: 6px;
        }

        .price-current {
            color: #FF3333;
            font-size: 16px;
            font-weight: bold;
        }

        .price-original {
            color: #999;
            font-size: 12px;
            text-decoration: line-through;
        }

        .product-distance {
            font-size: 12px;
            color: #999;
        }

        .grab-button {
            background: #FF3333;
            color: white;
            padding: 6px 16px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            margin-left: 12px;
        }

        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding-bottom: env(safe-area-inset-bottom);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #999;
            font-size: 10px;
        }

        .tab-item.active {
            color: #FF6600;
        }

        .tab-icon {
            font-size: 20px;
        }

        .scrollable-content {
            height: calc(100vh - 104px - 60px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← Back to Index</button>
    
    <div class="phone-frame">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-time">21:23</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <div class="scrollable-content">
            <!-- City Selector -->
            <div class="city-selector">
                <div class="city-name">
                    📍 Guangzhou
                    <div style="width: 0; height: 0; border-left: 4px solid transparent; border-right: 4px solid transparent; border-top: 4px solid white;"></div>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="search-section">
                <div class="search-bar">
                    <div style="color: #999; font-size: 16px;">🔍</div>
                    <input type="text" class="search-input" placeholder="Search main dishes...">
                    <button class="search-button">Search</button>
                </div>
            </div>

            <!-- Brand Promotion -->
            <div class="brand-promotion">
                <div class="brand-grid">
                    <div class="brand-item">
                        <div class="brand-logo kfc">🍗</div>
                        <div class="brand-name">KFC</div>
                    </div>
                    <div class="brand-item">
                        <div class="brand-logo mcdonalds">🍟</div>
                        <div class="brand-name">McDonald's</div>
                    </div>
                    <div class="brand-item">
                        <div class="brand-logo starbucks">☕</div>
                        <div class="brand-name">Starbucks</div>
                    </div>
                    <div class="brand-item">
                        <div class="brand-logo pizzahut">🍕</div>
                        <div class="brand-name">Pizza Hut</div>
                    </div>
                    <div class="brand-item">
                        <div class="brand-logo chayanyuese">🧋</div>
                        <div class="brand-name">ChayanyueseE</div>
                    </div>
                </div>
            </div>

            <!-- Category Tabs -->
            <div class="category-section">
                <div class="category-tabs">
                    <div class="category-tab active">All New</div>
                    <div class="category-tab">Food & Dining</div>
                    <div class="category-tab">Takeout</div>
                    <div class="category-tab">Entertainment</div>
                    <div class="category-tab">Beauty</div>
                </div>

                <!-- Filter Bar -->
                <div class="filter-bar">
                    <div class="filter-item active">All</div>
                    <div class="filter-item">Category</div>
                    <div class="filter-item">Smart Sort</div>
                </div>
            </div>

            <!-- Product List -->
            <div class="product-list">
                <div class="product-item">
                    <div class="product-image">Image</div>
                    <div class="product-info">
                        <div class="product-title">Premium Beef Hotpot Set for 2-3 People</div>
                        <div class="product-desc">Fresh beef slices, vegetables, and premium broth</div>
                        <div class="product-footer">
                            <div>
                                <div class="product-price">
                                    <span class="price-current">¥128</span>
                                    <span class="price-original">¥188</span>
                                </div>
                                <div class="product-distance">1.2km</div>
                            </div>
                            <button class="grab-button">Grab</button>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <div class="product-image">Image</div>
                    <div class="product-info">
                        <div class="product-title">Signature Fried Chicken Family Bucket</div>
                        <div class="product-desc">8 pieces chicken + fries + drinks</div>
                        <div class="product-footer">
                            <div>
                                <div class="product-price">
                                    <span class="price-current">¥89</span>
                                    <span class="price-original">¥128</span>
                                </div>
                                <div class="product-distance">800m</div>
                            </div>
                            <button class="grab-button">Grab</button>
                        </div>
                    </div>
                </div>

                <div class="product-item">
                    <div class="product-image">Image</div>
                    <div class="product-info">
                        <div class="product-title">Coffee & Pastry Morning Set</div>
                        <div class="product-desc">Americano + croissant + fresh fruit</div>
                        <div class="product-footer">
                            <div>
                                <div class="product-price">
                                    <span class="price-current">¥35</span>
                                    <span class="price-original">¥58</span>
                                </div>
                                <div class="product-distance">500m</div>
                            </div>
                            <button class="grab-button">Grab</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Tab Bar -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon">🏠</div>
                <div>Home</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon">🎯</div>
                <div>Activity</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">📤</div>
                <div>Share</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">👤</div>
                <div>Profile</div>
            </div>
        </div>
    </div>
</body>
</html>