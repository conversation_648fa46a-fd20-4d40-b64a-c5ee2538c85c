/* 自定义按钮组件样式 */
.custom-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  overflow: hidden;
}

.custom-button::after {
  border: none;
}

/* 按钮类型 */
.custom-button.primary {
  background-color: #FF6600;
  color: #ffffff;
  border-color: #FF6600;
}

.custom-button.primary:not(.disabled):active {
  background-color: #e55a00;
  border-color: #e55a00;
}

.custom-button.secondary {
  background-color: #f8f8f8;
  color: #333333;
  border-color: #e0e0e0;
}

.custom-button.secondary:not(.disabled):active {
  background-color: #e8e8e8;
}

.custom-button.outline {
  background-color: transparent;
  color: #FF6600;
  border-color: #FF6600;
}

.custom-button.outline:not(.disabled):active {
  background-color: #fff2e6;
}

.custom-button.text {
  background-color: transparent;
  color: #FF6600;
  border-color: transparent;
}

.custom-button.text:not(.disabled):active {
  background-color: #fff2e6;
}

/* 按钮大小 */
.custom-button.small {
  height: 56rpx;
  padding: 0 24rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
}

.custom-button.medium {
  height: 72rpx;
  padding: 0 32rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.custom-button.large {
  height: 88rpx;
  padding: 0 40rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
}

/* 圆角按钮 */
.custom-button.round.small {
  border-radius: 28rpx;
}

.custom-button.round.medium {
  border-radius: 36rpx;
}

.custom-button.round.large {
  border-radius: 44rpx;
}

/* 块级按钮 */
.custom-button.block {
  width: 100%;
  display: flex;
}

/* 禁用状态 */
.custom-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载状态 */
.custom-button.loading {
  cursor: not-allowed;
}

/* 按钮内容 */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.button-text {
  line-height: 1;
}

/* 按钮图标 */
.button-icon {
  width: 32rpx;
  height: 32rpx;
  flex-shrink: 0;
}

.button-icon.left {
  margin-right: 12rpx;
}

.button-icon.right {
  margin-left: 12rpx;
}

.custom-button.small .button-icon {
  width: 24rpx;
  height: 24rpx;
}

.custom-button.large .button-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 加载动画 */
.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #ffffff;
  border-radius: 50%;
  animation: buttonSpin 1s linear infinite;
}

.custom-button.small .loading-spinner {
  width: 24rpx;
  height: 24rpx;
  border-width: 2rpx;
}

.custom-button.large .loading-spinner {
  width: 36rpx;
  height: 36rpx;
  border-width: 4rpx;
}

/* 非主色按钮的加载动画颜色 */
.custom-button.secondary .loading-spinner,
.custom-button.outline .loading-spinner,
.custom-button.text .loading-spinner {
  border-color: rgba(255, 102, 0, 0.3);
  border-top-color: #FF6600;
}

@keyframes buttonSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
