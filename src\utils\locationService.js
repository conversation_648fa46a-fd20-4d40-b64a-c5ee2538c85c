class LocationService {
  constructor() {
    this.currentLocation = null
    this.currentCity = '广州'
    this.locationCache = null
    this.cacheExpiry = 5 * 60 * 1000 // 5分钟缓存
    
    // 热门城市列表
    this.hotCities = [
      { name: '北京', code: 'beijing' },
      { name: '上海', code: 'shanghai' },
      { name: '广州', code: 'guangzhou' },
      { name: '深圳', code: 'shenzhen' },
      { name: '杭州', code: 'hangzhou' },
      { name: '南京', code: 'nanjing' },
      { name: '成都', code: 'chengdu' },
      { name: '武汉', code: 'wuhan' },
      { name: '西安', code: 'xian' }
    ]
  }

  // 获取用户位置（带缓存）
  async getUserLocation() {
    return new Promise((resolve, reject) => {
      // 检查缓存
      if (this.locationCache && Date.now() - this.locationCache.timestamp < this.cacheExpiry) {
        resolve(this.locationCache.location)
        return
      }

      wx.getLocation({
        type: 'gcj02',
        altitude: false,
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            speed: res.speed
          }
          
          // 缓存位置信息
          this.locationCache = {
            location,
            timestamp: Date.now()
          }
          
          this.currentLocation = location
          resolve(location)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          reject(error)
        }
      })
    })
  }

  // 根据坐标获取城市（模拟地理编码）
  async getCityByCoordinates(latitude, longitude) {
    // 模拟地理编码API调用
    // 在实际项目中，这里应该调用腾讯地图或百度地图的逆地理编码API
    
    // 简单的城市坐标映射（仅用于演示）
    const cityCoordinates = {
      '北京': { lat: 39.9042, lng: 116.4074 },
      '上海': { lat: 31.2304, lng: 121.4737 },
      '广州': { lat: 23.1291, lng: 113.2644 },
      '深圳': { lat: 22.5431, lng: 114.0579 },
      '杭州': { lat: 30.2741, lng: 120.1551 },
      '南京': { lat: 32.0603, lng: 118.7969 },
      '成都': { lat: 30.5728, lng: 104.0668 },
      '武汉': { lat: 30.5928, lng: 114.3055 },
      '西安': { lat: 34.3416, lng: 108.9398 }
    }

    // 计算最近的城市
    let nearestCity = '广州'
    let minDistance = Infinity

    Object.entries(cityCoordinates).forEach(([city, coords]) => {
      const distance = this.calculateDistance(latitude, longitude, coords.lat, coords.lng)
      if (distance < minDistance) {
        minDistance = distance
        nearestCity = city
      }
    })

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return nearestCity
  }

  // 计算两点间距离（简化版）
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371 // 地球半径（公里）
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  // 设置当前城市
  setCurrentCity(city) {
    this.currentCity = city
    wx.setStorageSync('currentCity', city)
    
    // 触发城市切换事件
    wx.setStorageSync('cityChangeTime', Date.now())
    
    // 通知其他页面城市已切换
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.onCityChange && typeof page.onCityChange === 'function') {
        page.onCityChange(city)
      }
    })
  }

  // 获取当前城市
  getCurrentCity() {
    try {
      const storedCity = wx.getStorageSync('currentCity')
      return storedCity || this.currentCity
    } catch (error) {
      console.error('获取当前城市失败:', error)
      return this.currentCity
    }
  }

  // 获取热门城市列表
  getHotCities() {
    return [...this.hotCities]
  }

  // 获取所有城市列表（按字母分组）
  getAllCities() {
    const allCities = [
      // A
      { name: '安庆', code: 'anqing', letter: 'A' },
      { name: '安阳', code: 'anyang', letter: 'A' },
      { name: '鞍山', code: 'anshan', letter: 'A' },
      // B
      { name: '北京', code: 'beijing', letter: 'B' },
      { name: '保定', code: 'baoding', letter: 'B' },
      { name: '包头', code: 'baotou', letter: 'B' },
      { name: '本溪', code: 'benxi', letter: 'B' },
      { name: '蚌埠', code: 'bengbu', letter: 'B' },
      // C
      { name: '成都', code: 'chengdu', letter: 'C' },
      { name: '重庆', code: 'chongqing', letter: 'C' },
      { name: '长沙', code: 'changsha', letter: 'C' },
      { name: '长春', code: 'changchun', letter: 'C' },
      { name: '常州', code: 'changzhou', letter: 'C' },
      { name: '承德', code: 'chengde', letter: 'C' },
      // D
      { name: '大连', code: 'dalian', letter: 'D' },
      { name: '东莞', code: 'dongguan', letter: 'D' },
      { name: '大庆', code: 'daqing', letter: 'D' },
      { name: '丹东', code: 'dandong', letter: 'D' },
      // E
      { name: '鄂尔多斯', code: 'eerduosi', letter: 'E' },
      // F
      { name: '福州', code: 'fuzhou', letter: 'F' },
      { name: '佛山', code: 'foshan', letter: 'F' },
      { name: '抚顺', code: 'fushun', letter: 'F' },
      { name: '阜阳', code: 'fuyang', letter: 'F' },
      // G
      { name: '广州', code: 'guangzhou', letter: 'G' },
      { name: '贵阳', code: 'guiyang', letter: 'G' },
      { name: '桂林', code: 'guilin', letter: 'G' },
      { name: '赣州', code: 'ganzhou', letter: 'G' },
      // H
      { name: '杭州', code: 'hangzhou', letter: 'H' },
      { name: '哈尔滨', code: 'haerbin', letter: 'H' },
      { name: '合肥', code: 'hefei', letter: 'H' },
      { name: '呼和浩特', code: 'huhehaote', letter: 'H' },
      { name: '海口', code: 'haikou', letter: 'H' },
      { name: '惠州', code: 'huizhou', letter: 'H' },
      // J
      { name: '济南', code: 'jinan', letter: 'J' },
      { name: '金华', code: 'jinhua', letter: 'J' },
      { name: '嘉兴', code: 'jiaxing', letter: 'J' },
      { name: '江门', code: 'jiangmen', letter: 'J' },
      { name: '九江', code: 'jiujiang', letter: 'J' },
      // K
      { name: '昆明', code: 'kunming', letter: 'K' },
      { name: '开封', code: 'kaifeng', letter: 'K' },
      // L
      { name: '兰州', code: 'lanzhou', letter: 'L' },
      { name: '洛阳', code: 'luoyang', letter: 'L' },
      { name: '临沂', code: 'linyi', letter: 'L' },
      { name: '柳州', code: 'liuzhou', letter: 'L' },
      // M
      { name: '绵阳', code: 'mianyang', letter: 'M' },
      { name: '牡丹江', code: 'mudanjiang', letter: 'M' },
      // N
      { name: '南京', code: 'nanjing', letter: 'N' },
      { name: '南昌', code: 'nanchang', letter: 'N' },
      { name: '南宁', code: 'nanning', letter: 'N' },
      { name: '宁波', code: 'ningbo', letter: 'N' },
      { name: '南通', code: 'nantong', letter: 'N' },
      // P
      { name: '平顶山', code: 'pingdingshan', letter: 'P' },
      // Q
      { name: '青岛', code: 'qingdao', letter: 'Q' },
      { name: '泉州', code: 'quanzhou', letter: 'Q' },
      { name: '秦皇岛', code: 'qinhuangdao', letter: 'Q' },
      // R
      { name: '日照', code: 'rizhao', letter: 'R' },
      // S
      { name: '上海', code: 'shanghai', letter: 'S' },
      { name: '深圳', code: 'shenzhen', letter: 'S' },
      { name: '苏州', code: 'suzhou', letter: 'S' },
      { name: '沈阳', code: 'shenyang', letter: 'S' },
      { name: '石家庄', code: 'shijiazhuang', letter: 'S' },
      { name: '汕头', code: 'shantou', letter: 'S' },
      // T
      { name: '天津', code: 'tianjin', letter: 'T' },
      { name: '太原', code: 'taiyuan', letter: 'T' },
      { name: '唐山', code: 'tangshan', letter: 'T' },
      { name: '台州', code: 'taizhou', letter: 'T' },
      // W
      { name: '武汉', code: 'wuhan', letter: 'W' },
      { name: '无锡', code: 'wuxi', letter: 'W' },
      { name: '温州', code: 'wenzhou', letter: 'W' },
      { name: '潍坊', code: 'weifang', letter: 'W' },
      { name: '芜湖', code: 'wuhu', letter: 'W' },
      // X
      { name: '西安', code: 'xian', letter: 'X' },
      { name: '厦门', code: 'xiamen', letter: 'X' },
      { name: '徐州', code: 'xuzhou', letter: 'X' },
      { name: '襄阳', code: 'xiangyang', letter: 'X' },
      // Y
      { name: '银川', code: 'yinchuan', letter: 'Y' },
      { name: '烟台', code: 'yantai', letter: 'Y' },
      { name: '扬州', code: 'yangzhou', letter: 'Y' },
      { name: '宜昌', code: 'yichang', letter: 'Y' },
      // Z
      { name: '郑州', code: 'zhengzhou', letter: 'Z' },
      { name: '珠海', code: 'zhuhai', letter: 'Z' },
      { name: '中山', code: 'zhongshan', letter: 'Z' },
      { name: '淄博', code: 'zibo', letter: 'Z' },
      { name: '湛江', code: 'zhanjiang', letter: 'Z' }
    ]

    // 按字母分组
    const grouped = {}
    allCities.forEach(city => {
      if (!grouped[city.letter]) {
        grouped[city.letter] = []
      }
      grouped[city.letter].push(city)
    })

    // 对每个字母组内的城市按名称排序
    Object.keys(grouped).forEach(letter => {
      grouped[letter].sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
    })

    return grouped
  }

  // 搜索城市（优化搜索算法）
  searchCities(keyword) {
    if (!keyword || !keyword.trim()) {
      return []
    }

    const searchTerm = keyword.trim().toLowerCase()
    const allCities = Object.values(this.getAllCities()).flat()
    
    // 搜索结果按匹配度排序
    const results = allCities.filter(city => {
      const name = city.name.toLowerCase()
      const code = city.code.toLowerCase()
      
      return name.includes(searchTerm) || 
             code.includes(searchTerm) ||
             this.getPinyin(city.name).includes(searchTerm)
    })

    // 按匹配度排序：完全匹配 > 开头匹配 > 包含匹配
    return results.sort((a, b) => {
      const aName = a.name.toLowerCase()
      const bName = b.name.toLowerCase()
      
      // 完全匹配优先
      if (aName === searchTerm) return -1
      if (bName === searchTerm) return 1
      
      // 开头匹配优先
      if (aName.startsWith(searchTerm) && !bName.startsWith(searchTerm)) return -1
      if (bName.startsWith(searchTerm) && !aName.startsWith(searchTerm)) return 1
      
      // 按字母顺序排序
      return aName.localeCompare(bName, 'zh-CN')
    })
  }

  // 简单的拼音转换（仅用于搜索）
  getPinyin(chinese) {
    // 这里应该使用专业的拼音转换库
    // 为了演示，只提供几个常用城市的拼音映射
    const pinyinMap = {
      '北京': 'beijing',
      '上海': 'shanghai',
      '广州': 'guangzhou',
      '深圳': 'shenzhen',
      '杭州': 'hangzhou',
      '南京': 'nanjing',
      '成都': 'chengdu',
      '武汉': 'wuhan',
      '西安': 'xian'
    }
    
    return pinyinMap[chinese] || ''
  }

  // 清除位置缓存
  clearLocationCache() {
    this.locationCache = null
    this.currentLocation = null
  }

  // 检查位置权限状态
  async checkLocationPermission() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          const permission = res.authSetting['scope.userLocation']
          resolve({
            granted: permission === true,
            denied: permission === false,
            notDetermined: permission === undefined
          })
        },
        fail: () => {
          resolve({
            granted: false,
            denied: false,
            notDetermined: true
          })
        }
      })
    })
  }
}

module.exports = new LocationService()