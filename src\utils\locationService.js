class LocationService {
  constructor() {
    this.currentLocation = null
    this.currentCity = '广州'
    this.cities = [
      { name: '北京', code: 'beijing' },
      { name: '上海', code: 'shanghai' },
      { name: '广州', code: 'guangzhou' },
      { name: '深圳', code: 'shenzhen' },
      { name: '杭州', code: 'hangzhou' },
      { name: '南京', code: 'nanjing' },
      { name: '成都', code: 'chengdu' },
      { name: '武汉', code: 'wuhan' },
      { name: '西安', code: 'xian' }
    ]
  }

  // 获取用户位置
  async getUserLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.currentLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          resolve(this.currentLocation)
        },
        fail: (error) => {
          console.log('获取位置失败:', error)
          reject(error)
        }
      })
    })
  }

  // 根据坐标获取城市
  async getCityByCoordinates(latitude, longitude) {
    // 这里应该调用地理编码API
    // 暂时返回默认城市
    return '广州'
  }

  // 设置当前城市
  setCurrentCity(city) {
    this.currentCity = city
    wx.setStorageSync('currentCity', city)
  }

  // 获取当前城市
  getCurrentCity() {
    const storedCity = wx.getStorageSync('currentCity')
    return storedCity || this.currentCity
  }

  // 获取热门城市列表
  getHotCities() {
    return this.cities.slice(0, 9)
  }

  // 获取所有城市列表（按字母分组）
  getAllCities() {
    const allCities = [
      // A
      { name: '安庆', code: 'anqing', letter: 'A' },
      { name: '安阳', code: 'anyang', letter: 'A' },
      // B
      { name: '北京', code: 'beijing', letter: 'B' },
      { name: '保定', code: 'baoding', letter: 'B' },
      { name: '包头', code: 'baotou', letter: 'B' },
      // C
      { name: '成都', code: 'chengdu', letter: 'C' },
      { name: '重庆', code: 'chongqing', letter: 'C' },
      { name: '长沙', code: 'changsha', letter: 'C' },
      // D
      { name: '大连', code: 'dalian', letter: 'D' },
      { name: '东莞', code: 'dongguan', letter: 'D' },
      // F
      { name: '福州', code: 'fuzhou', letter: 'F' },
      { name: '佛山', code: 'foshan', letter: 'F' },
      // G
      { name: '广州', code: 'guangzhou', letter: 'G' },
      { name: '贵阳', code: 'guiyang', letter: 'G' },
      // H
      { name: '杭州', code: 'hangzhou', letter: 'H' },
      { name: '哈尔滨', code: 'haerbin', letter: 'H' },
      { name: '合肥', code: 'hefei', letter: 'H' },
      // J
      { name: '济南', code: 'jinan', letter: 'J' },
      { name: '金华', code: 'jinhua', letter: 'J' },
      // K
      { name: '昆明', code: 'kunming', letter: 'K' },
      // L
      { name: '兰州', code: 'lanzhou', letter: 'L' },
      // M
      { name: '绵阳', code: 'mianyang', letter: 'M' },
      // N
      { name: '南京', code: 'nanjing', letter: 'N' },
      { name: '南昌', code: 'nanchang', letter: 'N' },
      { name: '南宁', code: 'nanning', letter: 'N' },
      // Q
      { name: '青岛', code: 'qingdao', letter: 'Q' },
      // S
      { name: '上海', code: 'shanghai', letter: 'S' },
      { name: '深圳', code: 'shenzhen', letter: 'S' },
      { name: '苏州', code: 'suzhou', letter: 'S' },
      { name: '沈阳', code: 'shenyang', letter: 'S' },
      // T
      { name: '天津', code: 'tianjin', letter: 'T' },
      { name: '太原', code: 'taiyuan', letter: 'T' },
      // W
      { name: '武汉', code: 'wuhan', letter: 'W' },
      { name: '无锡', code: 'wuxi', letter: 'W' },
      { name: '温州', code: 'wenzhou', letter: 'W' },
      // X
      { name: '西安', code: 'xian', letter: 'X' },
      { name: '厦门', code: 'xiamen', letter: 'X' },
      // Y
      { name: '银川', code: 'yinchuan', letter: 'Y' },
      // Z
      { name: '郑州', code: 'zhengzhou', letter: 'Z' },
      { name: '珠海', code: 'zhuhai', letter: 'Z' }
    ]

    // 按字母分组
    const grouped = {}
    allCities.forEach(city => {
      if (!grouped[city.letter]) {
        grouped[city.letter] = []
      }
      grouped[city.letter].push(city)
    })

    return grouped
  }

  // 搜索城市
  searchCities(keyword) {
    const allCities = Object.values(this.getAllCities()).flat()
    return allCities.filter(city => 
      city.name.includes(keyword) || 
      city.code.includes(keyword.toLowerCase())
    )
  }
}

module.exports = new LocationService()