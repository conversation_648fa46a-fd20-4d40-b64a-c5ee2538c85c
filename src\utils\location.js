/**
 * 位置服务工具类
 * 处理地理位置相关功能
 */

class LocationService {
  constructor() {
    this.currentLocation = null
    this.currentCity = '广州'
  }

  /**
   * 获取当前位置
   */
  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.currentLocation = {
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy
          }
          resolve(this.currentLocation)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          reject(error)
        }
      })
    })
  }

  /**
   * 根据坐标获取城市信息
   */
  async getCityByLocation(latitude, longitude) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'your_tencent_map_key', // 需要申请腾讯地图API密钥
          get_poi: 0
        },
        success: (res) => {
          if (res.data.status === 0) {
            const addressComponent = res.data.result.address_component
            const city = addressComponent.city.replace('市', '')
            this.currentCity = city
            resolve({
              city,
              district: addressComponent.district,
              province: addressComponent.province,
              address: res.data.result.formatted_addresses.recommend
            })
          } else {
            reject(new Error('获取城市信息失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  /**
   * 计算两点间距离
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const radLat1 = lat1 * Math.PI / 180.0
    const radLat2 = lat2 * Math.PI / 180.0
    const a = radLat1 - radLat2
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0
    
    let s = 2 * Math.asin(Math.sqrt(
      Math.pow(Math.sin(a / 2), 2) + 
      Math.cos(radLat1) * Math.cos(radLat2) * 
      Math.pow(Math.sin(b / 2), 2)
    ))
    
    s = s * 6378.137 // 地球半径
    s = Math.round(s * 10000) / 10000
    
    return s
  }

  /**
   * 格式化距离显示
   */
  formatDistance(distance) {
    if (distance < 1) {
      return Math.round(distance * 1000) + 'm'
    } else {
      return distance.toFixed(1) + 'km'
    }
  }

  /**
   * 获取位置权限
   */
  async requestLocationPermission() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            resolve(true)
          } else {
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => resolve(true),
              fail: () => {
                wx.showModal({
                  title: '位置权限',
                  content: '需要获取您的位置信息来提供更好的服务',
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting({
                        success: (settingRes) => {
                          resolve(settingRes.authSetting['scope.userLocation'])
                        }
                      })
                    } else {
                      resolve(false)
                    }
                  }
                })
              }
            })
          }
        },
        fail: () => reject(false)
      })
    })
  }

  /**
   * 初始化位置服务
   */
  async init() {
    try {
      const hasPermission = await this.requestLocationPermission()
      if (hasPermission) {
        const location = await this.getCurrentLocation()
        const cityInfo = await this.getCityByLocation(location.latitude, location.longitude)
        return {
          location,
          cityInfo
        }
      } else {
        return {
          location: null,
          cityInfo: { city: this.currentCity }
        }
      }
    } catch (error) {
      console.error('初始化位置服务失败:', error)
      return {
        location: null,
        cityInfo: { city: this.currentCity }
      }
    }
  }
}

module.exports = new LocationService()