/**
 * 测试工具函数集合
 * 提供各种测试辅助功能
 */

// 生成随机字符串
function generateRandomString(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成测试数据
function generateTestData(type, options = {}) {
  switch (type) {
    case 'user':
      return {
        id: options.id || Math.floor(Math.random() * 10000),
        name: options.name || `测试用户_${generateRandomString(5)}`,
        avatar: options.avatar || 'https://example.com/avatar.png',
        phone: options.phone || `1${Math.floor(Math.random() * 10000000000)}`
      };
    case 'product':
      return {
        id: options.id || Math.floor(Math.random() * 10000),
        name: options.name || `测试商品_${generateRandomString(5)}`,
        price: options.price || Math.floor(Math.random() * 10000) / 100,
        image: options.image || 'https://example.com/product.png',
        description: options.description || '这是一个测试商品描述'
      };
    case 'order':
      return {
        id: options.id || `ORDER_${generateRandomString(8)}`,
        userId: options.userId || Math.floor(Math.random() * 10000),
        productId: options.productId || Math.floor(Math.random() * 10000),
        status: options.status || ['待付款', '已付款', '已发货', '已完成'][Math.floor(Math.random() * 4)],
        createTime: options.createTime || Date.now(),
        amount: options.amount || Math.floor(Math.random() * 100000) / 100
      };
    default:
      return {};
  }
}

// 模拟API请求延迟
function mockApiDelay(minDelay = 100, maxDelay = 500) {
  const delay = Math.floor(Math.random() * (maxDelay - minDelay)) + minDelay;
  return new Promise(resolve => setTimeout(resolve, delay));
}

// 模拟API响应
async function mockApiResponse(success = true, data = {}, options = {}) {
  await mockApiDelay(options.minDelay, options.maxDelay);
  
  if (success) {
    return {
      code: options.code || 0,
      message: options.message || '成功',
      data: data
    };
  } else {
    return {
      code: options.code || -1,
      message: options.message || '请求失败',
      data: null
    };
  }
}

// 验证对象结构
function validateObjectStructure(obj, structure) {
  if (!obj || typeof obj !== 'object') return false;
  
  for (const key in structure) {
    if (structure.hasOwnProperty(key)) {
      // 检查键是否存在
      if (!(key in obj)) return false;
      
      // 检查类型
      const expectedType = structure[key];
      if (typeof expectedType === 'string') {
        // 简单类型检查
        if (typeof obj[key] !== expectedType) return false;
      } else if (Array.isArray(expectedType)) {
        // 数组类型检查
        if (!Array.isArray(obj[key])) return false;
        // 如果数组不为空且有子结构，检查第一个元素
        if (expectedType.length > 0 && obj[key].length > 0) {
          if (!validateObjectStructure(obj[key][0], expectedType[0])) return false;
        }
      } else if (typeof expectedType === 'object') {
        // 嵌套对象检查
        if (!validateObjectStructure(obj[key], expectedType)) return false;
      }
    }
  }
  
  return true;
}

module.exports = {
  generateRandomString,
  generateTestData,
  mockApiDelay,
  mockApiResponse,
  validateObjectStructure
};