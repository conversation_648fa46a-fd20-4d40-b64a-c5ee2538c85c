/* 个人中心页面样式 */
.profile-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, #FF8800 100%);
  padding: 48rpx 32rpx;
  color: var(--white);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 登录提示 */
.login-prompt {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.8;
}

.login-info {
  flex: 1;
}

.login-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.login-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.login-btn::after {
  border: none;
}

/* 统计数据 */
.statistics-section {
  background: var(--white);
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 48rpx 0;
  display: flex;
  align-items: center;
  box-shadow: var(--shadow);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  color: var(--text-light);
}

.stat-divider {
  width: 2rpx;
  height: 80rpx;
  background: var(--border-color);
}

/* 功能菜单 */
.menu-section {
  background: var(--white);
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
}

.menu-title {
  font-size: 32rpx;
  color: var(--text-color);
}

.menu-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 其他功能 */
.other-section {
  background: var(--white);
  margin: 0 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.other-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.other-item:last-child {
  border-bottom: none;
}

.other-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.other-icon {
  width: 48rpx;
  height: 48rpx;
}

.other-title {
  font-size: 32rpx;
  color: var(--text-color);
}

.other-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.version-text {
  font-size: 28rpx;
  color: var(--text-lighter);
}

.other-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 底部空间 */
.bottom-space {
  height: 120rpx;
}