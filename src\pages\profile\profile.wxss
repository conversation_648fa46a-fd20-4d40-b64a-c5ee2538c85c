/* 个人中心页面样式 */
.profile-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, var(--meituan-yellow) 0%, #FFF200 100%);
  padding: 48rpx 32rpx;
  height: 240rpx;
  position: relative;
  box-sizing: border-box;
}

.logout-btn {
  position: absolute;
  top: 48rpx;
  right: 32rpx;
  color: var(--text-light);
  font-size: 28rpx;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: 32rpx;
}

.user-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.user-id {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.user-phone {
  font-size: 28rpx;
  color: var(--text-light);
}

/* 统计信息卡片 */
.statistics-section {
  background: var(--white);
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-card);
  height: 160rpx;
  box-sizing: border-box;
}

.stat-item {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
}

.stat-icon {
  width: 64rpx;
  height: 64rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

.stat-item:nth-child(1) .stat-number {
  color: #FF3333;
}

.stat-item:nth-child(2) .stat-number {
  color: var(--meituan-orange);
}

.stat-item:nth-child(3) .stat-number {
  color: #00AA90;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
  line-height: 1;
}

.stat-title {
  font-size: 24rpx;
  color: var(--text-light);
  line-height: 1;
}

/* 功能入口列表 */
.menu-section {
  background: var(--white);
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  height: 120rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid var(--border-color);
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}

.menu-icon {
  width: 64rpx;
  height: 64rpx;
}

.menu-text {
  flex: 1;
}

.menu-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 4rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: var(--text-light);
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

/* 版本信息区域 */
.version-section {
  text-align: center;
  padding: 40rpx 0;
  height: 80rpx;
  box-sizing: border-box;
}

.version-text {
  font-size: 24rpx;
  color: var(--text-lighter);
}

/* 底部空间 */
.bottom-space {
  height: 120rpx;
}