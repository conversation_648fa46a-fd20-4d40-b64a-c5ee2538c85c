/* 分享页面样式 */
.share-page {
  background: #F5F5F5;
  min-height: 100vh;
}

/* 艺术标题区域 */
.title-section {
  background: #FFFFFF;
  padding: 32rpx 0;
  text-align: center;
}

.art-title {
  font-size: 36rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #FFD100 0%, #FFF200 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2rpx 4rpx rgba(255, 209, 0, 0.3);
}

/* 搜索栏区域 */
.search-section {
  background: #FFFFFF;
  padding: 0 32rpx 24rpx;
}

.search-bar {
  position: relative;
  background: #F5F5F5;
  border-radius: 40rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.search-placeholder {
  color: #999999;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

.search-icon image {
  width: 100%;
  height: 100%;
}

/* 分类标签栏 */
.category-section {
  background: #FFFFFF;
  padding: 24rpx 0;
  margin-bottom: 16rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 0 32rpx;
  gap: 48rpx;
}

.category-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 0;
}

.category-name {
  font-size: 28rpx;
  color: #666666;
  transition: color 0.3s;
}

.category-item.active .category-name {
  color: #333333;
  font-weight: bold;
}

.category-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #FF3333;
  border-radius: 2rpx;
}

/* 内容标题 */
.content-title {
  background: #FFFFFF;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.content-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

/* 活动区域 */
.activities-section {
  background: #FFFFFF;
  padding: 0 32rpx 32rpx;
}

/* 瀑布流容器 */
.waterfall-container {
  display: flex;
  gap: 24rpx;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 活动卡片 */
.activity-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.activity-card:active {
  transform: scale(0.98);
}

.activity-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.activity-info {
  padding: 24rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.activity-discount {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF3333;
  margin-bottom: 16rpx;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.activity-expire {
  font-size: 24rpx;
  color: #666666;
}

.activity-popularity {
  font-size: 24rpx;
  color: #999999;
}

.share-button {
  background: #FF6600;
  color: #FFFFFF;
  text-align: center;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  transition: background-color 0.3s;
}

.share-button:active {
  background: #E55A00;
}

/* 加载和空状态 */
.loading,
.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999999;
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
}