/* 分享页面样式 */
.share-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 分类导航 */
.category-nav {
  background: var(--white);
  padding: 24rpx 0;
  margin-bottom: 16rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 0 24rpx;
  gap: 48rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.category-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: var(--bg-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon image {
  width: 40rpx;
  height: 40rpx;
}

.category-name {
  font-size: 24rpx;
  color: var(--text-color);
}

/* 推荐内容 */
.recommend-section {
  background: var(--white);
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 24rpx;
}

.recommend-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.recommend-item {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.recommend-info {
  padding: 24rpx;
  background: var(--white);
}

.recommend-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.recommend-desc {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 分享动态 */
.share-section {
  background: var(--white);
  padding: 32rpx 24rpx;
}

.share-list {
  margin-top: 24rpx;
}

.share-item {
  padding: 32rpx 0;
  border-bottom: 1rpx solid var(--border-color);
}

.share-item:last-child {
  border-bottom: none;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-nickname {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 4rpx;
}

.share-time {
  font-size: 24rpx;
  color: var(--text-lighter);
}

/* 分享内容 */
.share-content {
  margin-bottom: 24rpx;
}

.content-text {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
}

.content-images {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.content-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

/* 互动按钮 */
.interaction-bar {
  display: flex;
  gap: 48rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: var(--bg-color);
}

.interaction-item.liked {
  color: var(--primary-color);
}

.interaction-icon {
  width: 32rpx;
  height: 32rpx;
}

.interaction-text {
  font-size: 24rpx;
  color: var(--text-light);
}

.interaction-item.liked .interaction-text {
  color: var(--primary-color);
}

/* 加载和空状态 */
.loading,
.no-more {
  text-align: center;
  padding: 40rpx;
  color: var(--text-lighter);
  font-size: 28rpx;
}

.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: var(--text-lighter);
  font-size: 28rpx;
}