<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果 - 美团联盟原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        /* 导航栏 */
        .search-navbar {
            background: white;
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .back-icon {
            font-size: 20px;
            color: #333;
        }

        .search-input-wrapper {
            flex: 1;
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 36px;
            background: #f5f5f5;
            border: none;
            border-radius: 18px;
            padding: 0 16px 0 36px;
            font-size: 14px;
            color: #333;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
        }

        .search-button {
            background: #FF6600;
            color: white;
            border: none;
            padding: 0 16px;
            height: 36px;
            border-radius: 18px;
            font-size: 14px;
        }

        /* 搜索结果统计 */
        .result-stats {
            background: white;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .result-count {
            font-size: 12px;
            color: #666;
        }

        .result-count-number {
            color: #FF6600;
            font-weight: 500;
        }

        /* 筛选排序栏 */
        .filter-bar {
            background: white;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            height: 44px;
        }

        .filter-item {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 14px;
            color: #333;
            border-right: 1px solid #f0f0f0;
        }

        .filter-item:last-child {
            border-right: none;
        }

        .filter-item.active {
            color: #FF6600;
        }

        .filter-arrow {
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid currentColor;
            margin-top: 2px;
        }

        .filter-arrow.up {
            border-top: none;
            border-bottom: 4px solid currentColor;
        }

        /* 产品网格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            padding: 16px;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .product-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        .product-content {
            padding: 12px;
        }

        .product-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            line-height: 1.3;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            display: flex;
            align-items: baseline;
            gap: 6px;
            margin-bottom: 8px;
        }

        .price-current {
            color: #FF3333;
            font-size: 16px;
            font-weight: bold;
        }

        .price-original {
            color: #999;
            font-size: 12px;
            text-decoration: line-through;
        }

        .product-tags {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
        }

        .product-tag {
            background: #FF3333;
            color: white;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
        }

        .product-shop {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;
        }

        .shop-distance {
            color: #999;
        }

        /* 空状态 */
        .empty-state {
            display: none; /* 默认隐藏 */
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 48px 16px;
            text-align: center;
        }

        .empty-icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 16px;
        }

        .empty-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 8px;
        }

        .empty-desc {
            font-size: 14px;
            color: #999;
            margin-bottom: 24px;
        }

        .suggestion-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
            align-self: flex-start;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .suggestion-tag {
            background: white;
            border: 1px solid #e0e0e0;
            color: #333;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
        }

        .scrollable-content {
            height: calc(100vh - 44px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← 返回索引</button>
    
    <div class="phone-frame">
        <div class="scrollable-content">
            <!-- 搜索导航栏 -->
            <div class="search-navbar">
                <div class="back-icon">←</div>
                <div class="search-input-wrapper">
                    <div class="search-icon">🔍</div>
                    <input type="text" class="search-input" value="火锅">
                </div>
                <button class="search-button">搜索</button>
            </div>

            <!-- 搜索结果统计 -->
            <div class="result-stats">
                <div class="result-count">找到 <span class="result-count-number">123</span> 个相关商品</div>
            </div>

            <!-- 筛选排序栏 -->
            <div class="filter-bar">
                <div class="filter-item active">
                    <span>综合排序</span>
                    <span class="filter-arrow"></span>
                </div>
                <div class="filter-item">
                    <span>价格</span>
                    <span class="filter-arrow"></span>
                </div>
                <div class="filter-item">
                    <span>评分</span>
                    <span class="filter-arrow"></span>
                </div>
            </div>

            <!-- 产品网格 -->
            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">产品图片</div>
                    <div class="product-content">
                        <div class="product-title">海底捞4人火锅套餐，含锅底和特色菜品</div>
                        <div class="product-price">
                            <span class="price-current">¥198</span>
                            <span class="price-original">¥298</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">热门</span>
                        </div>
                        <div class="product-shop">
                            <span>海底捞火锅</span>
                            <span class="shop-distance">1.2km</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">产品图片</div>
                    <div class="product-content">
                        <div class="product-title">小龙坎麻辣火锅双人套餐</div>
                        <div class="product-price">
                            <span class="price-current">¥158</span>
                            <span class="price-original">¥228</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">特惠</span>
                        </div>
                        <div class="product-shop">
                            <span>小龙坎火锅</span>
                            <span class="shop-distance">800m</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">产品图片</div>
                    <div class="product-content">
                        <div class="product-title">巴蜀大将军火锅，特色牛油锅底</div>
                        <div class="product-price">
                            <span class="price-current">¥128</span>
                            <span class="price-original">¥168</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">新店</span>
                        </div>
                        <div class="product-shop">
                            <span>巴蜀大将军</span>
                            <span class="shop-distance">1.5km</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">产品图片</div>
                    <div class="product-content">
                        <div class="product-title">呷哺呷哺单人火锅套餐</div>
                        <div class="product-price">
                            <span class="price-current">¥68</span>
                            <span class="price-original">¥88</span>
                        </div>
                        <div class="product-tags">
                            <span class="product-tag">外卖</span>
                        </div>
                        <div class="product-shop">
                            <span>呷哺呷哺</span>
                            <span class="shop-distance">500m</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态（默认隐藏） -->
            <div class="empty-state">
                <div class="empty-icon">🔍</div>
                <div class="empty-title">未找到相关商品</div>
                <div class="empty-desc">换个关键词试试，或者看看以下推荐</div>
                <div class="suggestion-title">热门搜索</div>
                <div class="suggestion-tags">
                    <div class="suggestion-tag">火锅</div>
                    <div class="suggestion-tag">奶茶</div>
                    <div class="suggestion-tag">烤肉</div>
                    <div class="suggestion-tag">自助餐</div>
                    <div class="suggestion-tag">小吃</div>
                    <div class="suggestion-tag">甜品</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>