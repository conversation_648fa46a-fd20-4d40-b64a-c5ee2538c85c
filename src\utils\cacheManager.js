/**
 * 缓存管理器
 * 处理数据缓存、过期清理等功能
 */

class CacheManager {
  constructor() {
    this.prefix = 'meituan_cache_'
    this.defaultExpire = 5 * 60 * 1000 // 5分钟默认过期时间
  }

  /**
   * 生成缓存键
   */
  generateKey(type, params = {}) {
    const paramStr = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')
    return `${this.prefix}${type}_${paramStr}`
  }

  /**
   * 设置缓存
   */
  set(key, data, expire = this.defaultExpire) {
    try {
      const cacheData = {
        data,
        expireTime: Date.now() + expire,
        createTime: Date.now()
      }
      wx.setStorageSync(key, cacheData)
      return true
    } catch (error) {
      console.error('设置缓存失败:', error)
      return false
    }
  }

  /**
   * 获取缓存
   */
  get(key) {
    try {
      const cacheData = wx.getStorageSync(key)
      if (!cacheData) {
        return null
      }

      // 检查是否过期
      if (Date.now() > cacheData.expireTime) {
        this.remove(key)
        return null
      }

      return cacheData.data
    } catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  }

  /**
   * 删除缓存
   */
  remove(key) {
    try {
      wx.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('删除缓存失败:', error)
      return false
    }
  }

  /**
   * 清理所有缓存
   */
  clear() {
    try {
      const keys = wx.getStorageInfoSync().keys
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          wx.removeStorageSync(key)
        }
      })
      return true
    } catch (error) {
      console.error('清理缓存失败:', error)
      return false
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpired() {
    try {
      const keys = wx.getStorageInfoSync().keys
      const now = Date.now()
      
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          const cacheData = wx.getStorageSync(key)
          if (cacheData && cacheData.expireTime && now > cacheData.expireTime) {
            wx.removeStorageSync(key)
          }
        }
      })
      return true
    } catch (error) {
      console.error('清理过期缓存失败:', error)
      return false
    }
  }

  /**
   * 缓存商品数据
   */
  cacheProducts(key, products, expire = this.defaultExpire) {
    return this.set(key, products, expire)
  }

  /**
   * 获取商品缓存
   */
  getProducts(key) {
    return this.get(key)
  }

  /**
   * 缓存用户数据
   */
  cacheUserData(userData, expire = 24 * 60 * 60 * 1000) { // 24小时
    const key = `${this.prefix}user_data`
    return this.set(key, userData, expire)
  }

  /**
   * 获取用户数据缓存
   */
  getUserData() {
    const key = `${this.prefix}user_data`
    return this.get(key)
  }

  /**
   * 缓存城市数据
   */
  cacheCityData(cityData, expire = 7 * 24 * 60 * 60 * 1000) { // 7天
    const key = `${this.prefix}city_data`
    return this.set(key, cityData, expire)
  }

  /**
   * 获取城市数据缓存
   */
  getCityData() {
    const key = `${this.prefix}city_data`
    return this.get(key)
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    try {
      const info = wx.getStorageInfoSync()
      const cacheKeys = info.keys.filter(key => key.startsWith(this.prefix))
      
      return {
        totalKeys: cacheKeys.length,
        totalSize: info.currentSize,
        limitSize: info.limitSize,
        keys: cacheKeys
      }
    } catch (error) {
      console.error('获取缓存统计失败:', error)
      return null
    }
  }
}

module.exports = new CacheManager()