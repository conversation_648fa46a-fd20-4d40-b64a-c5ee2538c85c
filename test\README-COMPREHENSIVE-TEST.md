# Meituan API Comprehensive Signature Test

## Overview

This comprehensive test validates the complete Meituan API signature generation process according to the official documentation at: https://page.meituan.net/html/1687318722216_edeb3f/index.html

## Test Coverage

The test validates 7 critical components:

1. **🔧 Configuration Validation**
   - Validates AppKey format (32-character hexadecimal)
   - Validates Secret format (32-character hexadecimal)

2. **⏰ Timestamp Generation**
   - Generates 13-digit millisecond timestamp
   - Validates timestamp is within 2-minute validity window
   - Handles system time correction

3. **🔐 Content-MD5 Calculation**
   - Implements official spec: `Base64.encodeBase64(MD5(bodyStream.getbytes("UTF-8")))`
   - Validates 24-character Base64 output

4. **📝 Signature String Construction**
   - Implements official format: `HTTPMethod + "\n" + Content-MD5 + "\n" + Headers + Url`
   - Validates header dictionary sorting
   - Validates URL path construction

5. **🔑 HMAC-SHA256 Calculation**
   - Implements HmacSHA256 with Base64 encoding
   - Validates 44-character signature output

6. **📋 Request Header Construction**
   - Validates all required headers per official spec
   - Checks header formats and values

7. **🌐 Real API Call**
   - Makes actual API request to `/query_coupon` endpoint
   - Tests signature verification with Meituan servers

## Usage

### In Node.js Environment

```bash
cd test
node meituan-api-comprehensive-test.js
```

### In WeChat Mini Program

```javascript
const MeituanApiTest = require('./test/meituan-api-comprehensive-test.js')

const test = new MeituanApiTest()
test.runAllTests().then(success => {
  if (success) {
    console.log('✅ All tests passed - implementation is correct!')
  } else {
    console.log('❌ Some tests failed - check the detailed report')
  }
})
```

## Expected Output

### Successful Test Run

```
🚀 STARTING COMPREHENSIVE MEITUAN API SIGNATURE TEST
📖 Based on official documentation: https://page.meituan.net/html/1687318722216_edeb3f/index.html

🔧 STEP 1: API Configuration Validation
✅ Configuration validation PASSED

⏰ STEP 2: Timestamp Generation
✅ Timestamp generation PASSED

🔐 STEP 3: Content-MD5 Calculation
✅ Content-MD5 calculation PASSED

📝 STEP 4: Signature String Construction
✅ Signature string construction PASSED

🔑 STEP 5: HMAC-SHA256 Signature Calculation
✅ HMAC-SHA256 calculation PASSED

📋 STEP 6: Request Header Construction
✅ Header construction PASSED

🌐 STEP 7: Real API Call Test
✅ API call PASSED - Signature verification successful!

📊 COMPREHENSIVE TEST REPORT

=== TEST RESULTS SUMMARY ===
configValidation         : ✅ PASSED
timestampGeneration      : ✅ PASSED
contentMD5Calculation    : ✅ PASSED
signatureStringConstruction: ✅ PASSED
hmacSHA256Calculation    : ✅ PASSED
headerConstruction       : ✅ PASSED
apiCall                  : ✅ PASSED

=== OVERALL RESULT ===
🎉 ALL TESTS PASSED!
```

### Failed Test Run (Signature Verification Failed)

```
❌ API call FAILED
🔍 SIGNATURE VERIFICATION FAILED
This indicates either:
1. API credentials are incorrect
2. Account status issues  
3. Permission problems

⚠️ SOME TESTS FAILED

🌐 API CALL ISSUES:
- SIGNATURE VERIFICATION FAILED on server side
- This suggests:
  1. API credentials (AppKey/Secret) are incorrect
  2. Account not properly authenticated
  3. Application not approved
  4. Missing API permissions
- RECOMMENDED ACTION: Verify credentials in Meituan Union backend
```

## Interpreting Results

### If All Tests Pass
- ✅ Your technical implementation is 100% correct
- ✅ Signature generation follows official specification exactly
- ✅ API credentials are valid and working
- ✅ Account has proper permissions

### If Technical Tests Pass But API Call Fails
- ✅ Your code implementation is correct
- ❌ Issue is with API credentials or account status
- **Action Required**: 
  1. Verify AppKey/Secret in Meituan Union backend
  2. Check account authentication status
  3. Confirm application approval status
  4. Verify API permissions

### If Technical Tests Fail
- ❌ Implementation issues need to be fixed
- The test will provide specific guidance for each failed component
- Fix the technical issues before testing API credentials

## Troubleshooting

### Common Issues

1. **Configuration Format Errors**
   - Ensure AppKey and Secret are exactly 32 characters
   - Only use lowercase hexadecimal characters (0-9, a-f)

2. **Timestamp Issues**
   - System time may be incorrect
   - Test includes automatic time correction

3. **Signature Verification Failed**
   - Most likely cause: Incorrect API credentials
   - Check Meituan Union backend for latest credentials
   - Ensure account is properly authenticated

## API Credentials

The test uses the provided credentials:
- **AppKey**: `9498b0824d214ee4b65bfab1be6dbed0`
- **Secret**: `2a1463cb8f364cafbfd8d2a19c48eb48`

**Important**: If tests fail due to signature verification, these credentials may need to be updated from the Meituan Union backend.

## Support

If the comprehensive test shows all technical implementations are correct but API calls still fail, contact Meituan Union technical support with:

1. Your AppKey (safe to share)
2. The complete test report output
3. Your account verification status
4. Application approval status

This test provides definitive proof of whether the issue is technical implementation or account/credential related.
