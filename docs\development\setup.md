# 开发环境搭建指南

## 环境要求

### 必需软件
- **微信开发者工具**: 最新稳定版本
- **Node.js**: 14.0+ (用于构建工具和脚本)
- **Git**: 版本控制

### 可选工具
- **VS Code**: 代码编辑器，配合小程序插件使用
- **Postman**: API接口测试

## 项目初始化

### 1. 获取项目代码
```bash
git clone <repository-url>
cd meituan-miniprogram
```

### 2. 配置小程序AppID
1. 打开微信开发者工具
2. 导入项目，选择 `src` 目录
3. 输入你的小程序AppID
4. 如果没有AppID，可以选择"测试号"进行开发

### 3. 配置API凭证
编辑 `re.md` 文件，填入你的美团联盟API凭证：
```
媒体ID: your_media_id
appkey: your_appkey
签名密钥: your_secret
联盟ID: your_alliance_id
SID: your_sid
```

### 4. 配置域名白名单
在微信公众平台小程序管理后台配置以下域名：
- `https://media.meituan.com` (美团联盟API)
- `https://apis.map.qq.com` (腾讯地图API，可选)

## 开发流程

### 1. 启动开发
1. 打开微信开发者工具
2. 导入项目
3. 点击"编译"按钮
4. 在模拟器中预览效果

### 2. 真机调试
1. 点击"预览"按钮
2. 用微信扫描二维码
3. 在真机上测试功能

### 3. 代码规范
- 使用ES6+语法
- 遵循小程序开发规范
- 保持代码整洁和注释完整
- 使用统一的命名规范

## 目录结构说明

```
src/
├── app.js              # 小程序入口文件
├── app.json            # 小程序配置文件
├── app.wxss            # 全局样式文件
├── pages/              # 页面目录
│   ├── index/          # 首页
│   ├── activity/       # 活动页
│   ├── share/          # 分享页
│   ├── profile/        # 个人中心
│   ├── search/         # 搜索页
│   ├── city/           # 城市选择
│   └── detail/         # 商品详情
├── components/         # 组件目录
├── utils/              # 工具类目录
│   ├── apiService.js   # API服务
│   ├── cacheManager.js # 缓存管理
│   ├── crypto.js       # 加密工具
│   └── locationService.js # 位置服务
└── assets/             # 静态资源目录
    ├── images/         # 图片资源
    └── icons/          # 图标资源
```

## API集成说明

### 美团联盟API
- **基础URL**: `https://media.meituan.com/cps_open/common/api/v1`
- **认证方式**: HmacSHA256签名
- **请求方法**: POST
- **数据格式**: JSON

### 主要接口
1. **商品查询**: `/query_coupon`
2. **推广链接生成**: `/get_referral_link`

### 签名算法
```javascript
const stringToSign = `${method}\n${contentMD5}\n${headers}\n${url}`
const signature = crypto.HmacSHA256(stringToSign, secret).toString(crypto.enc.Base64)
```

## 调试技巧

### 1. 控制台调试
- 使用 `console.log()` 输出调试信息
- 在开发者工具中查看Console面板

### 2. 网络请求调试
- 在Network面板查看API请求
- 检查请求参数和响应数据

### 3. 存储调试
- 在Storage面板查看本地存储
- 清理缓存数据进行测试

### 4. 性能调试
- 使用Performance面板分析性能
- 优化页面渲染和数据加载

## 常见问题

### 1. API请求失败
- 检查域名是否已配置白名单
- 验证API凭证是否正确
- 确认签名算法实现正确

### 2. 页面跳转失败
- 检查页面路径是否正确
- 确认页面已在app.json中注册

### 3. 样式显示异常
- 检查CSS变量是否正确定义
- 确认rpx单位使用正确

### 4. 缓存问题
- 清理小程序缓存重新测试
- 检查缓存过期时间设置

## 发布流程

### 1. 代码检查
- 运行代码检查工具
- 确保没有语法错误
- 测试所有功能正常

### 2. 上传代码
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 上传成功后在微信公众平台查看

### 3. 提交审核
1. 登录微信公众平台
2. 进入版本管理页面
3. 提交审核并等待结果

### 4. 发布上线
- 审核通过后点击发布
- 用户即可在微信中使用小程序

## 性能优化建议

### 1. 代码优化
- 使用按需加载
- 避免过度渲染
- 合理使用缓存

### 2. 资源优化
- 压缩图片资源
- 使用CDN加速
- 启用懒加载

### 3. 用户体验
- 添加加载状态
- 优化错误提示
- 提供离线支持

## 技术支持

如遇到技术问题，可以：
1. 查看微信小程序官方文档
2. 参考美团联盟API文档
3. 在项目Issues中提问
4. 联系开发团队

---

*本文档会随着项目发展持续更新*