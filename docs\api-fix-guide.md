# 美团联盟API参数错误修复指南

## 🔍 问题分析

### 错误现象
```
API错误: 参数错误，请参考接口文档或联系相关负责人 
{code: 1, message: "参数错误，请参考接口文档或联系相关负责人", data: null}
```

### 错误原因
根据美团联盟API最新文档，商品查询接口 `/query_coupon` 必须包含以下四个参数中的**至少一个**：
- `vpSkuViewIds` - 商品ID列表
- `listTopiId` - 榜单ID
- `searchText` - 搜索关键词
- `multipleSupplyList` - 多供给列表

原代码中缺少这些必需参数，导致API返回参数错误。

## 🛠️ 修复内容

### 1. 修复 `queryCoupon` 方法

**修复前问题：**
- 缺少必需的查询参数
- 参数验证不完整
- 错误处理不够详细

**修复后改进：**
```javascript
async queryCoupon(latitude, longitude, options = {}) {
  try {
    const params = {
      latitude: Math.round(latitude * 1000000),
      longitude: Math.round(longitude * 1000000),
      pageNo: options.pageNo || 1,
      pageSize: options.pageSize || 20
    }

    // 确保包含必需参数之一
    if (options.vpSkuViewIds && Array.isArray(options.vpSkuViewIds) && options.vpSkuViewIds.length > 0) {
      params.vpSkuViewIds = options.vpSkuViewIds
    } else if (options.listTopiId) {
      params.listTopiId = options.listTopiId
    } else if (options.searchText && options.searchText.trim()) {
      params.searchText = options.searchText.trim()
    } else if (options.multipleSupplyList && Array.isArray(options.multipleSupplyList) && options.multipleSupplyList.length > 0) {
      params.multipleSupplyList = options.multipleSupplyList
    } else {
      // 默认使用热门榜单ID
      params.listTopiId = 'hot_sale'
    }

    console.log('查询商品券请求参数:', params)
    
    const response = await this.request('/query_coupon', params)
    console.log('查询商品券响应:', response)
    
    return response
  } catch (error) {
    console.error('查询商品券失败:', error)
    throw error
  }
}
```

### 2. 修复 `getRecommendProducts` 方法

**修复内容：**
- 确保正确传递 `listTopiId` 参数
- 添加详细的调试日志
- 优化缓存和错误处理

```javascript
async getRecommendProducts(page = 1, pageSize = 20) {
  try {
    const location = await this.getUserLocation()
    console.log('获取推荐商品 - 用户位置:', location)

    // 调用API - 使用热销榜单
    console.log('调用API获取推荐商品，参数:', { page, pageSize })
    const response = await this.queryCoupon(location.latitude, location.longitude, {
      pageNo: page,
      pageSize,
      listTopiId: 'hot_sale' // 确保使用正确的榜单ID
    })

    return response
  } catch (error) {
    console.error('获取推荐商品失败:', error)
    return this.getMockProducts(page, pageSize)
  }
}
```

### 3. 修复 `searchProducts` 方法

**修复内容：**
- 添加搜索关键词验证
- 确保 `searchText` 参数正确传递

```javascript
async searchProducts(keyword, page = 1, pageSize = 20) {
  try {
    if (!keyword || !keyword.trim()) {
      throw new Error('搜索关键词不能为空')
    }

    const location = await this.getUserLocation()
    console.log('搜索商品 - 关键词:', keyword, '位置:', location)

    const response = await this.queryCoupon(location.latitude, location.longitude, {
      pageNo: page,
      pageSize,
      searchText: keyword.trim() // 确保搜索关键词不为空
    })

    return response
  } catch (error) {
    console.error('搜索商品失败:', error)
    throw error
  }
}
```

### 4. 优化错误处理和调试

**改进内容：**
- 添加详细的API响应日志
- 针对不同错误码提供具体的错误提示
- 优化Content-MD5计算和签名生成

```javascript
success: (res) => {
  console.log('API响应状态码:', res.statusCode)
  console.log('API响应数据:', res.data)
  
  if (res.statusCode === 200) {
    if (res.data.code === 0) {
      console.log('API调用成功')
      resolve(res.data)
    } else {
      console.error(`API错误: ${res.data.message || '未知错误'}`, {
        code: res.data.code,
        message: res.data.message,
        data: res.data.data,
        endpoint,
        requestData
      })

      // 处理特定错误码
      if (res.data.code === 400) {
        if (res.data.message && res.data.message.includes('md5值不一致')) {
          console.error('MD5校验失败，请检查Content-MD5计算')
        } else if (res.data.message && res.data.message.includes('签名验证失败')) {
          console.error('签名验证失败，请检查签名算法')
        }
      } else if (res.data.code === 1) {
        console.error('参数错误，请检查API参数:', requestData)
      }

      reject(new Error(res.data.message || '请求失败'))
    }
  }
}
```

## 🧪 测试验证

### 运行测试
```bash
# 在微信开发者工具控制台中运行
node test/api-test.js
```

### 测试项目
1. **获取推荐商品测试** - 验证 `getRecommendProducts` 方法
2. **搜索商品测试** - 验证 `searchProducts` 方法  
3. **查询商品券测试** - 验证 `queryCoupon` 方法
4. **获取推广链接测试** - 验证 `getReferralLink` 方法

### 预期结果
- API调用成功，返回 `code: 0`
- 能够正常获取商品数据
- 错误处理机制正常工作
- 调试日志输出完整

## 📋 使用说明

### 1. 获取推荐商品
```javascript
const products = await apiService.getRecommendProducts(1, 20)
```

### 2. 搜索商品
```javascript
const searchResults = await apiService.searchProducts('奶茶', 1, 20)
```

### 3. 使用榜单获取商品
```javascript
const topProducts = await apiService.getTopListProducts('hot_sale', 1, 20)
```

### 4. 获取指定商品
```javascript
const specificProducts = await apiService.getSpecificProducts(['SKU123', 'SKU456'], 1, 20)
```

## ⚠️ 注意事项

1. **必需参数**: 调用 `queryCoupon` 时必须包含 `vpSkuViewIds`、`listTopiId`、`searchText`、`multipleSupplyList` 中的至少一个
2. **经纬度格式**: 需要乘以1000000传递（如39.928度 → 39928000）
3. **榜单ID**: 使用 `'hot_sale'` 作为默认热销榜单ID，实际使用时需要根据美团提供的榜单ID调整
4. **错误重试**: 系统会自动重试时间戳过期和服务器错误
5. **调试模式**: 开发阶段建议开启详细日志，生产环境可以关闭

## 🔗 相关文档

- [美团联盟API文档](docs/api/README.md)
- [API测试文件](test/api-test.js)
- [原始错误日志](docs/error-logs.md)