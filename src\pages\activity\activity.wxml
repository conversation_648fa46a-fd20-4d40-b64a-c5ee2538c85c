<!--活动页面-->
<view class="activity-page">
  <!-- 顶部区域 -->
  <view class="header">
    <view class="city-selector" bindtap="onCityTap">
      <text class="city-name">{{currentCity}}</text>
      <image class="city-arrow" src="/assets/icons/arrow-down.png" />
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-bar">
      <input class="search-input" 
             placeholder="搜主菜站" 
             placeholder-class="search-placeholder"
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             bindconfirm="onSearchConfirm" />
      <view class="search-btn" bindtap="onSearchTap">
        <text class="search-btn-text">搜索</text>
      </view>
    </view>
  </view>

  <!-- 品牌推广区域 -->
  <view class="brand-section">
    <view class="brand-list">
      <view class="brand-item" 
            wx:for="{{brandList}}" 
            wx:key="id"
            data-brand="{{item}}"
            bindtap="onBrandTap">
        <image class="brand-logo" src="{{item.logo}}" />
        <text class="brand-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 分类标签栏 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view class="category-item {{item.active ? 'active' : ''}}"
              wx:for="{{categories}}"
              wx:key="id"
              data-index="{{index}}"
              bindtap="onCategoryTap">
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 筛选功能区 -->
  <view class="filter-section">
    <view class="filter-list">
      <view class="filter-item" 
            wx:for="{{filterOptions}}" 
            wx:key="id"
            data-index="{{index}}"
            bindtap="onFilterTap">
        <text class="filter-name">{{item.name}}</text>
        <image class="filter-arrow" src="/assets/icons/arrow-down.png" />
      </view>
    </view>
  </view>
  <!-- 活动列表 -->
  <view class="activity-list">
    <view class="activity-item" 
          wx:for="{{activityList}}" 
          wx:key="id"
          data-id="{{item.id}}" 
          bindtap="onActivityTap">
      
      <!-- 左侧商品图片 -->
      <image class="activity-image" 
             src="{{item.image}}" 
             mode="aspectFill" 
             lazy-load />

      <!-- 右侧商品信息 -->
      <view class="activity-info">
        <text class="activity-title">{{item.title}}</text>
        <text class="activity-subtitle">{{item.subtitle}}</text>

        <!-- 价格信息 -->
        <view class="activity-price">
          <text class="price-current">¥{{item.price}}</text>
          <text class="price-original">¥{{item.originalPrice}}</text>
        </view>

        <!-- 优惠标签 -->
        <view class="activity-tags">
          <text class="tag tag-yellow" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>

        <!-- 商家信息 -->
        <view class="activity-meta">
          <text class="shop-name">{{item.shopName}}</text>
          <text class="distance">{{item.distance}}</text>
        </view>
      </view>

      <!-- 右侧抢购按钮 -->
      <view class="activity-action">
        <view class="grab-btn" data-id="{{item.id}}" bindtap="onBuyTap">
          <text class="grab-btn-text">抢</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="loading" wx:if="{{!hasMore && activityList.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && activityList.length === 0}}">
      <image class="empty-icon" src="/assets/icons/empty.png" />
      <text class="empty-text">暂无活动</text>
    </view>
  </view>
</view>