<!--活动页面-->
<view class="activity-page">
  <!-- 顶部筛选区域 -->
  <view class="filter-section">
    <!-- 城市选择 -->
    <city-selector
      current-city="{{currentCity}}"
      bind:change="onCityChange" />

    <!-- 分类筛选 -->
    <scroll-view class="category-filter" scroll-x>
      <view class="category-list">
        <view class="category-item {{item.active ? 'active' : ''}}"
              wx:for="{{categories}}"
              wx:key="id"
              data-index="{{index}}"
              bindtap="onCategoryTap">
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 排序筛选 -->
    <scroll-view class="sort-filter" scroll-x>
      <view class="sort-list">
        <view class="sort-item {{item.active ? 'active' : ''}}"
              wx:for="{{sortTypes}}"
              wx:key="id"
              data-index="{{index}}"
              bindtap="onSortTap">
          <text class="sort-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 活动列表 -->
  <view class="activity-list">
    <view class="activity-item"
          wx:for="{{activityList}}"
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="onActivityTap">

      <!-- 活动图片 -->
      <view class="activity-image-wrapper">
        <image class="activity-image"
               src="{{item.image}}"
               mode="aspectFill"
               lazy-load />

        <!-- 活动标签 -->
        <view class="activity-badges">
          <text class="activity-badge" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>
      </view>

      <!-- 活动信息 -->
      <view class="activity-info">
        <text class="activity-title">{{item.title}}</text>
        <text class="activity-subtitle">{{item.subtitle}}</text>

        <!-- 价格信息 -->
        <view class="activity-price">
          <text class="price-current">¥{{item.price}}</text>
          <text class="price-original">¥{{item.originalPrice}}</text>
          <text class="price-discount">{{item.discount}}折</text>
        </view>

        <!-- 商家信息 -->
        <view class="activity-shop">
          <text class="shop-name">{{item.shopName}}</text>
          <text class="shop-distance">{{item.distance}}</text>
          <text class="shop-sales">{{item.sales}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="activity-action">
        <custom-button
          text="抢"
          type="primary"
          size="small"
          data-id="{{item.id}}"
          bind:tap="onBuyTap" />
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <custom-loading
    wx:if="{{loading}}"
    loading="{{loading}}"
    text="加载中..."
    type="spinner" />

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!hasMore && activityList.length > 0}}">
    <text class="no-more-text">没有更多了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && activityList.length === 0}}">
    <image class="empty-icon" src="/assets/icons/empty.png" />
    <text class="empty-text">暂无活动</text>
  </view>
</view>
<view class="activity-page">
  <!-- 顶部区域 -->
  <view class="header">
    <view class="city-selector" bindtap="onCityTap">
      <text class="city-name">{{currentCity}}</text>
      <image class="city-arrow" src="/assets/icons/arrow-down.png" />
    </view>
    <text class="page-title">精选活动</text>
  </view>

  <!-- 分类筛选 -->
  <view class="filter-section">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view class="category-item {{item.active ? 'active' : ''}}"
              wx:for="{{categories}}" wx:key="id"
              data-index="{{index}}" bindtap="onCategoryTap">
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 排序选项 -->
  <view class="sort-section">
    <scroll-view class="sort-scroll" scroll-x>
      <view class="sort-list">
        <view class="sort-item {{item.active ? 'active' : ''}}"
              wx:for="{{sortTypes}}" wx:key="id"
              data-index="{{index}}" bindtap="onSortTap">
          <text class="sort-name">{{item.name}}</text>
          <image class="sort-arrow" src="/assets/icons/arrow-down.png" wx:if="{{item.active}}" />
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 活动列表 -->
  <view class="activity-list">
    <view class="activity-item" wx:for="{{activityList}}" wx:key="id"
          data-id="{{item.id}}" bindtap="onActivityTap">
      <image class="activity-image" src="{{item.image}}" mode="aspectFill" lazy-load />

      <view class="activity-info">
        <view class="activity-header">
          <text class="activity-title">{{item.title}}</text>
          <text class="activity-subtitle">{{item.subtitle}}</text>
        </view>

        <view class="activity-price">
          <text class="price">¥{{item.price}}</text>
          <text class="price-original">¥{{item.originalPrice}}</text>
          <text class="discount">{{item.discount}}折</text>
        </view>

        <view class="activity-tags">
          <text class="tag tag-red" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
        </view>

        <view class="activity-meta">
          <text class="shop-name">{{item.shopName}}</text>
          <text class="distance">{{item.distance}}</text>
          <text class="sales">{{item.sales}}</text>
          <text class="rating">{{item.rating}}分</text>
        </view>

        <view class="activity-footer">
          <text class="end-time">剩余时间: 2天3小时</text>
          <view class="buy-btn" data-id="{{item.id}}" bindtap="onBuyTap">
            立即抢购
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="loading" wx:if="{{!hasMore && activityList.length > 0}}">
      <text>没有更多了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && activityList.length === 0}}">
      <image class="empty-icon" src="/assets/icons/empty.png" />
      <text class="empty-text">暂无活动</text>
    </view>
  </view>
</view>