.product-card {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  position: relative;
  transition: all 0.3s ease;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 209, 0, 0.05), rgba(255, 102, 0, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.product-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.16);
}

.product-card:active::before {
  opacity: 1;
}

.product-card--waterfall {
  margin-bottom: 24rpx;
}

.product-card--list {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

.product-image-wrapper {
  position: relative;
  overflow: hidden;
}

.product-card--waterfall .product-image-wrapper {
  width: 100%;
  height: 240rpx;
}

.product-card--list .product-image-wrapper {
  width: 160rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:active .product-image {
  transform: scale(1.02);
}

.image-placeholder,
.image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.placeholder-text,
.error-text {
  font-size: 24rpx;
  color: var(--text-lighter);
}

.product-badges {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-badge {
  background: linear-gradient(135deg, #FF6600, #FF8C42);
  color: var(--white);
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.product-content {
  padding: 24rpx;
  flex: 1;
  position: relative;
  z-index: 1;
}

.product-card--list .product-content {
  padding: 0;
}

.product-title {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.product-card--list .product-title {
  -webkit-line-clamp: 1;
  margin-bottom: 12rpx;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.product-card--list .product-price {
  margin-bottom: 12rpx;
}

.price-current {
  font-size: 32rpx;
  color: #FF3333;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 51, 51, 0.2);
}

.product-card--list .price-current {
  font-size: 28rpx;
}

.price-original {
  font-size: 24rpx;
  color: var(--text-lighter);
  text-decoration: line-through;
}

.price-discount {
  background: #FF3333;
  color: var(--white);
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.product-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.shop-name {
  font-size: 24rpx;
  color: var(--text-light);
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.shop-distance {
  font-size: 22rpx;
  color: var(--text-lighter);
}

.product-sales {
  font-size: 22rpx;
  color: var(--text-lighter);
}

.product-action {
  padding: 0 24rpx;
}

.action-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.action-button::after {
  border: none;
}

/* 瀑布流特殊样式 */
.product-card--waterfall:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* 列表模式特殊样式 */
.product-card--list:active {
  background: #f8f8f8;
}