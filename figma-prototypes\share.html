<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享页面 - 美团联盟原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #FF6600, #FF8533);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        /* 艺术标题 */
        .page-header {
            background: linear-gradient(135deg, #FFD100, #FFA500);
            padding: 20px 16px;
            text-align: center;
        }

        .artistic-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            font-family: 'Arial Black', Arial, sans-serif;
            letter-spacing: 1px;
        }

        /* 搜索区域 */
        .search-section {
            background: white;
            padding: 16px;
            margin: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-bar {
            background: #f5f5f5;
            border-radius: 20px;
            padding: 8px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            font-size: 14px;
            color: #999;
        }

        /* 分类标签 */
        .category-tabs {
            display: flex;
            padding: 12px 16px;
            background: white;
            margin: 0 16px 16px;
            border-radius: 8px;
            overflow-x: auto;
            gap: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .category-tab {
            white-space: nowrap;
            font-size: 14px;
            color: #666;
            position: relative;
            padding-bottom: 8px;
        }

        .category-tab.active {
            color: #FF3333;
            font-weight: 500;
        }

        .category-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: #FF3333;
            border-radius: 1px;
        }

        /* 推荐区域 */
        .recommended-section {
            background: white;
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .recommended-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .recommended-item {
            background: #f8f8f8;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .recommended-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .recommended-title {
            font-size: 12px;
            color: #333;
            font-weight: 500;
        }

        .recommended-desc {
            font-size: 10px;
            color: #666;
            margin-top: 4px;
        }

        /* 活动流 */
        .activity-feed {
            padding: 0 16px;
        }

        .activity-card {
            background: white;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .activity-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        .activity-content {
            padding: 12px;
        }

        .activity-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .activity-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .activity-discount {
            color: #FF3333;
            font-size: 16px;
            font-weight: bold;
        }

        .activity-expire {
            font-size: 12px;
            color: #666;
        }

        .activity-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .activity-shares {
            font-size: 12px;
            color: #999;
        }

        .share-button {
            background: #FF6600;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            border: none;
        }

        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 60px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding-bottom: env(safe-area-inset-bottom);
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: #999;
            font-size: 10px;
        }

        .tab-item.active {
            color: #FF6600;
        }

        .tab-icon {
            font-size: 20px;
        }

        .scrollable-content {
            height: calc(100vh - 44px - 60px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.history.back()">← 返回索引</button>
    
    <div class="phone-frame">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-time">21:23</div>
            <div class="status-icons">
                <span>📶</span>
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <div class="scrollable-content">
            <!-- 艺术标题 -->
            <div class="page-header">
                <div class="artistic-title">分享活动</div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-bar">
                    <div style="color: #999; font-size: 16px;">🔍</div>
                    <input type="text" class="search-input" placeholder="搜索活动...">
                </div>
            </div>

            <!-- 分类标签 -->
            <div class="category-tabs">
                <div class="category-tab active">推荐</div>
                <div class="category-tab">美食</div>
                <div class="category-tab">外卖</div>
                <div class="category-tab">娱乐</div>
                <div class="category-tab">零售</div>
            </div>

            <!-- 推荐区域 -->
            <div class="recommended-section">
                <div class="section-title">热门推荐</div>
                <div class="recommended-grid">
                    <div class="recommended-item">
                        <div class="recommended-icon">🍜</div>
                        <div class="recommended-title">美食特惠</div>
                        <div class="recommended-desc">最高立减50元</div>
                    </div>
                    <div class="recommended-item">
                        <div class="recommended-icon">🎬</div>
                        <div class="recommended-title">电影票半价</div>
                        <div class="recommended-desc">周末特惠</div>
                    </div>
                    <div class="recommended-item">
                        <div class="recommended-icon">🏨</div>
                        <div class="recommended-title">酒店折扣</div>
                        <div class="recommended-desc">低至7折</div>
                    </div>
                    <div class="recommended-item">
                        <div class="recommended-icon">🛍️</div>
                        <div class="recommended-title">购物返现</div>
                        <div class="recommended-desc">最高返20%</div>
                    </div>
                </div>
            </div>

            <!-- 活动流 -->
            <div class="activity-feed">
                <div class="activity-card">
                    <div class="activity-image">活动图片</div>
                    <div class="activity-content">
                        <div class="activity-title">肯德基双人套餐特惠，原味鸡+汉堡+饮料</div>
                        <div class="activity-meta">
                            <div class="activity-discount">立减¥30</div>
                            <div class="activity-expire">有效期至7月31日</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-shares">已分享 2,345 次</div>
                            <button class="share-button">分享</button>
                        </div>
                    </div>
                </div>

                <div class="activity-card">
                    <div class="activity-image">活动图片</div>
                    <div class="activity-content">
                        <div class="activity-title">星巴克买一送一，周末限时特惠</div>
                        <div class="activity-meta">
                            <div class="activity-discount">省¥25+</div>
                            <div class="activity-expire">本周末有效</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-shares">已分享 1,892 次</div>
                            <button class="share-button">分享</button>
                        </div>
                    </div>
                </div>

                <div class="activity-card">
                    <div class="activity-image">活动图片</div>
                    <div class="activity-content">
                        <div class="activity-title">海底捞4人套餐，赠送小吃拼盘</div>
                        <div class="activity-meta">
                            <div class="activity-discount">立减¥50</div>
                            <div class="activity-expire">有效期至8月15日</div>
                        </div>
                        <div class="activity-stats">
                            <div class="activity-shares">已分享 3,210 次</div>
                            <button class="share-button">分享</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="tab-icon">🏠</div>
                <div>首页</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">🎯</div>
                <div>活动</div>
            </div>
            <div class="tab-item active">
                <div class="tab-icon">📤</div>
                <div>分享</div>
            </div>
            <div class="tab-item">
                <div class="tab-icon">👤</div>
                <div>我的</div>
            </div>
        </div>
    </div>
</body>
</html>