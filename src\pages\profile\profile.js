// 个人中心页面
const app = getApp()

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    statistics: {
      redPackets: 0,
      vouchers: 0,
      cashback: 0
    },
    menuList: [
      {
        id: 1,
        title: '订单中心',
        icon: 'order',
        url: '/pages/order/order'
      },
      {
        id: 2,
        title: '我的收藏',
        icon: 'favorite',
        url: '/pages/favorite/favorite'
      },
      {
        id: 3,
        title: '优惠券',
        icon: 'coupon',
        url: '/pages/coupon/coupon'
      },
      {
        id: 4,
        title: '邀请好友',
        icon: 'invite',
        url: '/pages/invite/invite'
      },
      {
        id: 5,
        title: '客服中心',
        icon: 'service',
        url: '/pages/service/service'
      },
      {
        id: 6,
        title: '设置',
        icon: 'setting',
        url: '/pages/setting/setting'
      },
      {
        id: 7,
        title: '功能测试',
        icon: 'test',
        url: '/pages/test/test'
      }
    ],
    version: '1.0.0'
  },

  onLoad() {
    this.getUserInfo()
    this.loadStatistics()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadStatistics()
  },

  // 获取用户信息
  getUserInfo() {
    const userInfo = app.globalData.userInfo
    if (userInfo) {
      this.setData({
        userInfo,
        hasUserInfo: true
      })
    } else {
      // 尝试从缓存获取
      const cachedUserInfo = wx.getStorageSync('userInfo')
      if (cachedUserInfo) {
        this.setData({
          userInfo: cachedUserInfo,
          hasUserInfo: true
        })
        app.globalData.userInfo = cachedUserInfo
      }
    }
  },

  // 用户登录
  onLoginTap() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo
        this.setData({
          userInfo,
          hasUserInfo: true
        })

        // 保存到全局和缓存
        app.globalData.userInfo = userInfo
        wx.setStorage({
          key: 'userInfo',
          data: userInfo
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 加载统计数据
  async loadStatistics() {
    try {
      // 从缓存获取统计数据
      const cachedStats = wx.getStorageSync('userStatistics')
      if (cachedStats) {
        this.setData({
          statistics: cachedStats
        })
      }

      // 模拟统计数据
      const mockStats = {
        redPackets: Math.floor(Math.random() * 50),
        vouchers: Math.floor(Math.random() * 100),
        cashback: (Math.random() * 500).toFixed(2)
      }

      this.setData({
        statistics: mockStats
      })

      // 缓存统计数据
      wx.setStorage({
        key: 'userStatistics',
        data: mockStats
      })
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  // 点击菜单项
  onMenuTap(e) {
    const { item } = e.currentTarget.dataset
    console.log('点击菜单:', item)

    if (item.url) {
      // 检查页面是否存在
      if (this.isPageExists(item.url)) {
        wx.navigateTo({
          url: item.url
        })
      } else {
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
      }
    }
  },

  // 检查页面是否存在
  isPageExists(url) {
    const existingPages = [
      '/pages/order/order',
      '/pages/favorite/favorite',
      '/pages/coupon/coupon'
    ]
    return existingPages.includes(url)
  },

  // 点击统计项
  onStatTap(e) {
    const { type } = e.currentTarget.dataset
    console.log('点击统计:', type)

    let url = ''
    switch (type) {
      case 'redPackets':
        url = '/pages/redpacket/redpacket'
        break
      case 'vouchers':
        url = '/pages/voucher/voucher'
        break
      case 'cashback':
        url = '/pages/cashback/cashback'
        break
    }

    if (url) {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: '美团优惠大全',
      desc: '精选美团优惠券，吃喝玩乐全都有',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-logo.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '美团优惠大全 - 精选优惠券',
      imageUrl: '/assets/images/share-logo.png'
    }
  },

  // 关于我们
  onAboutTap() {
    wx.showModal({
      title: '关于我们',
      content: `美团优惠大全 v${this.data.version}\n\n为您提供最新最全的美团优惠信息\n让您享受更多实惠`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 意见反馈
  onFeedbackTap() {
    wx.showToast({
      title: '反馈功能开发中',
      icon: 'none'
    })
  }
})