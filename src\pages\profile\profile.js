// 个人中心页面
const app = getApp()

Page({
  data: {
    userInfo: {
      userId: 'AGx424069683',
      phone: '186****7573'
    },
    statistics: {
      redPackets: 28,
      vouchers: 6,
      cashback: '0.0'
    },
    version: '1.0.0'
  },

  onLoad() {
    this.loadUserData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadUserData()
  },

  // 加载用户数据
  loadUserData() {
    try {
      // 从缓存获取用户统计数据
      const cachedStats = wx.getStorageSync('userStatistics')
      if (cachedStats) {
        this.setData({
          statistics: cachedStats
        })
      } else {
        // 使用默认数据
        this.setData({
          statistics: {
            redPackets: 28,
            vouchers: 6,
            cashback: '0.0'
          }
        })
      }
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  },

  // 退出登录
  onLogoutTap() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户数据
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('userStatistics')
          
          // 清除全局数据
          if (app.globalData) {
            app.globalData.userInfo = null
          }
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
          
          // 可以跳转到登录页面或刷新当前页面
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/index/index'
            })
          }, 1500)
        }
      }
    })
  },

  // 点击统计项 - 跳转到美团小程序
  onStatTap(e) {
    const { type } = e.currentTarget.dataset
    console.log('点击统计:', type)

    let message = ''
    switch (type) {
      case 'redPackets':
        message = '即将跳转到美团小程序查看红包/神券'
        break
      case 'vouchers':
        message = '即将跳转到美团小程序查看代金券'
        break
      case 'cashback':
        message = '返现功能开发中'
        break
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })

    // 模拟跳转到美团小程序
    if (type !== 'cashback') {
      setTimeout(() => {
        wx.navigateToMiniProgram({
          appId: 'wxde8ac0a21135c07d', // 美团小程序AppID
          path: 'pages/index/index',
          success: () => {
            console.log('跳转美团小程序成功')
          },
          fail: (error) => {
            console.error('跳转美团小程序失败:', error)
            wx.showToast({
              title: '跳转失败，请稍后重试',
              icon: 'none'
            })
          }
        })
      }, 1000)
    }
  },

  // 点击功能菜单
  onMenuTap(e) {
    const { type } = e.currentTarget.dataset
    console.log('点击菜单:', type)

    switch (type) {
      case 'order':
        this.handleOrderCenter()
        break
      case 'welfare':
        this.handleWelfareAssistant()
        break
      case 'group':
        this.handleWelfareGroup()
        break
      case 'service':
        this.handleCustomerService()
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  // 订单中心
  handleOrderCenter() {
    wx.showToast({
      title: '查看我的订单',
      icon: 'none'
    })
    
    // 可以跳转到订单页面或美团小程序
    setTimeout(() => {
      wx.navigateToMiniProgram({
        appId: 'wxde8ac0a21135c07d',
        path: 'pages/order/list',
        success: () => {
          console.log('跳转到美团订单页面成功')
        },
        fail: (error) => {
          console.error('跳转失败:', error)
          wx.showToast({
            title: '跳转失败，请稍后重试',
            icon: 'none'
          })
        }
      })
    }, 1000)
  },

  // 添加官方福利君
  handleWelfareAssistant() {
    wx.showModal({
      title: '添加官方福利君',
      content: '默认小助手，有20元福利等你来领取！',
      confirmText: '添加',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 加入省钱福利群
  handleWelfareGroup() {
    wx.showModal({
      title: '加入省钱福利群',
      content: '天天有20元福利，快来加入我们吧！',
      confirmText: '加入',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 客服
  handleCustomerService() {
    wx.showModal({
      title: '客服',
      content: '欢迎咨询，我们会尽快答复您哦～',
      confirmText: '联系客服',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '客服功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: '美团优惠大全',
      desc: '精选美团优惠券，吃喝玩乐全都有',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-logo.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '美团优惠大全 - 精选优惠券',
      imageUrl: '/assets/images/share-logo.png'
    }
  }
})