const apiService = require('../../utils/meituan-api-service')
const cacheManager = require('../../utils/cacheManager')

Page({
  data: {
    searchKeyword: '',
    categories: [
      { id: 1, name: '推荐', active: true },
      { id: 2, name: '美食', active: false },
      { id: 3, name: '外卖', active: false },
      { id: 4, name: '娱乐', active: false },
      { id: 5, name: '零售', active: false }
    ],
    activities: [],
    leftColumnActivities: [],
    rightColumnActivities: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    currentCategory: 1
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreActivities()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 初始化页面
  async initPage() {
    wx.showLoading({ title: '加载中...' })

    try {
      await this.loadActivities(true)
    } catch (error) {
      console.error('初始化页面失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 加载活动数据
  async loadActivities(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const page = refresh ? 1 : this.data.page
      const cacheKey = cacheManager.generateKey('share_activities', {
        category: this.data.currentCategory,
        keyword: this.data.searchKeyword,
        page
      })

      let activities = cacheManager.get(cacheKey)

      if (!activities) {
        // 尝试从美团API获取数据，如果失败则使用模拟数据
        try {
          activities = await this.fetchActivitiesFromAPI(page)
        } catch (apiError) {
          console.warn('API调用失败，使用模拟数据:', apiError)
          activities = this.generateMockActivities(page)
        }
        
        cacheManager.set(cacheKey, activities, 15 * 60 * 1000) // 15分钟缓存
      }

      if (refresh) {
        this.setData({
          activities: activities,
          page: 2,
          hasMore: activities.length >= this.data.pageSize
        })
      } else {
        this.setData({
          activities: [...this.data.activities, ...activities],
          page: page + 1,
          hasMore: activities.length >= this.data.pageSize
        })
      }

      // 分配到瀑布流两列
      this.distributeToColumns()

    } catch (error) {
      console.error('加载活动失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 从美团API获取活动数据
  async fetchActivitiesFromAPI(page) {
    const params = {
      page: page,
      limit: this.data.pageSize,
      category: this.data.currentCategory,
      keyword: this.data.searchKeyword
    }

    const response = await apiService.queryCoupon(params)
    
    if (response && response.data && response.data.list) {
      return response.data.list.map(item => ({
        id: item.skuViewId || `activity_${Date.now()}_${Math.random()}`,
        title: item.name || '美团优惠活动',
        image: item.headUrl || '/assets/images/activity-placeholder.png',
        discount: `立减${item.sellPrice || '10'}元`,
        expireDate: this.formatExpireDate(item.expireTime),
        shareCount: Math.floor(Math.random() * 1000 + 100),
        originalPrice: item.originalPrice || '0',
        sellPrice: item.sellPrice || '0',
        brandName: item.brandName || '美团商家',
        referralLink: item.referralLink || ''
      }))
    }

    throw new Error('API返回数据格式错误')
  },

  // 生成模拟活动数据
  generateMockActivities(page) {
    const activities = []
    const baseIndex = (page - 1) * this.data.pageSize

    const activityTitles = [
      '新用户专享立减20元',
      '美食节特惠，满100减30',
      '外卖免配送费券',
      'KTV欢唱券5折起',
      '电影票买一送一',
      '咖啡店下午茶套餐',
      '火锅店双人餐优惠',
      '健身房月卡特价',
      '美容院护理套餐',
      '书店图书满减活动'
    ]

    const discounts = [
      '立减20元', '满100减30', '5折优惠', '买一送一', 
      '免配送费', '立减50元', '8折特惠', '满200减80'
    ]

    for (let i = 0; i < this.data.pageSize; i++) {
      const index = baseIndex + i
      const titleIndex = index % activityTitles.length
      const discountIndex = index % discounts.length

      activities.push({
        id: `activity_${index}`,
        title: activityTitles[titleIndex],
        image: `/assets/images/activity-${(index % 5) + 1}.png`,
        discount: discounts[discountIndex],
        expireDate: this.getRandomExpireDate(),
        shareCount: Math.floor(Math.random() * 1000 + 100),
        originalPrice: (Math.random() * 100 + 50).toFixed(0),
        sellPrice: (Math.random() * 50 + 20).toFixed(0),
        brandName: `品牌${index + 1}`,
        referralLink: `https://meituan.com/activity/${index}`
      })
    }

    return activities
  },

  // 获取随机过期日期
  getRandomExpireDate() {
    const now = new Date()
    const futureDate = new Date(now.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000)
    const month = futureDate.getMonth() + 1
    const day = futureDate.getDate()
    return `${month}月${day}日到期`
  },

  // 格式化过期时间
  formatExpireDate(timestamp) {
    if (!timestamp) return this.getRandomExpireDate()
    
    const date = new Date(timestamp)
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}月${day}日到期`
  },

  // 分配活动到瀑布流两列
  distributeToColumns() {
    const leftColumn = []
    const rightColumn = []

    this.data.activities.forEach((activity, index) => {
      if (index % 2 === 0) {
        leftColumn.push(activity)
      } else {
        rightColumn.push(activity)
      }
    })

    this.setData({
      leftColumnActivities: leftColumn,
      rightColumnActivities: rightColumn
    })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 搜索确认
  onSearchConfirm() {
    this.setData({
      activities: [],
      leftColumnActivities: [],
      rightColumnActivities: [],
      page: 1,
      hasMore: true
    })
    this.loadActivities(true)
  },

  // 切换分类
  onCategoryTap(e) {
    const { index } = e.currentTarget.dataset
    const categories = this.data.categories.map((item, i) => ({
      ...item,
      active: i === index
    }))

    this.setData({
      categories,
      currentCategory: categories[index].id,
      activities: [],
      leftColumnActivities: [],
      rightColumnActivities: [],
      page: 1,
      hasMore: true
    })

    this.loadActivities(true)
  },

  // 点击活动卡片
  onActivityTap(e) {
    const { activity } = e.currentTarget.dataset
    
    // 生成推广链接并跳转
    this.generateReferralLink(activity)
  },

  // 生成推广链接
  async generateReferralLink(activity) {
    try {
      wx.showLoading({ title: '生成链接中...' })

      // 尝试调用美团API生成推广链接
      const params = {
        skuViewId: activity.id,
        mediaId: '1000304354',
        allianceId: '1904722314357399633',
        sid: 'yjd2025'
      }

      let referralLink = activity.referralLink

      try {
        const response = await apiService.getReferralLink(params)
        if (response && response.data && response.data.referralLink) {
          referralLink = response.data.referralLink
        }
      } catch (apiError) {
        console.warn('推广链接生成失败，使用默认链接:', apiError)
      }

      wx.hideLoading()

      // 跳转到美团小程序或H5页面
      if (referralLink.includes('meituan.com')) {
        wx.navigateToMiniProgram({
          appId: 'wxde8ac0a21135c07d', // 美团小程序AppID
          path: `pages/detail/detail?url=${encodeURIComponent(referralLink)}`,
          success: () => {
            console.log('跳转美团小程序成功')
          },
          fail: () => {
            // 如果跳转小程序失败，则复制链接
            wx.setClipboardData({
              data: referralLink,
              success: () => {
                wx.showToast({ title: '链接已复制', icon: 'success' })
              }
            })
          }
        })
      } else {
        wx.setClipboardData({
          data: referralLink,
          success: () => {
            wx.showToast({ title: '链接已复制', icon: 'success' })
          }
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('处理活动点击失败:', error)
      wx.showToast({ title: '操作失败', icon: 'none' })
    }
  },

  // 点击分享按钮
  onShareButtonTap(e) {
    e.stopPropagation() // 阻止事件冒泡
    const { activity } = e.currentTarget.dataset
    
    // 生成分享内容
    const shareData = {
      title: activity.title,
      desc: `${activity.discount} - ${activity.expireDate}`,
      path: `/pages/share/share?activityId=${activity.id}`,
      imageUrl: activity.image
    }

    // 调用微信分享
    wx.shareAppMessage(shareData)
  },

  // 加载更多活动
  loadMoreActivities() {
    this.loadActivities(false)
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadActivities(true)
      wx.showToast({ title: '刷新成功', icon: 'success' })
    } catch (error) {
      wx.showToast({ title: '刷新失败', icon: 'none' })
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 页面分享配置
  onShareAppMessage(options) {
    // 如果是通过分享按钮触发的分享
    if (options.from === 'button') {
      return options.target.dataset.shareData || {
        title: '美团优惠活动分享',
        desc: '发现更多优惠好物',
        path: '/pages/share/share'
      }
    }

    // 默认分享配置
    return {
      title: '美团优惠活动分享',
      desc: '精选美团优惠活动，吃喝玩乐全都有',
      path: '/pages/share/share',
      imageUrl: '/assets/images/share-logo.png'
    }
  }
})