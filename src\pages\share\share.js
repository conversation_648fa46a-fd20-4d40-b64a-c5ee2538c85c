const apiService = require('../../utils/api-service-fixed')
const cacheManager = require('../../utils/cacheManager')

Page({
  data: {
    tabs: [
      { id: 1, name: '推荐', active: true },
      { id: 2, name: '关注', active: false },
      { id: 3, name: '附近', active: false }
    ],
    shareList: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    currentTab: 1
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreShares()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 初始化页面
  async initPage() {
    wx.showLoading({ title: '加载中...' })
    
    try {
      await this.loadShares(true)
    } catch (error) {
      console.error('初始化页面失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 加载分享数据
  async loadShares(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const page = refresh ? 1 : this.data.page
      const cacheKey = cacheManager.generateKey('shares', {
        tab: this.data.currentTab,
        page
      })

      let shares = cacheManager.get(cacheKey)
      
      if (!shares) {
        // 生成模拟数据
        shares = this.generateMockShares(page)
        cacheManager.set(cacheKey, shares)
      }

      if (refresh) {
        this.setData({
          shareList: shares,
          page: 2,
          hasMore: shares.length >= this.data.pageSize
        })
      } else {
        this.setData({
          shareList: [...this.data.shareList, ...shares],
          page: page + 1,
          hasMore: shares.length >= this.data.pageSize
        })
      }
    } catch (error) {
      console.error('加载分享失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 生成模拟分享数据
  generateMockShares(page) {
    const shares = []
    const baseIndex = (page - 1) * this.data.pageSize

    for (let i = 0; i < this.data.pageSize; i++) {
      const index = baseIndex + i
      const hasMultipleImages = Math.random() > 0.5
      
      shares.push({
        id: `share_${index}`,
        user: {
          id: `user_${index}`,
          nickname: `用户${index + 1}`,
          avatar: '/assets/images/avatar-placeholder.png'
        },
        content: `发现了一个超棒的优惠！${index + 1} 这家店的性价比真的很高，推荐给大家～`,
        images: hasMultipleImages ? [
          '/assets/images/share-image1.png',
          '/assets/images/share-image2.png',
          '/assets/images/share-image3.png'
        ] : ['/assets/images/share-image1.png'],
        product: {
          id: `product_${index}`,
          title: `美团优惠商品 ${index + 1}`,
          image: '/assets/images/product-placeholder.png',
          price: (Math.random() * 50 + 10).toFixed(1),
          originalPrice: (Math.random() * 100 + 50).toFixed(1),
          shopName: `商家${index + 1}`
        },
        stats: {
          likes: Math.floor(Math.random() * 100 + 10),
          comments: Math.floor(Math.random() * 50 + 5),
          shares: Math.floor(Math.random() * 20 + 2)
        },
        isLiked: Math.random() > 0.7,
        createTime: Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
        location: `距离${(Math.random() * 2 + 0.5).toFixed(1)}km`
      })
    }

    return shares
  },

  // 加载更多分享
  loadMoreShares() {
    this.loadShares(false)
  },

  // 刷新数据
  async refreshData() {
    try {
      await this.loadShares(true)
      wx.showToast({ title: '刷新成功', icon: 'success' })
    } catch (error) {
      wx.showToast({ title: '刷新失败', icon: 'none' })
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 切换标签
  onTabTap(e) {
    const { index } = e.currentTarget.dataset
    const tabs = this.data.tabs.map((item, i) => ({
      ...item,
      active: i === index
    }))
    
    this.setData({ 
      tabs,
      currentTab: tabs[index].id,
      shareList: [],
      page: 1,
      hasMore: true
    })
    
    this.loadShares(true)
  },

  // 点击用户头像
  onUserTap(e) {
    const { userId } = e.currentTarget.dataset
    wx.showToast({ title: '用户详情开发中', icon: 'none' })
  },

  // 点击图片
  onImageTap(e) {
    const { images, current } = e.currentTarget.dataset
    wx.previewImage({
      urls: images,
      current: images[current]
    })
  },

  // 点击商品
  onProductTap(e) {
    const { productId } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${productId}`
    })
  },

  // 点击点赞
  onLikeTap(e) {
    const { index } = e.currentTarget.dataset
    const shareList = [...this.data.shareList]
    const item = shareList[index]
    
    item.isLiked = !item.isLiked
    item.stats.likes += item.isLiked ? 1 : -1
    
    this.setData({ shareList })
    
    wx.showToast({ 
      title: item.isLiked ? '已点赞' : '已取消', 
      icon: 'none' 
    })
  },

  // 点击评论
  onCommentTap(e) {
    const { shareId } = e.currentTarget.dataset
    wx.showToast({ title: '评论功能开发中', icon: 'none' })
  },

  // 点击分享
  onShareTap(e) {
    const { shareId } = e.currentTarget.dataset
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        const actions = ['分享给朋友', '分享到朋友圈', '复制链接']
        wx.showToast({ 
          title: `${actions[res.tapIndex]}成功`, 
          icon: 'success' 
        })
      }
    })
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '美团优惠分享',
      desc: '发现更多优惠好物',
      path: '/pages/share/share'
    }
  }
})