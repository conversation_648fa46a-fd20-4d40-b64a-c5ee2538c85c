/**
 * 最终Header修复测试
 * 确保S-Ca-Signature-Headers使用正确的逗号分隔格式
 */

// 模拟微信小程序环境
global.wx = {
  request: function(options) {
    console.log('\n=== 最终Header格式检查 ===')
    
    const signatureHeaders = options.header['S-Ca-Signature-Headers']
    console.log('S-Ca-Signature-Headers值:', `"${signatureHeaders}"`)
    
    // 检查是否包含逗号
    const hasComma = signatureHeaders.includes(',')
    const isCorrectFormat = signatureHeaders === 'S-Ca-App,S-Ca-Timestamp'
    
    console.log('格式检查结果:')
    console.log(`- 包含逗号: ${hasComma ? '✅ 是' : '❌ 否'}`)
    console.log(`- 正确格式: ${isCorrectFormat ? '✅ 是' : '❌ 否'}`)
    
    if (!isCorrectFormat) {
      console.log(`- 期望格式: "S-Ca-App,S-Ca-Timestamp"`)
      console.log(`- 实际格式: "${signatureHeaders}"`)
      console.log('❌ Header格式错误！需要修复')
    } else {
      console.log('✅ Header格式完全正确！')
    }
    
    // 模拟成功响应
    setTimeout(() => {
      if (isCorrectFormat) {
        options.success({
          statusCode: 200,
          data: { code: 0, message: 'success', data: { products: [] } },
          header: { 'Date': new Date().toUTCString() }
        })
      } else {
        options.fail({
          statusCode: 400,
          data: { code: 400, message: '请求签名验证失败' }
        })
      }
    }, 100)
  }
}

global.getApp = function() {
  return {
    globalData: {
      location: { latitude: 39.928, longitude: 116.404 }
    }
  }
}

console.log('=== 最终Header修复测试 ===\n')

async function runHeaderFixTest() {
  try {
    const ApiService = require('../src/utils/api-service-fixed.js')
    console.log('✅ API服务加载成功')
    
    console.log('\n📋 测试Header格式...')
    const result = await ApiService.queryCoupon(39.928, 116.404, {
      pageNo: 1,
      pageSize: 5,
      listTopiId: 'hot_sale'
    })
    
    console.log('\n🎉 测试完成')
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

runHeaderFixTest()