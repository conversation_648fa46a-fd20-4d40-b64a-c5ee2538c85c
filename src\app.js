// 小程序入口文件
App({
  globalData: {
    userInfo: null,
    currentCity: '广州',
    location: null,
    apiConfig: {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      mediaId: '1000304354',
      allianceId: '1904722314357399633',
      sid: 'yjd2025'
    }
  },

  onLaunch() {
    console.log('小程序启动')
    this.initApp()
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
  },

  // 初始化应用
  initApp() {
    // 获取用户位置
    this.getUserLocation()
    // 清理过期缓存
    this.clearExpiredCache()
  },

  // 获取用户位置
  getUserLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        this.globalData.location = {
          latitude: res.latitude,
          longitude: res.longitude
        }
        // 根据位置获取城市信息
        this.getCityByLocation(res.latitude, res.longitude)
      },
      fail: (error) => {
        console.log('获取位置失败:', error)
        // 使用默认城市
        this.globalData.currentCity = '广州'
      }
    })
  },

  // 根据位置获取城市
  getCityByLocation(latitude, longitude) {
    // 调用微信逆地理编码API获取城市信息
    wx.request({
      url: 'https://apis.map.qq.com/ws/geocoder/v1/',
      data: {
        location: `${latitude},${longitude}`,
        key: 'your_tencent_map_key', // 需要申请腾讯地图API密钥
        get_poi: 0
      },
      success: (res) => {
        if (res.data.status === 0) {
          const city = res.data.result.address_component.city
          this.globalData.currentCity = city.replace('市', '')
        }
      },
      fail: () => {
        // 使用默认城市
        this.globalData.currentCity = '广州'
      }
    })
  },

  // 清理过期缓存
  clearExpiredCache() {
    try {
      const cacheKeys = wx.getStorageInfoSync().keys
      const now = Date.now()
      
      cacheKeys.forEach(key => {
        if (key.startsWith('meituan_cache_')) {
          const cacheData = wx.getStorageSync(key)
          if (cacheData && cacheData.expireTime && now > cacheData.expireTime) {
            wx.removeStorageSync(key)
          }
        }
      })
    } catch (error) {
      console.error('清理缓存失败:', error)
    }
  }
})