/**
 * 时间戳验证测试
 * 专门测试时间戳生成和验证功能
 */

// 模拟微信小程序环境
global.wx = {
  request: function(options) {
    console.log('模拟wx.request调用:', options.url)
    console.log('请求头时间戳:', options.header['S-Ca-Timestamp'])
    
    // 模拟服务器响应，包含时间戳验证
    const requestTimestamp = parseInt(options.header['S-Ca-Timestamp'])
    const currentTimestamp = Math.floor(Date.now() / 1000)
    const timeDiff = Math.abs(currentTimestamp - requestTimestamp)
    
    console.log('服务器时间戳验证:')
    console.log('- 请求时间戳:', requestTimestamp, new Date(requestTimestamp * 1000).toISOString())
    console.log('- 服务器时间戳:', currentTimestamp, new Date(currentTimestamp * 1000).toISOString())
    console.log('- 时间差:', timeDiff, '秒')
    
    // 模拟时间戳过期检查（允许5分钟误差）
    const isExpired = timeDiff > 300
    
    setTimeout(() => {
      if (isExpired) {
        // 模拟时间戳过期错误
        options.success({
          statusCode: 200,
          data: { 
            code: 400, 
            message: '请求时间戳已过期',
            serverTime: currentTimestamp
          },
          header: { 
            'Date': new Date().toUTCString(),
            'Server-Time': currentTimestamp.toString()
          }
        })
      } else {
        // 模拟成功响应
        options.success({
          statusCode: 200,
          data: { 
            code: 0, 
            message: 'success',
            data: { test: 'success' }
          },
          header: { 
            'Date': new Date().toUTCString(),
            'Server-Time': currentTimestamp.toString()
          }
        })
      }
    }, 100)
  }
}

// 模拟getApp函数
global.getApp = function() {
  return {
    globalData: {
      location: { latitude: 39.928, longitude: 116.404 }
    }
  }
}

console.log('=== 时间戳验证测试 ===\n')

async function runTimestampTest() {
  try {
    // 测试美团时间修复工具
    console.log('1. 测试美团时间修复工具')
    const meituanTimeFix = require('../src/utils/meituan-time-fix.js')
    
    // 测试基本时间戳生成
    const timestamp1 = meituanTimeFix.getMeituanTimestamp()
    console.log('生成时间戳1:', timestamp1, new Date(timestamp1 * 1000).toISOString())
    
    // 等待1秒后再生成
    await new Promise(resolve => setTimeout(resolve, 1000))
    const timestamp2 = meituanTimeFix.getMeituanTimestamp()
    console.log('生成时间戳2:', timestamp2, new Date(timestamp2 * 1000).toISOString())
    console.log('时间差:', timestamp2 - timestamp1, '秒')
    
    // 测试时间戳过期处理
    console.log('\n2. 测试时间戳过期处理')
    const expiredError = { code: 400, message: '请求时间戳已过期' }
    const handled = meituanTimeFix.handleTimestampExpired(expiredError)
    console.log('时间戳过期处理结果:', handled)
    console.log('处理后状态:', meituanTimeFix.getStatus())
    
    // 测试API服务
    console.log('\n3. 测试API服务时间戳生成')
    const ApiService = require('../src/utils/api-service-fixed.js')
    
    // 测试签名生成
    const signature1 = ApiService.generateSignature('POST', '/test', { test: 'data' })
    console.log('签名1:', {
      timestamp: signature1.timestamp,
      time: new Date(parseInt(signature1.timestamp) * 1000).toISOString()
    })
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const signature2 = ApiService.generateSignature('POST', '/test', { test: 'data' })
    console.log('签名2:', {
      timestamp: signature2.timestamp,
      time: new Date(parseInt(signature2.timestamp) * 1000).toISOString()
    })
    
    console.log('时间戳递增:', parseInt(signature2.timestamp) > parseInt(signature1.timestamp))
    
    // 测试实际请求
    console.log('\n4. 测试实际API请求')
    try {
      const result = await ApiService.request('/test', { test: 'data' })
      console.log('请求成功:', result)
    } catch (error) {
      console.log('请求失败:', error.message)
    }
    
    console.log('\n✅ 时间戳验证测试完成')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    console.error('错误堆栈:', error.stack)
  }
}

runTimestampTest()