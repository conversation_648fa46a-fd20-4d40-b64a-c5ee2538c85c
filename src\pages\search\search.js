const apiService = require('../../utils/api-service-fixed')
const cacheManager = require('../../utils/cacheManager')

Page({
  data: {
    keyword: '',
    searchHistory: [],
    hotKeywords: [
      '肯德基', '麦当劳', '星巴克', '必胜客', '海底捞',
      '奶茶', '火锅', '烧烤', '日料', '西餐'
    ],
    searchResults: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    showResults: false
  },

  onLoad(options) {
    if (options.keyword) {
      this.setData({ keyword: decodeURIComponent(options.keyword) })
      this.performSearch()
    }
    this.loadSearchHistory()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading && this.data.showResults) {
      this.loadMoreResults()
    }
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({ searchHistory: history.slice(0, 10) })
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    try {
      let history = wx.getStorageSync('searchHistory') || []
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 限制数量
      history = history.slice(0, 20)
      
      wx.setStorageSync('searchHistory', history)
      this.setData({ searchHistory: history.slice(0, 10) })
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({ keyword: e.detail.value })
  },

  // 搜索提交
  onSearchSubmit() {
    this.performSearch()
  },

  // 执行搜索
  async performSearch() {
    const keyword = this.data.keyword.trim()
    if (!keyword) return

    this.saveSearchHistory(keyword)
    
    this.setData({
      loading: true,
      showResults: true,
      searchResults: [],
      page: 1,
      hasMore: true
    })

    try {
      const cacheKey = cacheManager.generateKey('search', { keyword, page: 1 })
      let results = cacheManager.get(cacheKey)
      
      if (!results) {
        const response = await apiService.searchProducts(keyword, 1, this.data.pageSize)
        results = this.formatSearchResults(response.data || [])
        cacheManager.cacheProducts(cacheKey, results)
      }

      this.setData({
        searchResults: results,
        page: 2,
        hasMore: results.length >= this.data.pageSize
      })
    } catch (error) {
      console.error('搜索失败:', error)
      wx.showToast({ title: '搜索失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载更多结果
  async loadMoreResults() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const keyword = this.data.keyword.trim()
      const page = this.data.page
      
      const cacheKey = cacheManager.generateKey('search', { keyword, page })
      let results = cacheManager.get(cacheKey)
      
      if (!results) {
        const response = await apiService.searchProducts(keyword, page, this.data.pageSize)
        results = this.formatSearchResults(response.data || [])
        cacheManager.cacheProducts(cacheKey, results)
      }

      this.setData({
        searchResults: [...this.data.searchResults, ...results],
        page: page + 1,
        hasMore: results.length >= this.data.pageSize
      })
    } catch (error) {
      console.error('加载更多失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 格式化搜索结果
  formatSearchResults(rawData) {
    return rawData.map(item => ({
      id: item.id || Math.random().toString(36).substr(2, 9),
      title: item.title || '美团优惠商品',
      image: item.image || '/assets/images/placeholder.png',
      price: item.price || '19.9',
      originalPrice: item.originalPrice || '39.9',
      tags: item.tags || ['外卖'],
      shopName: item.shopName || '美团商家',
      sales: item.sales || '月销100+',
      rating: item.rating || '4.8'
    }))
  },

  // 点击热门关键词
  onHotKeywordTap(e) {
    const { keyword } = e.currentTarget.dataset
    this.setData({ keyword })
    this.performSearch()
  },

  // 点击搜索历史
  onHistoryTap(e) {
    const { keyword } = e.currentTarget.dataset
    this.setData({ keyword })
    this.performSearch()
  },

  // 清空搜索历史
  onClearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('searchHistory')
          this.setData({ searchHistory: [] })
        }
      }
    })
  },

  // 点击商品
  onProductTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 返回首页
  onBackTap() {
    if (this.data.showResults) {
      this.setData({
        showResults: false,
        searchResults: [],
        keyword: ''
      })
    } else {
      wx.navigateBack()
    }
  }
})