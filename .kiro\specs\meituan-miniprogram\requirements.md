# 需求文档

## 项目介绍

本项目开发一个集成美团联盟CPS API的微信小程序，为用户提供美团优惠活动信息展示和推广链接生成功能。小程序界面设计严格按照提供的UI设计图进行1:1还原，包含首页、活动、分享、我的四个主要功能模块。应用使用微信API获取用户位置信息，提供本地化服务。

**技术要求：**
- **开发框架**: 微信小程序原生框架
- **部署方式**: 通过微信开发者工具直接部署，无需服务器
- **API集成**: 美团联盟CPS API（已测试验证可访问）
- **UI实现**: 按照UI设计图完全1:1还原

## 需求列表

### 需求1 - 界面布局和导航系统

**用户故事：** 作为用户，我希望通过直观的界面浏览美团优惠信息，以便快速找到感兴趣的活动和商品。

#### 验收标准

1. 当用户打开小程序时，系统应显示完整的首页界面，包括状态栏、城市选择器和搜索栏
2. 当用户点击底部导航时，系统应在首页、活动、分享、我的页面之间切换
3. 当用户在首页时，系统应显示城市选择器、搜索框、分类图标和推荐商品列表
4. 当用户点击搜索框时，系统应导航到搜索页面并支持关键词搜索

### 需求2 - 位置服务和城市选择

**用户故事：** 作为用户，我希望小程序能够获取我的当前位置，以便我能够获得附近的优惠活动和商家推荐。

#### 验收标准

1. 当用户首次使用小程序时，系统应请求用户授权位置访问权限
2. 当用户授权位置访问时，系统应通过微信API获取当前位置
3. 当系统获取位置信息时，系统应在界面顶部显示当前城市名称
4. 当用户点击城市名称时，系统应允许手动选择城市
5. 如果用户拒绝位置权限，系统应使用默认城市并提示手动选择选项

### 需求3 - 美团联盟API集成

**用户故事：** 作为用户，我希望看到最新的美团优惠券和活动信息，以便我能够获得实惠的购物体验。

#### 验收标准

1. 当系统启动时，系统应通过美团联盟API获取推广链接和商品信息
2. 当调用API时，系统应使用正确的签名算法和认证信息：
   - S-Ca-App: 9498b0824d214ee4b65bfab1be6dbed0
   - Media ID: 1000304354
   - Alliance ID: 1904722314357399633
   - SID: yjd2025
   - 签名算法: HmacSHA256(stringToSign, secret) -> Base64编码
   - 时间戳: 毫秒时间戳 Date.now()
   - Content-MD5: Base64(MD5(requestBody))
3. 当构建签名字符串时，系统应遵循美团文档格式: HTTPMethod + "\n" + Content-MD5 + "\n" + Headers + Url
4. 当设置请求头时，系统应包含必需的Headers: S-Ca-App, S-Ca-Signature, S-Ca-Timestamp, Content-MD5, S-Ca-Signature-Headers
5. 当获取商品数据时，系统应显示商品图片、标题、价格、促销信息和商家信息
6. 当用户点击商品时，系统应生成带有联盟ID的推广链接并跳转到美团页面
7. 当API调用失败时，系统应显示友好的错误消息并提供重试选项

### 需求4 - 首页内容展示

**用户故事：** 作为用户，我希望在首页看到丰富的活动内容和商品推荐，以便快速找到感兴趣的优惠。

#### 验收标准

1. 当用户查看首页时，系统应通过API调用显示活动横幅：
   - 主横幅："新用户专享（下单返现）"橙色背景横幅
   - 活动时间："活动时间：7月1日-7月8日"
2. 当用户查看首页时，系统应显示品牌快速入口：
   - 下午茶返现、今日秒杀、神券包、每日签到、今日免单等图标入口
3. 当用户查看首页时，系统应显示分类标签：
   - 为你推荐、美食、外卖券、休闲娱乐、零售等横向滚动标签
4. 当用户查看首页时，系统应以瀑布流布局显示商品列表：
   - 商品图片、标题、价格、促销标签和商家信息
   - 如"懒猪手特色！经典美味双人餐"等商品卡片
5. 当用户滚动页面时，系统应支持瀑布流无限滚动加载更多商品
6. 当用户使用搜索功能时，系统应通过API调用获取搜索结果并以瀑布流布局显示

### 需求5 - 活动页面功能

**用户故事：** 作为用户，我希望有专门的活动页面查看所有促销活动，以便不错过任何优惠机会。

#### 验收标准

1. 当用户点击活动标签时，系统应显示活动页面的完整界面布局：
   - 城市选择器（"广州"下拉选择）
   - 搜索栏（"搜主菜站"占位符，右侧搜索按钮）
   - 品牌推广区域（肯德基、麦当劳、星巴克、必胜客、茶颜悦色品牌图标）
2. 当用户查看活动页面时，系统应显示分类标签栏：
   - 全部新品、美食餐饮、外卖、休闲娱乐、丽人等横向滚动标签
3. 当用户查看活动页面时，系统应显示筛选功能：
   - 全部、品类、智能排序等筛选选项
4. 当用户查看活动页面时，系统应以列表格式显示商品信息：
   - 商品图片（左侧，方形布局）
   - 商品标题和描述（右侧）
   - 价格信息（红色现价，灰色删除线原价）
   - 促销标签（"外卖"黄色标签）
   - 红色"抢"按钮（右侧）
5. 当用户点击商品时，系统应生成推广链接并跳转到美团页面
6. 当用户滚动页面时，系统应支持无限滚动加载更多商品

### 需求6 - 分享页面功能

**用户故事：** 作为用户，我希望与朋友分享美团的促销活动，以便我们能够一起享受优惠。

#### 验收标准

1. 当用户点击分享标签时，系统应显示分享页面的完整界面布局：
   - 顶部标题："分享活动"黄色艺术字体
   - 活动搜索功能的搜索栏
2. 当用户查看分享页面时，系统应显示分类标签栏：
   - 推荐、美食、外卖、娱乐、零售等横向滚动标签
   - 推荐标签默认选中（红色下划线）
3. 当用户查看分享页面时，系统应显示来自美团API的促销活动：
   - 带有促销图片、标题和折扣信息的活动卡片
   - 微信分享功能的分享按钮
4. 当用户查看分享页面时，系统应以瀑布流布局显示活动内容：
   - 活动图片、标题、折扣金额和到期日期
   - 分享次数和人气指标
   - 直接链接到美团促销页面
5. 当用户点击分享按钮时，系统应生成带有促销链接的小程序分享卡片
6. 当用户滚动页面时，系统应支持无限滚动从API加载更多活动

### 需求7 - 个人中心页面功能

**用户故事：** 作为用户，我希望有个人中心查看基本功能入口，以便快速访问相关功能。

#### 验收标准

1. 当用户点击个人中心标签时，系统应显示个人中心页面的完整界面布局：
   - 黄色渐变背景区域
2. 当用户查看个人中心时，系统应显示用户信息区域：
   - 用户头像（圆形头像，默认头像）
   - 用户ID："AGx424069683"
   - 手机号码："186****7573"（脱敏显示）
   - 退出登录按钮（右上角）
3. 当用户查看个人中心时，系统应显示统计信息卡片：
   - 红包/神券：28个未使用（可点击，跳转到美团小程序）
   - 代金券：6张未使用（可点击，跳转到美团小程序）
   - 单笔返现：0.0元未提现
4. 当用户查看个人中心时，系统应显示功能入口列表：
   - 订单中心（查看我的订单）
   - 添加官方福利君（默认小助手，有20元）
   - 加入省钱福利群（天天有20元福利）
   - 客服（欢迎咨询，我们会尽快答复您哦～）
5. 当用户查看个人中心时，系统应显示版本信息：
   - 当前版本：v1.0.0正式版
6. 当用户点击功能入口时，系统应导航到对应的功能页面

### 需求8 - 搜索结果页面功能

**用户故事：** 作为用户，我希望有专门的搜索结果页面显示搜索的商品，以便我能够方便地浏览和选择。

#### 验收标准

1. 当用户在搜索栏输入关键词并点击搜索时，系统应导航到搜索结果页面并显示完整界面布局：
   - 导航栏（左侧返回按钮，中间搜索栏，右侧搜索按钮）
   - 搜索栏（保持搜索关键词，支持重新搜索，美团橙色#FF6600搜索按钮）
2. 当用户查看搜索结果页面时，系统应显示：
   - 搜索结果统计（如"找到123个相关商品"，#666666灰色小字，12px）
   - 筛选和排序选项（价格、评分等，横向布局，白色背景卡片）
   - 筛选标签（综合排序、价格升序、评分降序等，可点击切换）
3. 当用户查看搜索结果时，系统应以瀑布流布局显示搜索的商品：
   - 与首页相同的双列瀑布流布局（列间距16px，项目间距12px）
   - 商品卡片样式保持一致（白色背景，8px圆角，阴影效果）
   - 商品信息：图片、标题（14px，最多2行）、价格（红色现价+灰色原价）、促销标签
   - 支持点击商品生成推广链接并跳转到美团小程序
4. 当搜索无结果时，系统应显示友好的空状态提示：
   - 空状态图标（灰色搜索图标，64px大小）
   - 提示文字（"未找到相关商品"，#999999颜色，16px字体）
   - 推荐商品列表（热门或相关商品，瀑布流布局）
   - 搜索建议（热门搜索词，横向标签布局）
5. 当用户滚动页面时，系统应支持无限滚动加载更多搜索结果
6. 当用户点击筛选选项时，系统应显示筛选弹窗：
   - 价格区间选择（0-50元、50-100元、100元以上等）
   - 评分筛选（4.5星以上、4.0星以上等）
   - 分类筛选（美食、娱乐、零售等）
   - 确认和重置按钮

### 需求9 - 城市选择页面功能

**用户故事：** 作为用户，我希望能够方便地选择不同城市，以便查看各地的优惠信息。

#### 验收标准

1. 当用户点击城市选择器时，系统应导航到城市选择页面并显示完整界面布局：
   - 导航栏（左侧返回按钮，中间标题"选择城市"，美团橙色#FF6600背景）
   - 页面背景色：#F5F5F5浅灰色
2. 当用户查看城市选择页面时，系统应显示：
   - 当前位置城市区域（白色背景卡片，8px圆角）：
     * "当前位置"标题（#333333，14px字体）
     * 定位城市名称和GPS定位图标（美团橙色）
     * 重新定位按钮（#FF6600橙色文字）
   - 搜索框（白色背景，20px圆角，#F0F0F0边框）：
     * 左侧搜索图标（#999999灰色）
     * "请输入城市名称"占位符文字
   - 热门城市区域（白色背景卡片）：
     * "热门城市"标题（#333333，16px粗体）
     * 城市网格布局（3列，每个城市白色背景，4px圆角）
     * 城市列表：北京、上海、广州、深圳、杭州、南京、成都、武汉、西安
3. 当用户查看全部城市列表时，系统应显示：
   - 字母分组标题（#F0F0F0灰色背景，#666666文字，12px）
   - 城市名称列表（每行一个城市，白色背景，#E5E5E5分割线）
   - 右侧字母索引（固定位置，#FF6600橙色背景，白色文字）
   - 字母索引：A-Z完整字母表，支持快速跳转
4. 当用户选择城市时，系统应：
   - 显示选择确认动画（橙色勾选图标）
   - 更新当前城市信息到本地存储
   - 延迟500ms后自动返回原页面
   - 刷新相关页面商品和活动数据
   - 显示Toast提示："已切换到XXX"
5. 当用户拒绝位置权限时，系统应：
   - 隐藏当前位置区域
   - 显示位置权限说明卡片（黄色背景提示）
   - 提供"前往设置"按钮引导用户开启权限
   - 默认显示"广州"作为当前城市

### 需求10 - 商品详情页面功能

**用户故事：** 作为用户，我希望点击商品时能够查看详细信息，以便了解商品详情和促销信息。

#### 验收标准

1. 当用户点击商品卡片时，系统应导航到商品详情页面并显示完整界面布局：
   - 导航栏（左侧返回按钮，右侧分享按钮，透明背景）
   - 商品图片轮播区域（支持左右滑动查看多张图片）
2. 当用户查看商品详情时，系统应显示：
   - 商品基本信息区域（白色背景卡片）：
     * 商品标题（#333333，16px粗体，支持多行显示）
     * 商品价格（红色现价#FF3333，18px粗体 + 灰色原价#999999，14px删除线）
     * 促销标签（红色背景"立减"、"满减"等标签）
     * 销量和评分信息（"月销1000+，评分4.8星"）
   - 商家信息区域（白色背景卡片）：
     * 商家名称和Logo（左侧）
     * 商家评分信息
     * "进店"按钮（右侧，美团橙色边框）
3. 当用户查看商品详情时，系统应显示详细商品信息：
   - 商品描述区域（白色背景卡片）
   - 商品规格和参数信息
   - 用户评价列表（显示前3条评价，支持查看更多）
   - 相关推荐商品（横向滚动列表）
4. 当用户查看页面底部时，系统应显示操作按钮：
   - 客服按钮（左侧，灰色边框）
   - 收藏按钮（中间，心形图标）
   - 立即购买按钮（右侧，美团橙色背景，生成推广链接）
5. 当用户点击立即购买时，系统应：
   - 调用美团联盟API生成推广链接
   - 跳转到美团小程序对应商品页面
   - 记录用户点击行为用于统计分析

### 需求11 - 数据缓存和性能优化

**用户故事：** 作为用户，我希望小程序能够快速加载，即使在网络较差的情况下也能正常使用，以便我能够获得良好的用户体验。

#### 验收标准

1. 当系统获取API数据时，系统应将商品和活动数据缓存到本地存储：
   - 缓存时间：商品数据30分钟，活动数据15分钟，城市数据24小时
   - 缓存大小限制：单个缓存项不超过1MB，总缓存不超过10MB
   - 缓存键规范：使用"meituan_cache_"前缀 + 数据类型 + 参数哈希
2. 当网络连接较差时，系统应优先显示缓存数据并尝试后台更新：
   - 显示缓存数据时添加"数据可能不是最新"提示
   - 后台静默更新数据，更新成功时刷新界面
   - 网络超时设置为5秒，重试3次
3. 当用户重复访问相同内容时，系统应从缓存加载以提高响应速度：
   - 首屏数据优先缓存加载，提升用户体验
   - 同时发起网络请求更新数据，确保数据新鲜度
   - 加载状态优化：缓存加载显示骨架屏，网络加载显示刷新动画
4. 当缓存数据过期时，系统应自动清理并重新获取最新数据：
   - 每次应用启动时检查并清理过期缓存
   - 缓存空间不足时通过LRU算法清理最少使用的数据
   - 提供手动清理缓存功能（在个人中心设置中）
5. 当实现瀑布流布局时，系统应优化图片加载和内存使用以确保流畅滚动：
   - 图片懒加载：距离可视区域200px时开始加载
   - 图片压缩：根据显示尺寸动态调整图片质量
   - 内存管理：滚动时释放不可见图片内存，保留占位符
   - 虚拟滚动：仅渲染可视区域±3屏内的商品项目
