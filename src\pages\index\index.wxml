<!--首页-->
<view class="index-page">
  <!-- 顶部区域 -->
  <view class="header">
    <!-- 城市选择器 -->
    <city-selector
      current-city="{{currentCity}}"
      bind:change="onCityChange" />

    <!-- 搜索框 -->
    <search-bar
      placeholder="搜主菜站"
      bind:search="onSearch"
      bind:tap="onSearchTap" />
  </view>

  <!-- 横幅区域 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{bannerList.length > 1}}" autoplay circular>
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <view class="banner-item" style="background-color: {{item.bgColor}}">
          <view class="banner-content">
            <text class="banner-title">{{item.title}}</text>
            <text class="banner-subtitle">{{item.subtitle}}</text>
          </view>
          <image class="banner-image" src="{{item.image}}" mode="aspectFit" />
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快速入口 -->
  <view class="quick-entries">
    <view class="quick-item" wx:for="{{quickEntries}}" wx:key="id"
          data-id="{{item.id}}" bindtap="onQuickEntryTap">
      <image class="quick-icon" src="{{item.icon}}" />
      <text class="quick-name">{{item.name}}</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view class="category-item {{item.active ? 'active' : ''}}"
              wx:for="{{categories}}" wx:key="id"
              data-index="{{index}}" bindtap="onCategoryTap">
          <text class="category-name">{{item.name}}</text>
          <view class="category-line" wx:if="{{item.active}}"></view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 商品列表 -->
  <view class="product-section">
    <waterfall-list
      items="{{productList}}"
      loading="{{loading}}"
      has-more="{{hasMore}}"
      bind:itemtap="onProductTap"
      bind:loadmore="onWaterfallLoadMore" />

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && productList.length === 0}}">
      <image class="empty-icon" src="/assets/icons/empty.png" />
      <text class="empty-text">暂无商品</text>
    </view>
  </view>
</view>