<!--城市选择页面-->
<view class="city-page">
  <!-- 位置权限引导弹窗 -->
  <view class="permission-modal" wx:if="{{showPermissionModal}}">
    <view class="modal-mask" bindtap="onClosePermissionModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">获取位置权限</text>
        <view class="modal-close" bindtap="onClosePermissionModal">
          <image class="close-icon" src="/assets/icons/close.png" />
        </view>
      </view>
      <view class="modal-body">
        <image class="permission-icon" src="/assets/icons/location.png" />
        <text class="permission-desc">为了给您提供更精准的本地服务，需要获取您的位置信息</text>
        <view class="permission-tips">
          <text class="tip-item">• 自动定位到当前城市</text>
          <text class="tip-item">• 推荐附近优惠活动</text>
          <text class="tip-item">• 提供本地化服务</text>
        </view>
      </view>
      <view class="modal-footer">
        <view class="btn-cancel" bindtap="onClosePermissionModal">暂不开启</view>
        <view class="btn-confirm" bindtap="onRequestLocation">开启定位</view>
      </view>
    </view>
  </view>

  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-bar">
      <image class="search-icon" src="/assets/icons/search.png" />
      <input class="search-input" 
             placeholder="请输入城市名称" 
             value="{{searchKeyword}}"
             bindinput="onSearchInput"
             focus="{{searchFocus}}" />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onSearchClear">
        <image class="clear-icon" src="/assets/icons/close.png" />
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{showSearch}}">
    <view class="result-item {{selectedCity === item.name ? 'selected' : ''}}" 
          wx:for="{{searchResults}}" wx:key="code"
          data-name="{{item.name}}" bindtap="onCitySelect">
      <text class="result-name">{{item.name}}</text>
      <view class="check-icon" wx:if="{{selectedCity === item.name}}">✓</view>
    </view>
    <view class="no-result" wx:if="{{searchResults.length === 0 && searchKeyword}}">
      <image class="empty-icon" src="/assets/icons/empty.png" />
      <text class="empty-text">未找到相关城市</text>
      <text class="empty-tip">请尝试输入其他关键词</text>
    </view>
  </view>

  <!-- 城市列表 -->
  <view class="city-content" wx:else>
    <!-- 当前定位 -->
    <view class="location-section">
      <view class="location-card">
        <view class="location-header">
          <text class="section-title">当前位置</text>
          <view class="location-status {{locationStatus}}">
            <image class="status-icon" src="/assets/icons/location.png" />
            <text class="status-text">{{locationStatusText}}</text>
          </view>
        </view>
        <view class="location-item {{locationLoading ? 'loading' : ''}}" bindtap="onLocationTap">
          <view class="location-info">
            <image class="location-icon" src="/assets/icons/location.png" />
            <text class="location-name">{{currentCity}}</text>
          </view>
          <view class="location-action">
            <text class="location-tip">{{locationLoading ? '定位中...' : '重新定位'}}</text>
            <image class="arrow-icon" src="/assets/icons/arrow-right.png" />
          </view>
        </view>
      </view>
    </view>

    <!-- 热门城市 -->
    <view class="hot-section">
      <view class="hot-card">
        <view class="section-title">热门城市</view>
        <view class="hot-cities">
          <view class="hot-item {{selectedCity === item.name ? 'selected' : ''}}" 
                wx:for="{{hotCities}}" wx:key="code"
                data-name="{{item.name}}" bindtap="onCitySelect">
            <text class="hot-name">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 全部城市 -->
    <view class="all-section">
      <view class="all-card">
        <view class="section-title">全部城市</view>
        <scroll-view class="city-list" scroll-y="true" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="true">
          <view class="letter-group" wx:for="{{letters}}" wx:key="*this" id="letter-{{item}}">
            <view class="letter-title">{{item}}</view>
            <view class="city-group">
              <view class="city-item {{selectedCity === city.name ? 'selected' : ''}}" 
                    wx:for="{{allCities[item]}}" wx:key="code" wx:for-item="city"
                    data-name="{{city.name}}" bindtap="onCitySelect">
                <text class="city-name">{{city.name}}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 字母索引 -->
    <view class="letter-index">
      <view class="index-item {{currentLetter === item ? 'active' : ''}}" 
            wx:for="{{letters}}" wx:key="*this"
            data-letter="{{item}}" bindtap="onLetterTap" bindtouchstart="onLetterTouchStart" bindtouchend="onLetterTouchEnd">
        <text class="index-text">{{item}}</text>
      </view>
    </view>

    <!-- 字母提示 -->
    <view class="letter-tip {{showLetterTip ? 'show' : ''}}" wx:if="{{currentLetter}}">
      <text class="tip-text">{{currentLetter}}</text>
    </view>
  </view>

  <!-- 选择确认动画 -->
  <view class="select-animation {{showSelectAnimation ? 'show' : ''}}" wx:if="{{selectedCity}}">
    <view class="animation-content">
      <view class="check-animation">✓</view>
      <text class="select-text">已切换到{{selectedCity}}</text>
    </view>
  </view>
</view>