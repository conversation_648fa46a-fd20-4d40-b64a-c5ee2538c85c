<!--城市选择页面-->
<view class="city-page">
  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-bar">
      <image class="search-icon" src="/assets/icons/search.png" />
      <input class="search-input" 
             placeholder="搜索城市" 
             value="{{searchKeyword}}"
             bindinput="onSearchInput" />
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="onSearchClear">
        <image class="clear-icon" src="/assets/icons/close.png" />
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{showSearch}}">
    <view class="result-item" wx:for="{{searchResults}}" wx:key="code"
          data-name="{{item.name}}" bindtap="onCitySelect">
      <text class="result-name">{{item.name}}</text>
    </view>
    <view class="no-result" wx:if="{{searchResults.length === 0}}">
      <text>未找到相关城市</text>
    </view>
  </view>

  <!-- 城市列表 -->
  <view class="city-content" wx:else>
    <!-- 当前定位 -->
    <view class="location-section">
      <view class="section-title">当前定位</view>
      <view class="location-item" bindtap="onLocationTap">
        <image class="location-icon" src="/assets/icons/location.png" />
        <text class="location-name">{{currentCity}}</text>
        <text class="location-tip">点击重新定位</text>
      </view>
    </view>

    <!-- 热门城市 -->
    <view class="hot-section">
      <view class="section-title">热门城市</view>
      <view class="hot-cities">
        <view class="hot-item" wx:for="{{hotCities}}" wx:key="code"
              data-name="{{item.name}}" bindtap="onCitySelect">
          <text class="hot-name">{{item.name}}</text>
        </view>
      </view>
    </view>

    <!-- 全部城市 -->
    <view class="all-section">
      <view class="section-title">全部城市</view>
      <view class="city-list">
        <view class="letter-group" wx:for="{{letters}}" wx:key="*this">
          <view class="letter-title">{{item}}</view>
          <view class="city-group">
            <view class="city-item" wx:for="{{allCities[item]}}" wx:key="code"
                  data-name="{{item.name}}" bindtap="onCitySelect">
              <text class="city-name">{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>