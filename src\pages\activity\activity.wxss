/* 活动页面样式 */
.activity-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 顶部区域 */
.header {
  background: var(--white);
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.city-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: var(--bg-color);
  border-radius: 20rpx;
  min-width: 120rpx;
}

.city-name {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

.city-arrow {
  width: 24rpx;
  height: 24rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
}

/* 筛选区域 */
.filter-section {
  background: var(--white);
  padding: 0 24rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 48rpx;
}

.category-item {
  padding: 32rpx 0;
  position: relative;
}

.category-name {
  font-size: 28rpx;
  color: var(--text-light);
  white-space: nowrap;
}

.category-item.active .category-name {
  color: var(--primary-color);
  font-weight: 500;
}

.category-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 2rpx;
}

/* 排序区域 */
.sort-section {
  background: var(--white);
  padding: 24rpx;
  border-bottom: 2rpx solid var(--border-color);
}

.sort-scroll {
  white-space: nowrap;
}

.sort-list {
  display: flex;
  gap: 32rpx;
}

.sort-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  background: var(--bg-color);
}

.sort-item.active {
  background: var(--primary-color);
}

.sort-name {
  font-size: 24rpx;
  color: var(--text-light);
  white-space: nowrap;
}

.sort-item.active .sort-name {
  color: var(--white);
}

.sort-arrow {
  width: 20rpx;
  height: 20rpx;
}

/* 活动列表 */
.activity-list {
  padding: 24rpx;
}

.activity-item {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: var(--shadow);
  display: flex;
}

.activity-image {
  width: 200rpx;
  height: 200rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.activity-header {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.activity-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.activity-subtitle {
  font-size: 24rpx;
  color: var(--text-light);
}

.activity-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.price {
  font-size: 36rpx;
  color: #FF3333;
  font-weight: bold;
}

.price-original {
  font-size: 24rpx;
  color: var(--text-lighter);
  text-decoration: line-through;
}

.discount {
  background: #FF3333;
  color: var(--white);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.activity-tags {
  display: flex;
  gap: 8rpx;
}

.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: var(--white);
}

.tag-red {
  background: #FF3333;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 24rpx;
  color: var(--text-light);
}

.shop-name {
  font-weight: 500;
}

.activity-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.end-time {
  font-size: 24rpx;
  color: #FF3333;
}

.buy-btn {
  background: var(--primary-color);
  color: var(--white);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-lighter);
  font-size: 28rpx;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: var(--text-lighter);
  font-size: 28rpx;
}