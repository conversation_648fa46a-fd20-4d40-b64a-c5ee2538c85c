/* 活动页面样式 */
.activity-page {
  background: #F5F5F5;
  min-height: 100vh;
}

/* 顶部区域 */
.header {
  background: linear-gradient(135deg, #FF6600 0%, #FF8C42 100%);
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.city-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  min-width: 120rpx;
}

.city-name {
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
}

.city-arrow {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

/* 搜索栏 */
.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border-radius: 40rpx;
  height: 80rpx;
  padding: 0 32rpx;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.search-placeholder {
  color: #999999;
}

.search-btn {
  background: #FF6600;
  color: #FFFFFF;
  padding: 8rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.search-btn-text {
  color: #FFFFFF;
}

/* 品牌推广区域 */
.brand-section {
  background: #FFFFFF;
  margin: 16rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
}

.brand-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.brand-logo {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  object-fit: cover;
}

.brand-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

/* 分类标签栏 */
.category-section {
  background: #FFFFFF;
  padding: 0 32rpx;
  border-bottom: 2rpx solid #E5E5E5;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 48rpx;
}

.category-item {
  padding: 32rpx 0;
  position: relative;
}

.category-name {
  font-size: 28rpx;
  color: #666666;
  white-space: nowrap;
}

.category-item.active .category-name {
  color: #FF6600;
  font-weight: 500;
}

.category-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #FF6600;
  border-radius: 2rpx;
}

/* 筛选功能区 */
.filter-section {
  background: #FFFFFF;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #E5E5E5;
  height: 88rpx;
  display: flex;
  align-items: center;
}

.filter-list {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  justify-content: center;
}

.filter-name {
  font-size: 28rpx;
  color: #666666;
  white-space: nowrap;
}

.filter-arrow {
  width: 20rpx;
  height: 20rpx;
  filter: brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(60%) contrast(100%);
}

/* 活动列表 */
.activity-list {
  padding: 24rpx 32rpx;
}

.activity-item {
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  height: 200rpx;
  padding: 24rpx;
}

.activity-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  padding-left: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.activity-subtitle {
  font-size: 24rpx;
  color: #666666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.activity-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin: 4rpx 0;
}

.price-current {
  font-size: 32rpx;
  color: #FF3333;
  font-weight: bold;
}

.price-original {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}

.activity-tags {
  display: flex;
  gap: 8rpx;
  margin: 4rpx 0;
}

.tag {
  display: inline-block;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.tag-yellow {
  background: #FFD100;
  color: #333333;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 24rpx;
  color: #666666;
  margin-top: auto;
}

.shop-name {
  font-weight: 500;
}

.distance {
  color: #999999;
}

.activity-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
}

.grab-btn {
  background: #FF3333;
  width: 120rpx;
  height: 64rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grab-btn-text {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: bold;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: #999999;
  font-size: 28rpx;
}