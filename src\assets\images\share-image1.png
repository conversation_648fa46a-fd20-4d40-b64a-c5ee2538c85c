<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="shareGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4ECDC4;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="150" fill="url(#shareGradient1)"/>
  <circle cx="100" cy="60" r="25" fill="#FFFFFF" opacity="0.8"/>
  <rect x="60" y="90" width="80" height="40" rx="8" fill="#FFFFFF" opacity="0.9"/>
  <text x="100" y="105" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#333333">美食分享</text>
  <text x="100" y="120" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#666666">超值优惠</text>
</svg>