/**
 * 美团联盟API测试文件
 * 用于测试修复后的API调用功能
 */

const apiService = require('../src/utils/apiService')

// 测试配置
const testConfig = {
  // 测试位置（广州）
  latitude: 23.129163,
  longitude: 113.264435,
  
  // 测试参数
  pageSize: 10,
  searchKeyword: '奶茶'
}

/**
 * 测试获取推荐商品
 */
async function testGetRecommendProducts() {
  console.log('\n=== 测试获取推荐商品 ===')
  
  try {
    const response = await apiService.getRecommendProducts(1, testConfig.pageSize)
    
    console.log('✅ 获取推荐商品成功')
    console.log('响应码:', response.code)
    console.log('商品数量:', response.data ? response.data.length : 0)
    console.log('是否有更多:', response.hasNext)
    
    if (response.data && response.data.length > 0) {
      console.log('第一个商品:', {
        name: response.data[0].couponPackDetail?.name,
        price: response.data[0].couponPackDetail?.sellPrice,
        originalPrice: response.data[0].couponPackDetail?.originalPrice
      })
    }
    
    return true
  } catch (error) {
    console.error('❌ 获取推荐商品失败:', error.message)
    return false
  }
}

/**
 * 测试搜索商品
 */
async function testSearchProducts() {
  console.log('\n=== 测试搜索商品 ===')
  
  try {
    const response = await apiService.searchProducts(testConfig.searchKeyword, 1, testConfig.pageSize)
    
    console.log('✅ 搜索商品成功')
    console.log('搜索关键词:', testConfig.searchKeyword)
    console.log('响应码:', response.code)
    console.log('商品数量:', response.data ? response.data.length : 0)
    
    return true
  } catch (error) {
    console.error('❌ 搜索商品失败:', error.message)
    return false
  }
}

/**
 * 测试查询商品券（直接调用）
 */
async function testQueryCoupon() {
  console.log('\n=== 测试查询商品券 ===')
  
  try {
    // 测试使用榜单ID
    const response1 = await apiService.queryCoupon(testConfig.latitude, testConfig.longitude, {
      pageNo: 1,
      pageSize: 5,
      listTopiId: 'hot_sale'
    })
    
    console.log('✅ 使用榜单ID查询成功')
    console.log('响应码:', response1.code)
    console.log('商品数量:', response1.data ? response1.data.length : 0)
    
    // 测试使用搜索关键词
    const response2 = await apiService.queryCoupon(testConfig.latitude, testConfig.longitude, {
      pageNo: 1,
      pageSize: 5,
      searchText: '咖啡'
    })
    
    console.log('✅ 使用搜索关键词查询成功')
    console.log('响应码:', response2.code)
    console.log('商品数量:', response2.data ? response2.data.length : 0)
    
    return true
  } catch (error) {
    console.error('❌ 查询商品券失败:', error.message)
    return false
  }
}

/**
 * 测试获取推广链接
 */
async function testGetReferralLink() {
  console.log('\n=== 测试获取推广链接 ===')
  
  try {
    // 使用测试活动ID
    const response = await apiService.getReferralLink('test_act_id', 1)
    
    console.log('✅ 获取推广链接成功')
    console.log('响应码:', response.code)
    console.log('推广链接:', response.data)
    
    return true
  } catch (error) {
    console.error('❌ 获取推广链接失败:', error.message)
    // 这个可能会失败，因为需要真实的活动ID
    return false
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行美团联盟API测试')
  console.log('测试配置:', testConfig)
  
  const results = []
  
  // 运行各项测试
  results.push(await testGetRecommendProducts())
  results.push(await testSearchProducts())
  results.push(await testQueryCoupon())
  results.push(await testGetReferralLink())
  
  // 统计结果
  const successCount = results.filter(r => r).length
  const totalCount = results.length
  
  console.log('\n📊 测试结果统计:')
  console.log(`成功: ${successCount}/${totalCount}`)
  console.log(`失败: ${totalCount - successCount}/${totalCount}`)
  
  if (successCount === totalCount) {
    console.log('🎉 所有测试通过！')
  } else if (successCount > 0) {
    console.log('⚠️ 部分测试通过，请检查失败的测试项')
  } else {
    console.log('💥 所有测试失败，请检查API配置和网络连接')
  }
}

// 导出测试函数
module.exports = {
  testGetRecommendProducts,
  testSearchProducts,
  testQueryCoupon,
  testGetReferralLink,
  runAllTests
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests().catch(console.error)
}