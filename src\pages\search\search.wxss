/* 搜索页面样式 */
.search-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 搜索头部 */
.search-header {
  background: var(--white);
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  align-items: center;
  background: var(--bg-color);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  gap: 16rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-color);
}

.search-btn {
  background: var(--primary-color);
  color: var(--white);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 搜索建议 */
.search-suggest {
  padding: 24rpx;
}

/* 区域标题 */
.section-title {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 24rpx;
}

/* 搜索历史 */
.history-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.history-item {
  background: var(--white);
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
  border: 1rpx solid var(--border-color);
}

.history-text {
  font-size: 26rpx;
  color: var(--text-light);
}

/* 热门搜索 */
.hot-section {
  margin-bottom: 40rpx;
}

.hot-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-item {
  background: var(--white);
  padding: 16rpx 24rpx;
  border-radius: 32rpx;
}

.hot-text {
  font-size: 26rpx;
  color: var(--text-color);
}

/* 搜索结果 */
.search-results {
  padding: 24rpx;
}

.result-header {
  margin-bottom: 24rpx;
}

.result-count {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 商品列表 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.product-item {
  display: flex;
  background: var(--white);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: var(--shadow);
  gap: 24rpx;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.product-title {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.price {
  font-size: 32rpx;
  color: #FF3333;
  font-weight: bold;
}

.price-original {
  font-size: 24rpx;
  color: var(--text-lighter);
  text-decoration: line-through;
}

.product-tags {
  display: flex;
  gap: 8rpx;
}

.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: var(--white);
}

.tag-primary {
  background: var(--secondary-color);
  color: var(--text-color);
}

.product-shop {
  font-size: 24rpx;
  color: var(--text-light);
  margin-top: auto;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-lighter);
  font-size: 28rpx;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: var(--text-lighter);
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.empty-tip {
  color: var(--text-lighter);
  font-size: 24rpx;
}