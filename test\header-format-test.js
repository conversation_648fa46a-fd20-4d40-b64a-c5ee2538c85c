/**
 * Header格式测试
 * 专门测试S-Ca-Signature-Headers的格式问题
 */

console.log('=== Header格式测试 ===\n')

try {
  // 直接测试API服务的request方法
  const ApiService = require('../src/utils/api-service-fixed.js')
  
  // 模拟wx.request来捕获实际发送的headers
  global.wx = {
    request: function(options) {
      console.log('🔍 实际发送的Headers:')
      Object.keys(options.header).forEach(key => {
        console.log(`- ${key}: "${options.header[key]}"`)
        if (key === 'S-Ca-Signature-Headers') {
          const value = options.header[key]
          console.log(`  格式检查: ${value.includes(',') ? '✅ 包含逗号' : '❌ 缺少逗号'}`)
          console.log(`  期望格式: "S-Ca-App,S-Ca-Timestamp"`)
          console.log(`  实际格式: "${value}"`)
          console.log(`  是否正确: ${value === 'S-Ca-App,S-Ca-Timestamp' ? '✅' : '❌'}`)
        }
      })
      
      // 不执行实际请求，直接返回
      setTimeout(() => {
        options.success({
          statusCode: 200,
          data: { code: 0, message: 'test' },
          header: { 'Date': new Date().toUTCString() }
        })
      }, 10)
    }
  }
  
  // 模拟getApp
  global.getApp = function() {
    return {
      globalData: {
        location: { latitude: 39.928, longitude: 116.404 }
      }
    }
  }
  
  console.log('📋 测试API请求中的Header格式...\n')
  
  // 发起一个测试请求
  ApiService.queryCoupon(39.928, 116.404, {
    pageNo: 1,
    pageSize: 5,
    listTopiId: 'hot_sale'
  }).then(() => {
    console.log('\n✅ Header格式测试完成')
  }).catch(error => {
    console.log('\n❌ 测试出错:', error.message)
  })
  
} catch (error) {
  console.error('❌ 测试失败:', error)
}