/* 全局样式 */
:root {
  --primary-color: #FF6600;
  --secondary-color: #FFD100;
  --meituan-yellow: #FFD100;
  --meituan-orange: #FF6600;
  --meituan-orange-light: #FF8C42;
  --text-color: #333333;
  --text-light: #666666;
  --text-lighter: #999999;
  --bg-color: #F5F5F5;
  --white: #FFFFFF;
  --border-color: #E5E5E5;
  --shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-card: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 12rpx 32rpx rgba(0, 0, 0, 0.16);
}

/* 基础样式重置 */
page {
  background-color: var(--bg-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
}

/* 通用布局类 */
.container {
  padding: 0 24rpx;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 卡片样式 */
.card {
  background: var(--white);
  border-radius: 16rpx;
  box-shadow: var(--shadow);
  margin-bottom: 24rpx;
}

/* 按钮样式 */
.btn {
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-secondary {
  background: var(--white);
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-light {
  color: var(--text-light);
}

.text-lighter {
  color: var(--text-lighter);
}

.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

/* 价格样式 */
.price {
  color: #FF3333;
  font-weight: bold;
}

.price-original {
  color: var(--text-lighter);
  text-decoration: line-through;
  font-size: 24rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: var(--white);
}

.tag-primary {
  background: var(--primary-color);
}

.tag-secondary {
  background: var(--secondary-color);
  color: var(--text-color);
}

.tag-red {
  background: #FF3333;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-lighter);
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: var(--text-lighter);
  font-size: 28rpx;
}

/* 瀑布流布局 */
.waterfall {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 24rpx;
}

.waterfall-item {
  width: 48%;
  margin-bottom: 24rpx;
}

/* 商品卡片 */
.product-card {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.product-image {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
}

.product-info {
  padding: 24rpx;
}

.product-title {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.product-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.product-shop {
  font-size: 24rpx;
  color: var(--text-light);
}