# 美团联盟API文档

## 📋 目录结构

- **[快速开始](#快速开始)** - 5分钟快速接入
- **[API接口](#api接口)** - 核心接口说明
- **[认证签名](#认证签名)** - 签名算法详解
- **[错误处理](#错误处理)** - 常见错误及解决方案
- **[代码示例](#代码示例)** - 完整实现示例

---

## 🚀 快速开始

### 账户配置

```javascript
const config = {
  appkey: '9498b0824d214ee4b65bfab1be6dbed0',
  secret: '2a1463cb8f364cafbfd8d2a19c48eb48',
  mediaId: '1000304354',
  allianceId: '1904722314357399633',
  sid: 'yjd2025'
}
```

### 基础URL
```
https://media.meituan.com/cps_open/common/api/v1
```

---

## 🔌 API接口

### 1. 商品查询接口

**接口地址**: `/query_coupon`

**核心参数**:
```javascript
{
  "latitude": 39928000,      // 纬度*100万
  "longitude": 116404000,    // 经度*100万
  "pageNo": 1,
  "pageSize": 20,
  
  // 以下四个参数必须至少包含一个
  "listTopiId": "hot_sale",  // 榜单ID (推荐)
  "searchText": "奶茶",      // 搜索关键词
  "vpSkuViewIds": ["ID1"],   // 商品ID列表
  "multipleSupplyList": []   // 多供给列表
}
```

**响应数据**:
```javascript
{
  "code": 0,
  "data": [{
    "brandInfo": {
      "brandName": "茶百道",
      "brandLogoUrl": "http://..."
    },
    "couponPackDetail": {
      "name": "双杯酸奶套餐兑换券",
      "skuViewId": "MCVKOBE3ASOKPPPKLMRE12345",
      "originalPrice": "100.00",
      "sellPrice": "50.00"
    },
    "commissionInfo": {
      "commissionPercent": "1000"  // 除以100得到实际比例
    }
  }],
  "hasNext": true
}
```

### 2. 获取推广链接接口

**接口地址**: `/get_referral_link`

**核心参数**:
```javascript
{
  "actId": "1234",           // 活动ID (三选一)
  "skuViewId": "SKU123",     // 商品ID (三选一)  
  "text": "http://...",      // 目标链接 (三选一)
  "linkType": 1,             // 1:长链接 2:短链接
  "sid": "yjd2025"
}
```

**响应数据**:
```javascript
{
  "code": 0,
  "message": "成功",
  "data": "https://click.meitan.com/t?t=1&c=2&p=XXXXXXX",
  "referralLinkMap": {
    "1": "https://click.meitan.com/t?t=1&c=2&p=XXXXXXX",
    "2": "http://dpurl.cn/XXXXXXXX"
  }
}
```

---

## 🔐 认证签名

### 必需请求头

```javascript
{
  'Content-Type': 'application/json;charset=utf-8',
  'S-Ca-App': appkey,
  'S-Ca-Timestamp': timestamp,
  'S-Ca-Signature': signature,
  'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
  'Content-MD5': contentMD5
}
```

### 签名生成步骤

```javascript
// 1. 生成Content-MD5
const bodyStr = JSON.stringify(requestData)
const md5Hash = md5(bodyStr)
const md5Bytes = hexToBytes(md5Hash)
const contentMD5 = base64Encode(md5Bytes)

// 2. 构建签名字符串
const timestamp = Date.now().toString()
const headers = `S-Ca-App:${appkey}\nS-Ca-Timestamp:${timestamp}\n`
const endpoint = `/cps_open/common/api/v1/query_coupon`
const stringToSign = `POST\n${contentMD5}\n${headers}${endpoint}`

// 3. 生成HMAC-SHA256签名
const hmacResult = hmacSHA256(stringToSign, secret)
const signatureBytes = hexToBytes(hmacResult)
const signature = base64Encode(signatureBytes)
```

---

## ❌ 错误处理

### 常见错误码

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 400 | body md5值不一致 | 检查MD5计算方法 |
| 400 | 请求签名验证失败 | 检查签名生成算法 |
| 400 | 请求时间戳已过期 | 使用当前时间戳 |
| 1 | 物料ID未查询到相关数据 | 检查actId是否正确 |

### 错误处理示例

```javascript
try {
  const response = await apiService.getRecommendProducts()
  // 处理成功结果
} catch (error) {
  console.error('API调用失败:', error)
  
  if (error.message.includes('md5值不一致')) {
    // MD5计算错误，检查算法实现
  } else if (error.message.includes('签名验证失败')) {
    // 签名错误，检查签名生成
  } else {
    // 其他错误处理
    wx.showToast({ title: '请求失败，请重试', icon: 'none' })
  }
}
```

---

## 💻 代码示例

### 完整API服务类

```javascript
class MeituanApiService {
  constructor() {
    this.config = {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      secret: '2a1463cb8f364cafbfd8d2a19c48eb48',
      sid: 'yjd2025'
    }
  }

  // 获取推荐商品
  async getRecommendProducts(page = 1, pageSize = 20) {
    const location = await this.getUserLocation()
    return await this.queryCoupon(location.latitude, location.longitude, {
      pageNo: page,
      pageSize,
      listTopiId: 'hot_sale'
    })
  }

  // 搜索商品
  async searchProducts(keyword, page = 1, pageSize = 20) {
    const location = await this.getUserLocation()
    return await this.queryCoupon(location.latitude, location.longitude, {
      pageNo: page,
      pageSize,
      searchText: keyword
    })
  }

  // 获取推广链接
  async getReferralLink(actId, linkType = 1) {
    return await this.request('/get_referral_link', {
      actId,
      linkType,
      sid: this.config.sid
    })
  }

  // 核心请求方法
  async request(endpoint, data = {}) {
    const url = `${this.config.baseUrl}${endpoint}`
    const { signature, timestamp, contentMD5 } = this.generateSignature('POST', endpoint, data)

    const headers = {
      'Content-Type': 'application/json;charset=utf-8',
      'S-Ca-App': this.config.appkey,
      'S-Ca-Timestamp': timestamp,
      'S-Ca-Signature': signature,
      'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
      'Content-MD5': contentMD5
    }

    return new Promise((resolve, reject) => {
      wx.request({
        url, method: 'POST', data, header: headers,
        success: (res) => {
          if (res.statusCode === 200 && res.data.code === 0) {
            resolve(res.data)
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        },
        fail: reject
      })
    })
  }

  // 签名生成 (需要配合crypto工具类)
  generateSignature(method, endpoint, body) {
    const timestamp = Date.now().toString()
    const bodyStr = typeof body === 'object' ? JSON.stringify(body) : body
    const contentMD5 = crypto.generateContentMD5(bodyStr)
    const headers = `S-Ca-App:${this.config.appkey}\nS-Ca-Timestamp:${timestamp}\n`
    const fullEndpoint = `/cps_open/common/api/v1${endpoint}`
    const stringToSign = `${method}\n${contentMD5}\n${headers}${fullEndpoint}`
    const signature = crypto.hmacSHA256(stringToSign, this.config.secret)
    const signatureBytes = crypto.hexToBytes(signature)
    const base64Signature = crypto.base64Encode(signatureBytes)

    return { signature: base64Signature, timestamp, contentMD5 }
  }

  // 获取用户位置
  async getUserLocation() {
    return new Promise((resolve) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => resolve({
          latitude: res.latitude,
          longitude: res.longitude
        }),
        fail: () => resolve({
          latitude: 23.129163,  // 默认广州
          longitude: 113.264435
        })
      })
    })
  }
}

// 使用示例
const apiService = new MeituanApiService()

// 获取推荐商品
const products = await apiService.getRecommendProducts(1, 20)

// 搜索商品
const searchResults = await apiService.searchProducts('奶茶', 1, 20)

// 获取推广链接
const link = await apiService.getReferralLink('actId123', 1)
```

### 小程序页面使用

```javascript
// pages/index/index.js
Page({
  data: { products: [], loading: false },
  
  async onLoad() {
    await this.loadProducts()
  },
  
  async loadProducts() {
    try {
      this.setData({ loading: true })
      const response = await apiService.getRecommendProducts(1, 20)
      this.setData({ products: response.data || [] })
    } catch (error) {
      console.error('加载失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  }
})
```

---

## 📝 注意事项

1. **参数要求**: 商品查询接口必须包含 `vpSkuViewIds`、`listTopiId`、`searchText`、`multipleSupplyList` 中的至少一个
2. **经纬度格式**: 需要乘以100万传递（如39.928度 → 39928000）
3. **时间戳有效期**: 请求时间戳有效时间为2分钟
4. **佣金比例**: 返回值需要除以100得到实际比例（如1000 → 10%）
5. **重试机制**: 建议实现指数退避的重试策略

---

## 🔗 相关链接

- [美团联盟官方文档](https://page.meituan.net)
- [项目源码](../src/)
- [测试工具](../test/)

---

**最后更新**: 2025年1月24日  
**状态**: ✅ 已验证可用