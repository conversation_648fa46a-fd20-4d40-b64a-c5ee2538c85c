/**
 * 美团API终极修复版本
 * 完全重新实现，包含详细调试信息
 */

const crypto = require('./crypto-fixed-final.js')
const timeCorrection = require('./time-correction.js')

class MeituanApiService {
  constructor() {
    this.config = {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      // 请在这里更新您的真实API密钥
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
    }
    
    console.log('🚀 美团API终极修复版本初始化')
    console.log('⚠️  请确认API密钥是否为最新版本！')
    
    // 验证配置格式
    this.validateConfig()
  }

  validateConfig() {
    console.log('🔍 验证API配置格式:')
    
    const appkeyValid = /^[a-f0-9]{32}$/.test(this.config.appkey)
    const secretValid = /^[a-f0-9]{32}$/.test(this.config.secret)
    
    console.log(`AppKey格式: ${appkeyValid ? '✅' : '❌'} (${this.config.appkey})`)
    console.log(`Secret格式: ${secretValid ? '✅' : '❌'} (${this.config.secret.substring(0, 8)}...)`)
    
    if (!appkeyValid || !secretValid) {
      console.error('❌ API密钥格式不正确！请检查配置')
      throw new Error('API密钥格式不正确')
    }
  }

  generateTimestamp() {
    // 使用修正后的时间
    const correctedTime = timeCorrection.getCorrectedTimestamp()
    const timestamp = Math.floor(correctedTime)
    
    console.log('⏰ 时间戳生成:', {
      原始系统时间: new Date().toISOString(),
      修正后时间: new Date(correctedTime).toISOString(),
      最终时间戳: timestamp,
      时间戳长度: timestamp.toString().length,
      是否13位: timestamp.toString().length === 13
    })
    
    return timestamp
  }

  generateContentMD5(bodyStr) {
    console.log('📝 Content-MD5计算:')
    console.log('输入字符串:', bodyStr)
    console.log('字符串长度:', bodyStr.length)
    
    // 使用crypto模块计算MD5
    const result = crypto.generateContentMD5(bodyStr)
    
    console.log('MD5结果:', result)
    console.log('MD5长度:', result.length)
    
    return result
  }

  buildSignatureString(method, contentMD5, timestamp, endpoint) {
    // 严格按照美团官方文档构建
    const headers = `S-Ca-App:${this.config.appkey}\nS-Ca-Timestamp:${timestamp}\n`
    const url = `/cps_open/common/api/v1${endpoint}`
    const stringToSign = `${method}\n${contentMD5}\n${headers}${url}`
    
    console.log('🔐 签名字符串构建:')
    console.log('Method:', method)
    console.log('Content-MD5:', contentMD5)
    console.log('Headers:', headers.replace(/\n/g, '\\n'))
    console.log('URL:', url)
    console.log('完整签名字符串:')
    console.log(stringToSign.replace(/\n/g, '\\n'))
    console.log('签名字符串长度:', stringToSign.length)
    
    return stringToSign
  }

  generateSignature(method, endpoint, data) {
    console.log('🔒 开始生成签名')
    
    // 1. 生成时间戳
    const timestamp = this.generateTimestamp()
    
    // 2. 准备请求体
    const bodyStr = typeof data === 'object' ? JSON.stringify(data) : (data || '')
    console.log('请求体:', bodyStr)
    
    // 3. 计算Content-MD5
    const contentMD5 = this.generateContentMD5(bodyStr)
    
    // 4. 构建签名字符串
    const stringToSign = this.buildSignatureString(method, contentMD5, timestamp, endpoint)
    
    // 5. 计算HMAC-SHA256
    console.log('🔑 HMAC-SHA256计算:')
    console.log('密钥:', this.config.secret.substring(0, 8) + '...')
    
    const hmacResult = crypto.hmacSHA256(stringToSign, this.config.secret)
    console.log('HMAC结果:', hmacResult)
    console.log('HMAC长度:', hmacResult.length)
    
    // 6. 转换为字节数组并Base64编码
    const signatureBytes = crypto.hexToBytes(hmacResult)
    const signature = crypto.base64Encode(signatureBytes)
    
    console.log('最终签名:', signature)
    console.log('签名长度:', signature.length)
    
    return {
      signature,
      timestamp: timestamp.toString(),
      contentMD5
    }
  }

  buildRequestHeaders(signatureResult) {
    const headers = {
      'Content-Type': 'application/json;charset=utf-8',
      'S-Ca-App': this.config.appkey,
      'S-Ca-Timestamp': signatureResult.timestamp,
      'S-Ca-Signature': signatureResult.signature,
      'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
      'Content-MD5': signatureResult.contentMD5
    }
    
    console.log('📋 请求头构建:')
    Object.entries(headers).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`)
    })
    
    // 验证请求头格式
    const validations = {
      'Content-Type': headers['Content-Type'] === 'application/json;charset=utf-8',
      'S-Ca-App长度': headers['S-Ca-App'].length === 32,
      'S-Ca-Timestamp格式': /^\d{13}$/.test(headers['S-Ca-Timestamp']),
      'S-Ca-Signature长度': headers['S-Ca-Signature'].length === 44,
      'S-Ca-Signature-Headers': headers['S-Ca-Signature-Headers'] === 'S-Ca-App,S-Ca-Timestamp',
      'Content-MD5长度': headers['Content-MD5'].length === 24
    }
    
    console.log('🔍 请求头验证:')
    Object.entries(validations).forEach(([check, result]) => {
      console.log(`  ${check}: ${result ? '✅' : '❌'}`)
    })
    
    return headers
  }

  async request(endpoint, data = {}) {
    console.log('🚀 开始API请求')
    console.log('端点:', endpoint)
    console.log('数据:', data)
    
    return new Promise((resolve, reject) => {
      try {
        // 生成签名
        const signatureResult = this.generateSignature('POST', endpoint, data)
        
        // 构建请求头
        const headers = this.buildRequestHeaders(signatureResult)
        
        // 构建完整URL
        const url = `${this.config.baseUrl}${endpoint}`
        console.log('📡 请求URL:', url)
        
        // 发送请求
        wx.request({
          url: url,
          method: 'POST',
          header: headers,
          data: data,
          success: (response) => {
            console.log('📥 收到响应:')
            console.log('状态码:', response.statusCode)
            console.log('响应头:', response.header)
            console.log('响应数据:', response.data)
            
            if (response.statusCode === 200) {
              const result = response.data
              if (result.code === 0) {
                console.log('✅ API调用成功!')
                resolve(result)
              } else {
                console.error('❌ API返回错误:', result.message)
                
                // 如果是签名验证失败，提供详细的解决建议
                if (result.message && result.message.includes('签名验证失败')) {
                  console.log('🔍 签名验证失败解决建议:')
                  console.log('1. 🔑 检查API密钥:')
                  console.log('   - 登录美团联盟后台重新获取AppKey和Secret')
                  console.log('   - 确保复制时没有多余的空格或字符')
                  console.log('   - 当前AppKey:', this.config.appkey)
                  console.log('')
                  console.log('2. 📋 检查账户状态:')
                  console.log('   - 确认账户已完成实名认证')
                  console.log('   - 确认应用审核状态为"通过"')
                  console.log('   - 检查是否有权限限制')
                  console.log('')
                  console.log('3. 🌐 检查网络环境:')
                  console.log('   - 确认网络连接正常')
                  console.log('   - 检查是否有代理或防火墙限制')
                  console.log('')
                  console.log('4. 📞 联系技术支持:')
                  console.log('   - 如果以上都正常，请联系美团联盟技术支持')
                  console.log('   - 提供AppKey和详细的错误日志')
                }
                
                reject(new Error(result.message))
              }
            } else {
              console.error('❌ HTTP错误:', response.statusCode)
              reject(new Error(`HTTP ${response.statusCode}`))
            }
          },
          fail: (error) => {
            console.error('❌ 请求失败:', error)
            reject(new Error('网络请求失败'))
          }
        })
        
      } catch (error) {
        console.error('❌ 请求准备失败:', error)
        reject(error)
      }
    })
  }

  // 查询优惠券
  async queryCoupon(params) {
    return this.request('/query_coupon', params)
  }

  // 获取推荐商品
  async getRecommendProducts(params) {
    return this.request('/query_coupon', params)
  }
}

module.exports = new MeituanApiService()
