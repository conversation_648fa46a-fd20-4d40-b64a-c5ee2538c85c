/**
 * 签名调试测试
 * 专门用于调试签名验证失败的问题
 */

// 模拟微信小程序环境
global.wx = {
  request: function(options) {
    console.log('\n=== 详细请求信息 ===')
    console.log('URL:', options.url)
    console.log('Method:', options.method)
    console.log('Headers:', JSON.stringify(options.header, null, 2))
    console.log('Data:', JSON.stringify(options.data, null, 2))
    
    // 模拟签名验证失败的响应
    setTimeout(() => {
      options.success({
        statusCode: 200,
        data: { 
          code: 400, 
          message: '请求签名验证失败',
          debug: {
            receivedSignature: options.header['S-Ca-Signature'],
            receivedTimestamp: options.header['S-Ca-Timestamp'],
            receivedContentMD5: options.header['Content-MD5']
          }
        },
        header: { 'Date': new Date().toUTCString() }
      })
    }, 100)
  }
}

// 模拟getApp函数
global.getApp = function() {
  return {
    globalData: {
      location: { latitude: 39.928, longitude: 116.404 }
    }
  }
}

console.log('=== 签名调试测试 ===\n')

try {
  // 加载API服务
  const ApiService = require('../src/utils/api-service-fixed.js')
  console.log('✅ API服务加载成功\n')

  // 测试签名生成的每个步骤
  console.log('📋 详细签名生成过程:')
  
  // 1. 测试数据
  const testData = {
    latitude: 39928000,
    longitude: 116404000,
    pageNo: 1,
    pageSize: 5,
    listTopiId: 'hot_sale'
  }
  
  console.log('1. 测试数据:', JSON.stringify(testData, null, 2))
  
  // 2. 生成签名
  const signature = ApiService.generateSignature('POST', '/query_coupon', testData)
  console.log('\n2. 签名生成结果:')
  console.log('- 时间戳:', signature.timestamp)
  console.log('- 时间戳长度:', signature.timestamp.length, '位')
  console.log('- Content-MD5:', signature.contentMD5)
  console.log('- 签名:', signature.signature)
  console.log('- 签名长度:', signature.signature.length)
  
  // 3. 验证签名字符串构建
  console.log('\n3. 手动验证签名字符串构建:')
  const crypto = require('../src/utils/crypto-fixed-final.js')
  
  const bodyStr = JSON.stringify(testData)
  console.log('- Body字符串:', bodyStr)
  
  const contentMD5 = crypto.generateContentMD5(bodyStr)
  console.log('- Content-MD5:', contentMD5)
  
  const headers = `S-Ca-App:9498b0824d214ee4b65bfab1be6dbed0\nS-Ca-Timestamp:${signature.timestamp}\n`
  console.log('- Headers字符串:', JSON.stringify(headers))
  
  const stringToSign = `POST\n${contentMD5}\n${headers}/cps_open/common/api/v1/query_coupon`
  console.log('- 完整签名字符串:')
  console.log(stringToSign)
  
  // 4. 验证HMAC计算
  console.log('\n4. HMAC计算验证:')
  const secret = '2a1463cb8f364cafbfd8d2a19c48eb48'
  const hmacResult = crypto.hmacSHA256(stringToSign, secret)
  console.log('- HMAC结果 (hex):', hmacResult)
  
  const signatureBytes = crypto.hexToBytes(hmacResult)
  console.log('- 转换为字节数组长度:', signatureBytes.length)
  
  const base64Signature = crypto.base64Encode(signatureBytes)
  console.log('- Base64签名:', base64Signature)
  console.log('- 是否匹配:', base64Signature === signature.signature ? '✅' : '❌')
  
  // 5. 发送测试请求
  console.log('\n5. 发送测试请求:')
  ApiService.queryCoupon(39.928, 116.404, {
    pageNo: 1,
    pageSize: 5,
    listTopiId: 'hot_sale'
  }).then(result => {
    console.log('✅ 请求成功:', result)
  }).catch(error => {
    console.log('❌ 请求失败:', error.message)
    
    // 分析可能的问题
    console.log('\n🔍 问题分析:')
    console.log('1. 检查时间戳格式是否正确（应该是13位毫秒）')
    console.log('2. 检查Content-MD5计算是否正确')
    console.log('3. 检查签名字符串格式是否符合美团要求')
    console.log('4. 检查HMAC-SHA256计算是否正确')
    console.log('5. 检查Base64编码是否正确')
  })
  
} catch (error) {
  console.error('❌ 测试失败:', error)
  console.error('错误堆栈:', error.stack)
}