<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="activityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6600;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFD100;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  <rect width="300" height="200" fill="url(#activityGradient)"/>
  <rect x="20" y="20" width="260" height="160" rx="8" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="150" cy="80" r="30" fill="#FF6600" opacity="0.2"/>
  <text x="150" y="85" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#FF6600">活动</text>
  <text x="150" y="105" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#666666">精选优惠</text>
  <rect x="40" y="140" width="80" height="20" rx="10" fill="#FF6600"/>
  <text x="80" y="153" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#FFFFFF">限时特惠</text>
  <rect x="180" y="140" width="80" height="20" rx="10" fill="#FFD100"/>
  <text x="220" y="153" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#FFFFFF">立即抢购</text>
</svg>