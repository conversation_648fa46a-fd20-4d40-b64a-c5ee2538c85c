<!--城市选择器组件-->
<view class="city-selector">
  <!-- 城市显示区域 -->
  <view class="city-display" bindtap="showCityModal">
    <text class="city-name">{{currentCity}}</text>
    <image wx:if="{{showArrow}}" class="city-arrow" src="/assets/icons/arrow-down.png" />
  </view>

  <!-- 城市选择弹窗 -->
  <view class="city-modal" wx:if="{{showModal}}" bindtap="hideCityModal">
    <view class="modal-content" catchtap="stopPropagation">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">选择城市</text>
        <view class="close-btn" bindtap="hideCityModal">
          <image class="close-icon" src="/assets/icons/close.png" />
        </view>
      </view>

      <!-- 当前定位 -->
      <view class="location-section">
        <view class="section-title">当前定位</view>
        <view class="location-item" bindtap="getCurrentLocation">
          <image class="location-icon" src="/assets/icons/location.png" />
          <text class="location-text">重新定位</text>
        </view>
      </view>

      <!-- 热门城市 -->
      <view class="hot-cities-section">
        <view class="section-title">热门城市</view>
        <view class="hot-cities-grid">
          <view class="hot-city-item" 
                wx:for="{{hotCities}}" 
                wx:key="*this"
                data-city="{{item}}"
                bindtap="selectHotCity">
            <text class="hot-city-name">{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 全部城市 -->
      <view class="all-cities-section">
        <view class="section-title">全部城市</view>
        <scroll-view class="cities-scroll" scroll-y>
          <view class="city-group" wx:for="{{allCities}}" wx:key="letter">
            <view class="group-letter">{{item.letter}}</view>
            <view class="group-cities">
              <view class="city-item" 
                    wx:for="{{item.cities}}" 
                    wx:for-item="city"
                    wx:key="*this"
                    data-city="{{city}}"
                    bindtap="selectCity">
                <text class="city-item-name">{{city}}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</view>
