/**
 * 美团联盟API服务类 - 最终修复版
 * 完全符合美团官方文档要求
 */

const crypto = require('./crypto-fixed-final.js')
const cacheManager = require('./cacheManager.js')
const meituanTimeFix = require('./meituan-time-fix-official.js')

class ApiService {
  constructor() {
    this.config = {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      secret: '2a1463cb8f364cafbfd8d2a19c48eb48',
      mediaId: '1000304354',
      allianceId: '1904722314357399633',
      sid: 'yjd2025'
    }
    this.retryTimes = 3
    this.retryDelay = 1000

    this.initTimeSync()
  }

  initTimeSync() {
    if (typeof wx !== 'undefined') {
      setTimeout(() => {
        console.log('初始化美团时间同步')
      }, 100)
    }
  }

  generateContentMD5(body) {
    let bodyStr = ''
    if (body && typeof body === 'object' && Object.keys(body).length > 0) {
      bodyStr = JSON.stringify(body)
    } else if (body && typeof body === 'string' && body.trim() !== '') {
      bodyStr = body
    }

    console.log('Content-MD5 输入:', bodyStr)

    // 直接使用crypto模块的generateContentMD5方法
    // 该方法已经处理了小程序兼容性和UTF-8编码
    const result = crypto.generateContentMD5(bodyStr)

    console.log('Content-MD5 结果:', result)

    return result
  }

  generateTimestamp() {
    // 获取美团时间戳
    const timestamp = meituanTimeFix.getMeituanTimestamp()
    const currentTime = Date.now()

    // 记录详细的时间戳生成信息
    console.log('生成美团时间戳:', {
      timestamp: timestamp,
      date: new Date(timestamp).toISOString(),
      currentTime: new Date(currentTime).toISOString(),
      timeDiff: currentTime - timestamp,
      timeDiffSeconds: (currentTime - timestamp) / 1000 + '秒',
      format: '毫秒格式',
      length: timestamp.toString().length + '位',
      meituanStatus: meituanTimeFix.getStatus(),
      validPeriod: '2分钟'
    })

    // 检查时间戳是否在合理范围内
    const timeDiff = Math.abs(currentTime - timestamp)
    if (timeDiff > 60000) { // 差异超过1分钟
      console.warn('时间戳与当前时间差异较大:', {
        diffSeconds: timeDiff / 1000 + '秒',
        action: '继续使用，但可能导致签名验证失败'
      })
    }

    return timestamp.toString()
  }

  generateSignature(method, endpoint, body = {}) {
    const timestamp = this.generateTimestamp()
    const bodyStr = typeof body === 'object' ? JSON.stringify(body) : body
    const contentMD5 = this.generateContentMD5(bodyStr)

    // 根据美团官方文档构建Headers字符串
    // Headers格式：HeaderKey1:HeaderValue1\n + HeaderKey2:HeaderValue2\n
    // 需要按字典排序，S-Ca-App在S-Ca-Timestamp之前
    const headers = `S-Ca-App:${this.config.appkey}\nS-Ca-Timestamp:${timestamp}\n`

    // 根据美团官方文档构建URL
    // Url指Path，不包含域名和Query参数（因为这个接口没有Query参数）
    const url = `/cps_open/common/api/v1${endpoint}`

    // 根据美团官方文档构建签名字符串
    // 格式：HTTPMethod + "\n" + Content-MD5 + "\n" + Headers + Url
    const stringToSign = `${method}\n${contentMD5}\n${headers}${url}`

    console.log('美团官方签名详情:', {
      method: method,
      contentMD5: contentMD5,
      headers: headers.replace(/\n/g, '\\n'),
      url: url,
      stringToSign: stringToSign.replace(/\n/g, '\\n'),
      timestamp: timestamp,
      timestampDate: new Date(parseInt(timestamp)).toISOString(),
      appkey: this.config.appkey,
      secret: this.config.secret.substring(0, 8) + '...' // 只显示前8位
    })

    // 根据美团官方文档计算签名
    // 使用HmacSHA256，然后Base64编码
    const hmacResult = crypto.hmacSHA256(stringToSign, this.config.secret)
    console.log('HMAC-SHA256结果:', hmacResult)

    const signatureBytes = crypto.hexToBytes(hmacResult)
    const base64Signature = crypto.base64Encode(signatureBytes)

    console.log('最终签名:', base64Signature)

    return {
      signature: base64Signature,
      timestamp,
      contentMD5
    }
  }

  async request(endpoint, data = {}, retryCount = 0) {
    const url = `${this.config.baseUrl}${endpoint}`
    const method = 'POST'

    const requestData = { ...data }
    const { signature, timestamp, contentMD5 } = this.generateSignature(method, endpoint, requestData)

    // 根据美团官方文档构建请求头
    const headers = {
      'Content-Type': 'application/json;charset=utf-8',
      'S-Ca-App': this.config.appkey,
      'S-Ca-Timestamp': timestamp,
      'S-Ca-Signature': signature,
      'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp', // 必须包含S-Ca-Timestamp，建议包含S-Ca-App
      'Content-MD5': contentMD5
    }

    console.log(`[请求${retryCount > 0 ? `重试${retryCount}` : ''}] ${endpoint}`, {
      url: url,
      method: method,
      timestamp: timestamp,
      timestampDate: new Date(parseInt(timestamp)).toISOString(),
      currentTime: new Date().toISOString(),
      timeDiff: Date.now() - parseInt(timestamp),
      retryCount: retryCount
    })

    // 详细记录请求头信息，便于调试
    console.log('请求头详情:', {
      contentType: headers['Content-Type'],
      app: headers['S-Ca-App'],
      timestamp: headers['S-Ca-Timestamp'],
      signatureHeaders: headers['S-Ca-Signature-Headers'],
      contentMD5: headers['Content-MD5'],
      signatureLength: headers['S-Ca-Signature'].length
    })

    return new Promise((resolve, reject) => {
      wx.request({
        url,
        method,
        data: requestData,
        header: headers,
        success: (res) => {
          // 从响应中学习服务器时间
          meituanTimeFix.learnFromResponse(res)

          if (res.statusCode === 200) {
            if (res.data.code === 0) {
              console.log(`[请求成功] ${endpoint}`, res.data)
              resolve(res.data)
            } else {
              console.error(`[API错误] ${endpoint}: ${res.data.message || '未知错误'}`, {
                code: res.data.code,
                message: res.data.message,
                env: `Windows,mp,${wx.getSystemInfoSync().SDKVersion}; lib: ${wx.getAccountInfoSync().miniProgram.version || '未知'}`
              })

              // 处理签名验证失败
              if (this.isSignatureError(res.data) && retryCount < this.retryTimes) {
                console.log(`[签名错误重试] ${endpoint} 第${retryCount + 1}次重试`)
                const delay = this.retryDelay * Math.pow(2, retryCount)
                setTimeout(() => {
                  this.request(endpoint, data, retryCount + 1)
                    .then(resolve)
                    .catch(reject)
                }, delay)
                return
              }

              // 处理时间戳过期错误
              if (this.isTimestampExpiredError(res.data) && retryCount < this.retryTimes) {
                console.log(`[时间戳过期重试] ${endpoint} 第${retryCount + 1}次重试`)

                const handled = meituanTimeFix.handleTimestampExpired(res.data)
                if (handled) {
                  const delay = this.retryDelay * Math.pow(2, retryCount)
                  setTimeout(() => {
                    this.request(endpoint, data, retryCount + 1)
                      .then(resolve)
                      .catch(reject)
                  }, delay)
                  return
                }
              }

              reject(new Error(res.data.message || '请求失败'))
            }
          } else {
            console.error(`[HTTP错误] ${endpoint}: ${res.statusCode}`, res)

            if (res.statusCode >= 500 && retryCount < this.retryTimes) {
              console.log(`[服务器错误重试] ${endpoint} 第${retryCount + 1}次重试`)
              const delay = this.retryDelay * Math.pow(2, retryCount)
              setTimeout(() => {
                this.request(endpoint, data, retryCount + 1)
                  .then(resolve)
                  .catch(reject)
              }, delay)
              return
            }

            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (error) => {
          console.error(`[请求失败] ${endpoint}:`, error)

          if (retryCount < this.retryTimes) {
            console.log(`[网络错误重试] ${endpoint} 第${retryCount + 1}次重试`)
            const delay = this.retryDelay * Math.pow(2, retryCount)
            setTimeout(() => {
              this.request(endpoint, data, retryCount + 1)
                .then(resolve)
                .catch(reject)
            }, delay)
            return
          }

          reject(error)
        }
      })
    })
  }

  isSignatureError(responseData) {
    if (!responseData) return false

    // 扩展签名错误关键词列表，提高检测准确性
    const signatureErrors = [
      '请求签名验证失败',
      'signature verification failed',
      'invalid signature',
      '签名验证失败',
      '签名无效',
      'signature invalid',
      'signature error',
      'signature failed',
      '签名错误',
      '签名失败'
    ]

    const message = (responseData.message || '').toLowerCase()
    const isSignatureError = responseData.code === 400 && signatureErrors.some(error =>
      message.includes(error.toLowerCase())
    )

    if (isSignatureError) {
      console.log('[签名错误详情]', {
        errorCode: responseData.code,
        errorMessage: responseData.message,
        detectedPattern: signatureErrors.find(error => message.includes(error.toLowerCase())),
        currentTime: new Date().toISOString(),
        meituanTimeStatus: meituanTimeFix.getStatus()
      })
    }

    return isSignatureError
  }

  isTimestampExpiredError(responseData) {
    if (!responseData) return false

    // 扩展时间戳错误关键词列表，提高检测准确性
    const timestampErrors = [
      '请求时间戳已过期',
      'timestamp expired',
      'timestamp invalid',
      'request timestamp expired',
      '时间戳过期',
      '时间戳无效',
      'timestamp out of range',
      'timestamp error',
      'timestamp too old',
      'timestamp too new',
      '时间戳超出范围',
      '时间戳错误'
    ]

    const message = (responseData.message || '').toLowerCase()
    return responseData.code === 400 && timestampErrors.some(error =>
      message.includes(error.toLowerCase())
    )
  }

  async queryCoupon(latitude, longitude, options = {}) {
    const params = {
      latitude: Math.round(latitude * 1000000),
      longitude: Math.round(longitude * 1000000),
      priceFloor: options.priceFloor || 0,
      priceCap: options.priceCap || 500000,
      pageNo: options.pageNo || 1,
      pageSize: options.pageSize || 20,
      ascDescOrder: options.ascDescOrder || 2,
      sortField: options.sortField || 1
    }

    if (options.vpSkuViewIds && options.vpSkuViewIds.length > 0) {
      params.vpSkuViewIds = options.vpSkuViewIds
    } else if (options.listTopiId) {
      params.listTopiId = options.listTopiId
    } else if (options.searchText) {
      params.searchText = options.searchText
    } else if (options.multipleSupplyList && options.multipleSupplyList.length > 0) {
      params.multipleSupplyList = options.multipleSupplyList
    } else {
      params.listTopiId = 'hot_sale'
    }

    try {
      const response = await this.request('/query_coupon', params)
      return response
    } catch (error) {
      console.error('查询商品券失败:', error)
      throw error
    }
  }

  async getRecommendProducts(page = 1, pageSize = 20) {
    try {
      const location = await this.getUserLocation()

      const cacheKey = cacheManager.generateKey('recommend_products', {
        lat: location.latitude,
        lng: location.longitude,
        page,
        pageSize
      })

      const cachedData = cacheManager.get(cacheKey)
      if (cachedData) {
        return cachedData
      }

      const response = await this.queryCoupon(location.latitude, location.longitude, {
        pageNo: page,
        pageSize,
        listTopiId: 'hot_sale',
        sortField: 2
      })

      cacheManager.set(cacheKey, response, 5 * 60 * 1000)
      return response
    } catch (error) {
      console.error('获取推荐商品失败:', error)
      return this.getMockProducts(page, pageSize)
    }
  }

  async getUserLocation() {
    return new Promise((resolve, reject) => {
      const app = getApp()
      if (app.globalData && app.globalData.location) {
        resolve(app.globalData.location)
        return
      }

      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude
          }
          if (app.globalData) {
            app.globalData.location = location
          }
          resolve(location)
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          const defaultLocation = {
            latitude: 23.129163,
            longitude: 113.264435
          }
          resolve(defaultLocation)
        }
      })
    })
  }

  getMockProducts(page = 1, pageSize = 20) {
    const products = []
    const baseIndex = (page - 1) * pageSize

    for (let i = 0; i < pageSize; i++) {
      const index = baseIndex + i
      products.push({
        couponPackDetail: {
          name: `精选商品 ${index + 1}`,
          skuViewId: `mock_${index}`,
          couponNum: 1,
          validTime: 86400,
          headUrl: '/assets/images/placeholder.png',
          saleVolume: `${Math.floor(Math.random() * 1000 + 100)}+`,
          startTime: Date.now(),
          endTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
          saleStatus: true,
          originalPrice: (Math.random() * 100 + 50).toFixed(2),
          sellPrice: (Math.random() * 50 + 10).toFixed(2)
        },
        brandInfo: {
          brandName: `品牌${index + 1}`,
          brandLogoUrl: '/assets/images/placeholder.png'
        },
        commissionInfo: {
          commissionPercent: '1000'
        }
      })
    }

    return {
      code: 0,
      data: products,
      hasNext: page < 5
    }
  }
}

module.exports = new ApiService()