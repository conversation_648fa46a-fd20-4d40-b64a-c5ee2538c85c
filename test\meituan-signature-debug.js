/**
 * 美团API签名调试工具
 * 用于诊断和修复签名验证失败问题
 */

// 模拟微信小程序环境
global.wx = {
  getSystemInfoSync: () => ({ SDKVersion: '3.8.12' }),
  getAccountInfoSync: () => ({ miniProgram: { version: '1.0.0' } }),
  arrayBufferToBase64: (buffer) => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  },
  request: (options) => {
    console.log('模拟请求:', {
      url: options.url,
      method: options.method,
      headers: options.header,
      data: options.data
    })

    // 模拟成功响应
    setTimeout(() => {
      options.success({
        statusCode: 200,
        data: { code: 0, message: 'success', data: {} },
        header: {
          'Date': new Date().toUTCString()
        }
      })
    }, 100)
  }
}

// Node.js环境下的btoa/atob实现
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

const meituanTimeFix = require('../src/utils/meituan-time-fix-official.js')
const crypto = require('../src/utils/crypto-fixed-final.js')

console.log('=== 美团API签名调试工具 ===\n')

// 1. 测试时间戳生成
console.log('1. 时间戳生成测试:')
console.log('当前系统时间:', new Date().toISOString())

const timestamp1 = meituanTimeFix.getMeituanTimestamp()
console.log('生成的时间戳:', timestamp1)
console.log('时间戳对应时间:', new Date(timestamp1).toISOString())
console.log('时间戳长度:', timestamp1.toString().length, '位')
console.log('时间戳格式:', typeof timestamp1, '(应该是number)')

// 检查时间戳合理性
const currentTime = Date.now()
const timeDiff = Math.abs(currentTime - timestamp1)
console.log('与当前时间差异:', Math.floor(timeDiff / 1000), '秒')
console.log('时间戳是否合理:', timeDiff < 120000 ? '✅ 是' : '❌ 否')

console.log('\n2. Content-MD5测试:')
const testBody = '{"latitude":39928000,"longitude":116404000,"pageNo":1,"pageSize":10}'
console.log('测试Body:', testBody)

const contentMD5 = crypto.generateContentMD5(testBody)
console.log('Content-MD5结果:', contentMD5)
console.log('Content-MD5长度:', contentMD5.length)

// 测试空Body
const emptyMD5 = crypto.generateContentMD5('')
console.log('空Body MD5:', emptyMD5)

console.log('\n3. 签名字符串构建测试:')
const config = {
  appkey: '9498b0824d214ee4b65bfab1be6dbed0',
  secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
}

const method = 'POST'
const endpoint = '/query_coupon'
const timestamp = timestamp1.toString()

// 构建Headers字符串
const headers = `S-Ca-App:${config.appkey}\nS-Ca-Timestamp:${timestamp}\n`
console.log('Headers字符串:', JSON.stringify(headers))

// 构建完整端点
const fullEndpoint = `/cps_open/common/api/v1${endpoint}`
console.log('完整端点:', fullEndpoint)

// 构建签名字符串
const stringToSign = `${method}\n${contentMD5}\n${headers}${fullEndpoint}`
console.log('签名字符串:')
console.log(JSON.stringify(stringToSign))

console.log('\n4. HMAC-SHA256签名测试:')
const hmacResult = crypto.hmacSHA256(stringToSign, config.secret)
console.log('HMAC-SHA256结果:', hmacResult)
console.log('HMAC长度:', hmacResult.length)

console.log('\n5. 最终签名生成测试:')
const signatureBytes = crypto.hexToBytes(hmacResult)
console.log('签名字节数组长度:', signatureBytes.length)

const finalSignature = crypto.base64Encode(signatureBytes)
console.log('最终签名:', finalSignature)
console.log('最终签名长度:', finalSignature.length)

console.log('\n6. 完整请求头测试:')
const requestHeaders = {
  'Content-Type': 'application/json;charset=utf-8',
  'S-Ca-App': config.appkey,
  'S-Ca-Timestamp': timestamp,
  'S-Ca-Signature': finalSignature,
  'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
  'Content-MD5': contentMD5
}

console.log('请求头:')
Object.entries(requestHeaders).forEach(([key, value]) => {
  console.log(`  ${key}: ${value}`)
})

console.log('\n7. 请求头格式验证:')
console.log('Content-Type格式:', requestHeaders['Content-Type'].includes('application/json') ? '✅ 正确' : '❌ 错误')
console.log('S-Ca-Signature-Headers格式:', requestHeaders['S-Ca-Signature-Headers'].includes(',') ? '✅ 正确' : '❌ 错误')
console.log('时间戳格式:', /^\d{13}$/.test(requestHeaders['S-Ca-Timestamp']) ? '✅ 正确' : '❌ 错误')

console.log('\n8. 时间同步状态:')
const status = meituanTimeFix.getStatus()
console.log('时间同步状态:', status)

console.log('\n=== 调试完成 ===')

// 9. 模拟API调用测试
console.log('\n9. 模拟API调用测试:')
const apiService = require('../src/utils/api-service-fixed.js')

// 测试查询优惠券接口
const testData = {
  latitude: 39928000,
  longitude: 116404000,
  pageNo: 1,
  pageSize: 10
}

console.log('准备调用API...')
apiService.request('/query_coupon', testData)
  .then(result => {
    console.log('API调用成功:', result)
  })
  .catch(error => {
    console.error('API调用失败:', error.message)
  })
