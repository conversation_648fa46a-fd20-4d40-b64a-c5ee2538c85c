/* 加载状态组件样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.loading-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9999;
}

/* 转圈加载 */
.loading-spinner {
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6600;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
  border-width: 2rpx;
}

.loading-spinner.medium {
  width: 48rpx;
  height: 48rpx;
  border-width: 4rpx;
}

.loading-spinner.large {
  width: 64rpx;
  height: 64rpx;
  border-width: 6rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 点点点加载 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.loading-dots.small .dot {
  width: 8rpx;
  height: 8rpx;
}

.loading-dots.medium .dot {
  width: 12rpx;
  height: 12rpx;
}

.loading-dots.large .dot {
  width: 16rpx;
  height: 16rpx;
}

.dot {
  border-radius: 50%;
  background-color: #FF6600;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 骨架屏加载 */
.loading-skeleton {
  width: 100%;
  max-width: 400rpx;
}

.skeleton-line {
  height: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.skeleton-line.short {
  width: 60%;
}

.loading-skeleton.small .skeleton-line {
  height: 24rpx;
}

.loading-skeleton.large .skeleton-line {
  height: 40rpx;
}

@keyframes skeletonLoading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 加载文本 */
.loading-text {
  margin-top: 16rpx;
  color: #666666;
  text-align: center;
}

.loading-text.small {
  font-size: 24rpx;
  margin-top: 12rpx;
}

.loading-text.medium {
  font-size: 28rpx;
  margin-top: 16rpx;
}

.loading-text.large {
  font-size: 32rpx;
  margin-top: 20rpx;
}
