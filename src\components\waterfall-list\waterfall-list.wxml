<scroll-view 
  class="waterfall-container" 
  scroll-y 
  enhanced
  show-scrollbar="{{false}}"
  bind:scrolltolower="onScrollToLower">
  
  <view class="waterfall-content" style="column-gap: {{columnGap}}rpx;">
    <view 
      class="waterfall-column" 
      wx:for="{{columns}}" 
      wx:key="index"
      wx:for-item="column"
      wx:for-index="columnIndex">
      
      <product-card
        wx:for="{{column}}"
        wx:key="id"
        wx:for-item="item"
        product="{{item}}"
        layout="waterfall"
        data-item="{{item}}"
        bind:tap="onItemTap"
        bind:action="onItemAction"
      />
    </view>
  </view>
  
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{loading || !hasMore}}">
    <view class="loading-spinner" wx:if="{{loading}}"></view>
    <text class="load-text">
      {{loading ? '加载中...' : (hasMore ? '' : '没有更多了')}}
    </text>
  </view>
</scroll-view>