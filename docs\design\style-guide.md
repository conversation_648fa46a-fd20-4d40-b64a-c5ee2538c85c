# 样式指南

## 🎨 设计系统概述

本样式指南基于美团品牌视觉规范，结合微信小程序平台特性，为美团联盟微信小程序提供统一的视觉设计标准。通过系统化的设计规范，确保整个产品的视觉一致性和用户体验的连贯性。

## 🌈 色彩系统

### 主色调 (Primary Colors)

#### 美团品牌色
```css
/* 美团黄 - 主要品牌色 */
--meituan-yellow: #FFD100;
--meituan-yellow-light: #FFF566;
--meituan-yellow-dark: #E6BC00;

/* 美团橙 - 辅助品牌色 */
--meituan-orange: #FF6600;
--meituan-orange-light: #FF8533;
--meituan-orange-dark: #E65C00;

/* 美团绿 - 成功状态色 */
--meituan-green: #00AA90;
--meituan-green-light: #33BBA3;
--meituan-green-dark: #009981;
```

#### 渐变色彩
```css
/* 主要渐变 */
--gradient-primary: linear-gradient(135deg, #FFD100 0%, #FF6600 100%);
--gradient-orange: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);
--gradient-yellow: linear-gradient(135deg, #FFD100 0%, #FFA500 100%);

/* 背景渐变 */
--gradient-bg-warm: linear-gradient(180deg, #FFF8E1 0%, #FFFFFF 100%);
--gradient-bg-cool: linear-gradient(180deg, #F5F5F5 0%, #FFFFFF 100%);
```

### 功能色彩 (Functional Colors)

#### 状态色彩
```css
/* 成功状态 */
--color-success: #00AA90;
--color-success-light: #E8F5F3;
--color-success-dark: #008A73;

/* 警告状态 */
--color-warning: #FF9500;
--color-warning-light: #FFF3E0;
--color-warning-dark: #E6850E;

/* 错误状态 */
--color-error: #FF4444;
--color-error-light: #FFEBEE;
--color-error-dark: #E63939;

/* 信息状态 */
--color-info: #2196F3;
--color-info-light: #E3F2FD;
--color-info-dark: #1976D2;
```

#### 文字色彩
```css
/* 主要文字 */
--text-primary: #333333;
--text-primary-inverse: #FFFFFF;

/* 次要文字 */
--text-secondary: #666666;
--text-secondary-light: #999999;

/* 辅助文字 */
--text-tertiary: #999999;
--text-placeholder: #CCCCCC;
--text-disabled: #CCCCCC;

/* 链接文字 */
--text-link: #FF6600;
--text-link-hover: #E65C00;
--text-link-visited: #CC5500;
```

#### 背景色彩
```css
/* 主要背景 */
--bg-primary: #FFFFFF;
--bg-secondary: #F8F8F8;
--bg-tertiary: #F0F0F0;

/* 卡片背景 */
--bg-card: #FFFFFF;
--bg-card-hover: #FAFAFA;
--bg-card-active: #F5F5F5;

/* 遮罩背景 */
--bg-mask: rgba(0, 0, 0, 0.5);
--bg-mask-light: rgba(0, 0, 0, 0.3);
--bg-mask-dark: rgba(0, 0, 0, 0.7);

/* 分割线 */
--border-light: #F0F0F0;
--border-medium: #E0E0E0;
--border-dark: #CCCCCC;
```

### 色彩使用规范

#### 主色调使用场景
- **美团黄**: 主要按钮、重要标签、品牌标识
- **美团橙**: 价格显示、优惠标签、次要按钮
- **美团绿**: 成功状态、环保标签、确认操作

#### 色彩搭配原则
```css
/* 高对比度搭配 - 确保可读性 */
.high-contrast {
  background: var(--meituan-yellow);
  color: var(--text-primary);
}

/* 和谐搭配 - 同色系渐变 */
.harmonious {
  background: var(--gradient-orange);
  color: var(--text-primary-inverse);
}

/* 互补色搭配 - 突出重点 */
.complementary {
  background: var(--bg-primary);
  border: 2px solid var(--meituan-orange);
  color: var(--meituan-orange);
}
```

## 📝 字体系统

### 字体族定义
```css
/* 系统字体栈 */
--font-family-primary: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;

/* 数字字体 */
--font-family-number: 'Helvetica Neue', Helvetica, Arial, sans-serif;

/* 等宽字体 */
--font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
```

### 字体大小系统

#### 基础字体大小
```css
/* 标题字体 */
--font-size-h1: 48rpx;    /* 24px - 页面主标题 */
--font-size-h2: 40rpx;    /* 20px - 区块标题 */
--font-size-h3: 36rpx;    /* 18px - 卡片标题 */
--font-size-h4: 32rpx;    /* 16px - 小标题 */

/* 正文字体 */
--font-size-large: 32rpx;  /* 16px - 重要正文 */
--font-size-medium: 28rpx; /* 14px - 普通正文 */
--font-size-small: 24rpx;  /* 12px - 辅助文字 */
--font-size-mini: 20rpx;   /* 10px - 极小文字 */

/* 特殊字体 */
--font-size-price: 36rpx;  /* 18px - 价格显示 */
--font-size-badge: 20rpx;  /* 10px - 徽章文字 */
```

#### 字体权重系统
```css
--font-weight-light: 300;     /* 细体 */
--font-weight-normal: 400;    /* 正常 */
--font-weight-medium: 500;    /* 中等 */
--font-weight-semibold: 600;  /* 半粗 */
--font-weight-bold: 700;      /* 粗体 */
--font-weight-heavy: 800;     /* 重体 */
```

#### 行高系统
```css
--line-height-tight: 1.2;     /* 紧密行高 - 标题 */
--line-height-normal: 1.4;    /* 正常行高 - 正文 */
--line-height-relaxed: 1.6;   /* 宽松行高 - 长文本 */
--line-height-loose: 1.8;     /* 松散行高 - 特殊场景 */
```

### 字体使用规范

#### 标题层级
```css
/* H1 - 页面主标题 */
.title-h1 {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: 32rpx;
}

/* H2 - 区块标题 */
.title-h2 {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: 24rpx;
}

/* H3 - 卡片标题 */
.title-h3 {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  margin-bottom: 16rpx;
}
```

#### 正文样式
```css
/* 重要正文 */
.text-large {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
}

/* 普通正文 */
.text-medium {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
}

/* 辅助文字 */
.text-small {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
}
```

#### 特殊文字样式
```css
/* 价格显示 */
.text-price {
  font-family: var(--font-family-number);
  font-size: var(--font-size-price);
  font-weight: var(--font-weight-bold);
  color: var(--meituan-orange);
}

/* 徽章文字 */
.text-badge {
  font-size: var(--font-size-badge);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary-inverse);
  text-transform: uppercase;
}

/* 链接文字 */
.text-link {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-normal);
  color: var(--text-link);
  text-decoration: none;
}

.text-link:active {
  color: var(--text-link-hover);
}
```

## 📏 间距系统

### 基础间距单位
```css
/* 基础间距 - 8rpx为基础单位 */
--spacing-xs: 8rpx;      /* 4px - 极小间距 */
--spacing-sm: 16rpx;     /* 8px - 小间距 */
--spacing-md: 32rpx;     /* 16px - 中等间距 */
--spacing-lg: 48rpx;     /* 24px - 大间距 */
--spacing-xl: 64rpx;     /* 32px - 极大间距 */
--spacing-xxl: 96rpx;    /* 48px - 超大间距 */

/* 页面级间距 */
--spacing-page: 32rpx;   /* 16px - 页面边距 */
--spacing-section: 48rpx; /* 24px - 区块间距 */
--spacing-group: 32rpx;   /* 16px - 组间距 */
```

### 组件间距规范

#### 页面布局间距
```css
/* 页面容器 */
.page-container {
  padding: var(--spacing-page);
}

/* 区块间距 */
.section {
  margin-bottom: var(--spacing-section);
}

.section:last-child {
  margin-bottom: 0;
}

/* 组件间距 */
.component-group {
  margin-bottom: var(--spacing-group);
}
```

#### 卡片内部间距
```css
/* 卡片容器 */
.card {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

/* 卡片标题 */
.card-title {
  margin-bottom: var(--spacing-sm);
}

/* 卡片内容 */
.card-content {
  margin-bottom: var(--spacing-sm);
}

.card-content:last-child {
  margin-bottom: 0;
}
```

#### 列表间距
```css
/* 列表项 */
.list-item {
  padding: var(--spacing-md) var(--spacing-page);
  border-bottom: 1px solid var(--border-light);
}

.list-item:last-child {
  border-bottom: none;
}

/* 列表内容间距 */
.list-item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
```

### 响应式间距
```css
/* 小屏幕适配 */
@media (max-width: 375px) {
  :root {
    --spacing-page: 24rpx;    /* 12px */
    --spacing-section: 32rpx; /* 16px */
    --spacing-md: 24rpx;      /* 12px */
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  :root {
    --spacing-page: 40rpx;    /* 20px */
    --spacing-section: 64rpx; /* 32px */
  }
}
```

## 🔘 圆角系统

### 圆角尺寸定义
```css
/* 基础圆角 */
--radius-none: 0;           /* 无圆角 */
--radius-xs: 4rpx;          /* 2px - 极小圆角 */
--radius-sm: 8rpx;          /* 4px - 小圆角 */
--radius-md: 16rpx;         /* 8px - 中等圆角 */
--radius-lg: 24rpx;         /* 12px - 大圆角 */
--radius-xl: 32rpx;         /* 16px - 极大圆角 */
--radius-full: 50%;         /* 圆形 */

/* 特殊圆角 */
--radius-card: var(--radius-md);      /* 卡片圆角 */
--radius-button: var(--radius-sm);    /* 按钮圆角 */
--radius-input: var(--radius-sm);     /* 输入框圆角 */
--radius-modal: var(--radius-lg);     /* 模态框圆角 */
```

### 圆角使用规范

#### 组件圆角
```css
/* 卡片组件 */
.card {
  border-radius: var(--radius-card);
  overflow: hidden;
}

/* 按钮组件 */
.button {
  border-radius: var(--radius-button);
}

/* 输入框组件 */
.input {
  border-radius: var(--radius-input);
}

/* 头像组件 */
.avatar {
  border-radius: var(--radius-full);
}
```

#### 特殊圆角处理
```css
/* 顶部圆角 */
.rounded-top {
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
}

/* 底部圆角 */
.rounded-bottom {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

/* 左侧圆角 */
.rounded-left {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

/* 右侧圆角 */
.rounded-right {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}
```

## 🌟 阴影系统

### 阴影层级定义
```css
/* 基础阴影 */
--shadow-none: none;
--shadow-xs: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
--shadow-sm: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
--shadow-md: 0 8rpx 16rpx rgba(0, 0, 0, 0.12);
--shadow-lg: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
--shadow-xl: 0 24rpx 48rpx rgba(0, 0, 0, 0.18);

/* 特殊阴影 */
--shadow-card: var(--shadow-sm);
--shadow-modal: var(--shadow-lg);
--shadow-dropdown: var(--shadow-md);
--shadow-button: var(--shadow-xs);
```

### 阴影使用场景

#### 组件阴影
```css
/* 卡片阴影 */
.card {
  box-shadow: var(--shadow-card);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* 按钮阴影 */
.button {
  box-shadow: var(--shadow-button);
}

.button:active {
  box-shadow: var(--shadow-xs);
}

/* 模态框阴影 */
.modal {
  box-shadow: var(--shadow-modal);
}
```

#### 层级阴影
```css
/* Z轴层级对应的阴影 */
.z-1 { box-shadow: var(--shadow-xs); }
.z-2 { box-shadow: var(--shadow-sm); }
.z-3 { box-shadow: var(--shadow-md); }
.z-4 { box-shadow: var(--shadow-lg); }
.z-5 { box-shadow: var(--shadow-xl); }
```

## 🎯 交互状态

### 状态定义
```css
/* 基础状态 */
.interactive {
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 悬停状态 */
.interactive:hover {
  opacity: 0.8;
  transform: translateY(-2rpx);
}

/* 激活状态 */
.interactive:active {
  opacity: 0.6;
  transform: translateY(0);
}

/* 禁用状态 */
.interactive:disabled,
.interactive.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
```

### 按钮状态
```css
/* 主要按钮状态 */
.button-primary {
  background: var(--meituan-yellow);
  color: var(--text-primary);
  border: none;
  transition: all 0.2s ease;
}

.button-primary:active {
  background: var(--meituan-yellow-dark);
  transform: scale(0.98);
}

/* 次要按钮状态 */
.button-secondary {
  background: var(--meituan-orange);
  color: var(--text-primary-inverse);
  border: none;
  transition: all 0.2s ease;
}

.button-secondary:active {
  background: var(--meituan-orange-dark);
  transform: scale(0.98);
}
```

### 卡片状态
```css
/* 卡片交互状态 */
.card-interactive {
  transition: all 0.2s ease;
  cursor: pointer;
}

.card-interactive:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

/* 选中状态 */
.card-selected {
  border: 2px solid var(--meituan-orange);
  box-shadow: var(--shadow-md);
}
```

## 📱 响应式设计

### 断点系统
```css
/* 设备断点 */
--breakpoint-xs: 320px;   /* iPhone SE */
--breakpoint-sm: 375px;   /* iPhone 6/7/8 */
--breakpoint-md: 414px;   /* iPhone 6/7/8 Plus */
--breakpoint-lg: 428px;   /* iPhone 12/13/14 Pro Max */

/* 媒体查询 */
@media (max-width: 375px) {
  /* 小屏幕样式 */
}

@media (min-width: 376px) and (max-width: 414px) {
  /* 中等屏幕样式 */
}

@media (min-width: 415px) {
  /* 大屏幕样式 */
}
```

### 响应式字体
```css
/* 基础字体大小 */
:root {
  --font-size-base: 28rpx;
}

/* 小屏幕适配 */
@media (max-width: 375px) {
  :root {
    --font-size-base: 26rpx;
    --font-size-h1: 44rpx;
    --font-size-h2: 36rpx;
  }
}

/* 大屏幕适配 */
@media (min-width: 414px) {
  :root {
    --font-size-base: 30rpx;
    --font-size-h1: 52rpx;
    --font-size-h2: 44rpx;
  }
}
```

### 响应式布局
```css
/* 网格布局 */
.grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: repeat(2, 1fr);
}

@media (max-width: 375px) {
  .grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
}

@media (min-width: 414px) {
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 🎨 主题系统

### 主题变量
```css
/* 亮色主题 */
[data-theme="light"] {
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F8F8;
  --text-primary: #333333;
  --text-secondary: #666666;
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-primary: #1A1A1A;
  --bg-secondary: #2D2D2D;
  --text-primary: #FFFFFF;
  --text-secondary: #CCCCCC;
}
```

### 主题切换
```javascript
// 主题切换函数
function toggleTheme() {
  const currentTheme = document.documentElement.getAttribute('data-theme')
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark'
  
  document.documentElement.setAttribute('data-theme', newTheme)
  wx.setStorageSync('theme', newTheme)
}
```

## 🔧 工具类

### 布局工具类
```css
/* Flex布局 */
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

/* 对齐方式 */
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

/* Flex项目 */
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }
```

### 间距工具类
```css
/* 外边距 */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }

/* 内边距 */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

/* 方向性间距 */
.mt-xs { margin-top: var(--spacing-xs); }
.mr-xs { margin-right: var(--spacing-xs); }
.mb-xs { margin-bottom: var(--spacing-xs); }
.ml-xs { margin-left: var(--spacing-xs); }

.pt-xs { padding-top: var(--spacing-xs); }
.pr-xs { padding-right: var(--spacing-xs); }
.pb-xs { padding-bottom: var(--spacing-xs); }
.pl-xs { padding-left: var(--spacing-xs); }
```

### 文本工具类
```css
/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文本截断 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 文本样式 */
.text-bold { font-weight: var(--font-weight-bold); }
.text-medium { font-weight: var(--font-weight-medium); }
.text-normal { font-weight: var(--font-weight-normal); }
.text-light { font-weight: var(--font-weight-light); }

.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }
```

### 显示工具类
```css
/* 显示控制 */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* 可见性 */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }
```

## 📋 使用指南

### 样式引入顺序
```css
/* 1. 重置样式 */
@import "assets/styles/reset.wxss";

/* 2. 变量定义 */
@import "assets/styles/variables.wxss";

/* 3. 基础样式 */
@import "assets/styles/base.wxss";

/* 4. 组件样式 */
@import "components/button/button.wxss";

/* 5. 工具类 */
@import "assets/styles/utilities.wxss";

/* 6. 页面样式 */
/* 页面特定样式 */
```

### 命名规范
```css
/* BEM命名规范 */
.block {}
.block__element {}
.block--modifier {}
.block__element--modifier {}

/* 示例 */
.product-card {}
.product-card__image {}
.product-card__title {}
.product-card--featured {}
.product-card__title--large {}
```

### 最佳实践

#### 1. 使用CSS变量
```css
/* ✅ 推荐 */
.button {
  background: var(--meituan-yellow);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-button);
}

/* ❌ 不推荐 */
.button {
  background: #FFD100;
  color: #333333;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
}
```

#### 2. 保持一致性
```css
/* ✅ 统一的间距系统 */
.card {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.list-item {
  padding: var(--spacing-md);
}

/* ❌ 随意的数值 */
.card {
  padding: 30rpx;
  margin-bottom: 25rpx;
}

.list-item {
  padding: 28rpx;
}
```

#### 3. 响应式设计
```css
/* ✅ 移动优先 */
.container {
  padding: var(--spacing-sm);
}

@media (min-width: 414px) {
  .container {
    padding: var(--spacing-md);
  }
}

/* ❌ 桌面优先 */
.container {
  padding: var(--spacing-md);
}

@media (max-width: 414px) {
  .container {
    padding: var(--spacing-sm);
  }
}
```

---

*样式指南会根据设计系统的发展持续更新和完善*