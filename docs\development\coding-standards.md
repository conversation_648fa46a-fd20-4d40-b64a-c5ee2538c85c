# 编码规范

## 📝 总体原则

### 代码质量标准
- **可读性**: 代码应该清晰易懂，见名知意
- **一致性**: 保持统一的编码风格和命名规范
- **可维护性**: 代码结构清晰，便于后续维护和扩展
- **性能优化**: 考虑小程序性能特点，避免不必要的性能损耗
- **安全性**: 遵循安全编码规范，防止常见安全问题

### 开发工具配置
- **编辑器**: 推荐使用微信开发者工具或VS Code
- **代码格式化**: 使用Prettier进行代码格式化
- **代码检查**: 使用ESLint进行代码质量检查
- **版本控制**: 使用Git进行版本管理

## 🏷 命名规范

### 文件命名

#### 页面文件
```
pages/
├── index/              # 首页 - 使用简洁的英文单词
├── product-detail/     # 商品详情 - 使用kebab-case
├── search-result/      # 搜索结果 - 多个单词用连字符连接
└── user-profile/       # 用户资料 - 避免使用缩写
```

#### 组件文件
```
components/
├── product-card/       # 商品卡片 - 描述性命名
├── search-bar/         # 搜索栏 - 功能性命名
├── waterfall-list/     # 瀑布流列表 - 具体功能描述
└── loading-spinner/    # 加载动画 - 明确组件用途
```

#### 工具文件
```javascript
// 使用camelCase命名
utils/
├── apiService.js       # API服务
├── cacheManager.js     # 缓存管理
├── locationHelper.js   # 位置助手
└── formatUtils.js      # 格式化工具
```

### 变量命名

#### JavaScript变量
```javascript
// 使用camelCase命名
const userName = 'john'              // 普通变量
const isLoading = false              // 布尔值使用is/has/can开头
const hasMoreData = true
const canEdit = false

// 常量使用UPPER_CASE
const API_BASE_URL = 'https://api.example.com'
const MAX_RETRY_COUNT = 3
const DEFAULT_PAGE_SIZE = 20

// 私有变量使用下划线前缀
const _privateVariable = 'private'
const _internalState = {}

// 数组和对象使用复数形式
const products = []                  // 数组使用复数
const productList = []               // 或者使用List后缀
const userInfo = {}                  // 对象使用Info/Data后缀
const configData = {}
```

#### 函数命名
```javascript
// 动词开头，描述函数功能
function getUserInfo() {}            // 获取数据使用get
function setUserName(name) {}        // 设置数据使用set
function updateProductList() {}      // 更新数据使用update
function deleteProduct(id) {}        // 删除数据使用delete

// 事件处理函数使用on开头
function onButtonTap() {}            // 点击事件
function onPageLoad() {}             // 页面加载
function onDataChange() {}           // 数据变化

// 工具函数使用动词描述
function formatPrice(price) {}       // 格式化价格
function validateEmail(email) {}     // 验证邮箱
function calculateDistance() {}      // 计算距离
```

### CSS类名命名

#### BEM命名规范
```css
/* Block__Element--Modifier */
.product-card {}                     /* 块 */
.product-card__image {}              /* 元素 */
.product-card__title {}
.product-card--featured {}           /* 修饰符 */
.product-card--loading {}

/* 示例 */
.search-bar {}
.search-bar__input {}
.search-bar__button {}
.search-bar--focused {}
.search-bar--disabled {}

.waterfall-list {}
.waterfall-list__item {}
.waterfall-list__loading {}
.waterfall-list--empty {}
```

#### 功能性类名
```css
/* 布局类 */
.flex {}
.flex-center {}
.flex-between {}
.grid {}
.container {}

/* 文本类 */
.text-center {}
.text-left {}
.text-ellipsis {}
.text-bold {}

/* 间距类 */
.m-xs {}                            /* margin extra small */
.p-md {}                            /* padding medium */
.mt-lg {}                           /* margin top large */

/* 状态类 */
.is-active {}
.is-disabled {}
.is-loading {}
.is-hidden {}
```

## 📁 代码结构规范

### 页面文件结构

#### JavaScript文件结构
```javascript
// pages/example/example.js

// 1. 导入依赖（按类型分组）
const app = getApp()                 // 小程序实例
const ApiService = require('../../utils/api.js')    // 工具类
const { formatPrice } = require('../../utils/format.js')  // 解构导入

Page({
  // 2. 页面数据
  data: {
    // 基础数据
    loading: false,
    error: null,
    
    // 业务数据
    products: [],
    categories: [],
    
    // UI状态
    currentTab: 0,
    showModal: false
  },

  // 3. 生命周期函数（按执行顺序）
  onLoad(options) {
    this.initPage(options)
  },

  onShow() {
    this.refreshData()
  },

  onHide() {
    this.pauseTimer()
  },

  onUnload() {
    this.cleanup()
  },

  // 4. 小程序特有生命周期
  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  onShareAppMessage() {
    return this.getShareConfig()
  },

  // 5. 事件处理函数（按功能分组）
  // 用户交互事件
  onProductTap(e) {
    const { product } = e.currentTarget.dataset
    this.navigateToDetail(product)
  },

  onTabChange(e) {
    const { index } = e.detail
    this.switchTab(index)
  },

  // 组件事件
  onSearchInput(e) {
    const { value } = e.detail
    this.handleSearch(value)
  },

  // 6. 业务逻辑函数（按功能分组）
  // 初始化相关
  initPage(options) {
    this.parseOptions(options)
    this.loadInitialData()
  },

  // 数据加载相关
  async loadInitialData() {
    try {
      this.setData({ loading: true })
      const data = await ApiService.getProducts()
      this.processData(data)
    } catch (error) {
      this.handleError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 数据处理相关
  processData(data) {
    const processedData = data.map(item => ({
      ...item,
      formattedPrice: formatPrice(item.price)
    }))
    this.setData({ products: processedData })
  },

  // 7. 工具函数
  navigateToDetail(product) {
    wx.navigateTo({
      url: `/pages/product/detail?id=${product.id}`
    })
  },

  handleError(error) {
    console.error('页面错误:', error)
    wx.showToast({
      title: '加载失败',
      icon: 'none'
    })
  },

  cleanup() {
    // 清理定时器、监听器等
  }
})
```

### 组件文件结构

#### 组件JavaScript结构
```javascript
// components/product-card/product-card.js

Component({
  // 1. 组件配置
  options: {
    multipleSlots: true,
    styleIsolation: 'isolated'
  },

  // 2. 组件属性
  properties: {
    product: {
      type: Object,
      value: {},
      observer: 'onProductChange'
    },
    layout: {
      type: String,
      value: 'default'
    }
  },

  // 3. 组件数据
  data: {
    internalState: '',
    computedValue: ''
  },

  // 4. 生命周期函数
  lifetimes: {
    created() {
      console.log('组件创建')
    },
    
    attached() {
      this.initComponent()
    },
    
    detached() {
      this.cleanup()
    }
  },

  // 5. 页面生命周期
  pageLifetimes: {
    show() {
      this.onPageShow()
    },
    
    hide() {
      this.onPageHide()
    }
  },

  // 6. 组件方法
  methods: {
    // 初始化方法
    initComponent() {
      this.calculateLayout()
    },

    // 事件处理
    onCardTap() {
      this.triggerEvent('tap', {
        product: this.data.product
      })
    },

    // 数据观察器
    onProductChange(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.updateDisplay()
      }
    },

    // 工具方法
    calculateLayout() {
      // 计算布局
    },

    updateDisplay() {
      // 更新显示
    },

    cleanup() {
      // 清理资源
    }
  }
})
```

## 🎨 样式编码规范

### WXSS文件结构

```css
/* components/product-card/product-card.wxss */

/* 1. 导入样式 */
@import "../../assets/styles/variables.wxss";

/* 2. 组件根样式 */
.product-card {
  display: flex;
  flex-direction: column;
  background: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.2s ease;
}

/* 3. 子元素样式（按DOM结构顺序） */
.product-card__image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.product-card__content {
  padding: var(--spacing-md);
  flex: 1;
}

.product-card__title {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
  
  /* 文本截断 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card__price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.price-current {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-bold);
  color: var(--primary-orange);
}

.price-original {
  font-size: var(--font-size-small);
  color: var(--text-tertiary);
  text-decoration: line-through;
}

/* 4. 修饰符样式 */
.product-card--featured {
  border: 2px solid var(--primary-yellow);
}

.product-card--loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 5. 状态样式 */
.product-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

/* 6. 响应式样式 */
@media (max-width: 375px) {
  .product-card__title {
    font-size: var(--font-size-small);
  }
}

/* 7. 动画样式 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card--animate {
  animation: fadeIn 0.3s ease-out;
}
```

### CSS属性顺序

```css
.example {
  /* 1. 定位 */
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  
  /* 2. 盒模型 */
  display: flex;
  width: 100%;
  height: auto;
  margin: 0;
  padding: 16rpx;
  border: 1px solid #ccc;
  border-radius: 8rpx;
  
  /* 3. 背景 */
  background: #fff;
  background-image: url();
  
  /* 4. 文字 */
  font-size: 28rpx;
  font-weight: 400;
  line-height: 1.4;
  color: #333;
  text-align: left;
  
  /* 5. 其他 */
  opacity: 1;
  cursor: pointer;
  transition: all 0.2s ease;
  transform: translateX(0);
}
```

## 📋 注释规范

### JavaScript注释

#### 文件头注释
```javascript
/**
 * 商品卡片组件
 * @description 用于展示商品信息的卡片组件，支持多种布局模式
 * <AUTHOR>
 * @date 2025-01-22
 * @version 1.0.0
 */
```

#### 函数注释
```javascript
/**
 * 格式化价格显示
 * @param {number} price - 原始价格
 * @param {string} currency - 货币符号，默认为'¥'
 * @returns {string} 格式化后的价格字符串
 * @example
 * formatPrice(29.9) // '¥29.90'
 * formatPrice(100, '$') // '$100.00'
 */
function formatPrice(price, currency = '¥') {
  if (typeof price !== 'number' || isNaN(price)) {
    return currency + '0.00'
  }
  return currency + price.toFixed(2)
}
```

#### 复杂逻辑注释
```javascript
// 计算瀑布流布局
function calculateWaterfallLayout(items, containerWidth) {
  // 初始化列高度数组，用于记录每列的当前高度
  const columnHeights = new Array(COLUMN_COUNT).fill(0)
  const layoutItems = []
  
  items.forEach((item, index) => {
    // 找到高度最小的列，将新项目放置在该列
    const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights))
    
    // 计算项目位置和尺寸
    const x = shortestColumnIndex * (COLUMN_WIDTH + COLUMN_GAP)
    const y = columnHeights[shortestColumnIndex]
    
    // 更新列高度
    columnHeights[shortestColumnIndex] += item.height + ITEM_MARGIN
    
    layoutItems.push({
      ...item,
      x, y,
      columnIndex: shortestColumnIndex
    })
  })
  
  return layoutItems
}
```

### WXSS注释

```css
/* 
 * 商品卡片样式
 * 用于首页和搜索页面的商品展示
 */
.product-card {
  /* 基础布局 - 使用flex垂直排列 */
  display: flex;
  flex-direction: column;
  
  /* 视觉样式 - 卡片效果 */
  background: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  
  /* 交互效果 - 平滑过渡 */
  transition: all 0.2s ease;
}

/* 价格区域 - 突出显示当前价格 */
.product-card__price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

/* TODO: 需要添加加载状态样式 */
/* FIXME: 在小屏幕设备上标题可能显示不全 */
/* HACK: 临时解决方案，后续需要优化 */
```

## 🔧 代码质量规范

### ESLint配置

```javascript
// .eslintrc.js
module.exports = {
  env: {
    es6: true,
    node: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module'
  },
  rules: {
    // 代码风格
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'never'],
    'comma-dangle': ['error', 'never'],
    
    // 变量使用
    'no-unused-vars': 'error',
    'no-undef': 'error',
    'no-console': 'warn',
    
    // 函数规范
    'func-names': 'off',
    'prefer-arrow-callback': 'error',
    
    // 对象和数组
    'object-shorthand': 'error',
    'prefer-destructuring': 'error'
  },
  globals: {
    wx: 'readonly',
    App: 'readonly',
    Page: 'readonly',
    Component: 'readonly',
    getApp: 'readonly',
    getCurrentPages: 'readonly'
  }
}
```

### 代码检查规则

#### 必须遵守的规则
```javascript
// ✅ 正确示例
const userName = 'john'
const isLoading = false
const products = []

function getUserInfo() {
  return wx.getStorageSync('userInfo')
}

// ❌ 错误示例
var user_name = 'john'        // 使用var和下划线命名
let IsLoading = false         // 大写开头的变量名
const product = []            // 数组使用单数形式

function get_user_info() {    // 下划线命名函数
  return wx.getStorageSync('userInfo')
}
```

#### 推荐遵守的规则
```javascript
// ✅ 推荐写法
const config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000
}

const { apiUrl, timeout } = config

// 使用模板字符串
const message = `用户${userName}登录成功`

// 使用箭头函数
const formatPrice = (price) => `¥${price.toFixed(2)}`

// ❌ 不推荐写法
const config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000
}

const apiUrl = config.apiUrl
const timeout = config.timeout

// 字符串拼接
const message = '用户' + userName + '登录成功'

// 传统函数
function formatPrice(price) {
  return '¥' + price.toFixed(2)
}
```

## 🚀 性能优化规范

### 数据更新优化

```javascript
// ✅ 批量更新数据
const updateData = {
  'products[0].title': newTitle,
  'products[0].price': newPrice,
  loading: false
}
this.setData(updateData)

// ❌ 多次调用setData
this.setData({
  'products[0].title': newTitle
})
this.setData({
  'products[0].price': newPrice
})
this.setData({
  loading: false
})
```

### 图片加载优化

```xml
<!-- ✅ 使用懒加载和合适的模式 -->
<image 
  src="{{item.image}}" 
  mode="aspectFill" 
  lazy-load="{{true}}"
  show-menu-by-longpress="{{false}}"
/>

<!-- ❌ 不使用优化选项 -->
<image src="{{item.image}}" />
```

### 事件处理优化

```javascript
// ✅ 使用防抖处理搜索
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

const debouncedSearch = debounce(this.handleSearch, 300)

// ✅ 使用节流处理滚动
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

const throttledScroll = throttle(this.handleScroll, 100)
```

## 🔒 安全编码规范

### 数据验证

```javascript
// ✅ 输入验证
function validateUserInput(input) {
  if (!input || typeof input !== 'string') {
    throw new Error('无效输入')
  }
  
  // 长度验证
  if (input.length > 100) {
    throw new Error('输入过长')
  }
  
  // 特殊字符过滤
  const sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  
  return sanitized
}
```

### 敏感信息处理

```javascript
// ✅ 不在代码中硬编码敏感信息
const config = {
  appkey: wx.getStorageSync('appkey') || '',
  secret: wx.getStorageSync('secret') || ''
}

// ❌ 硬编码敏感信息
const config = {
  appkey: '9498b0824d214ee4b65bfab1be6dbed0',
  secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
}
```

### 网络请求安全

```javascript
// ✅ 请求参数验证和错误处理
async function apiRequest(url, data) {
  // 参数验证
  if (!url || typeof url !== 'string') {
    throw new Error('无效的URL')
  }
  
  try {
    const response = await wx.request({
      url: url,
      data: data,
      method: 'POST',
      timeout: 10000
    })
    
    // 响应验证
    if (response.statusCode !== 200) {
      throw new Error(`请求失败: ${response.statusCode}`)
    }
    
    return response.data
  } catch (error) {
    console.error('API请求错误:', error)
    throw error
  }
}
```

## 📊 测试规范

### 单元测试示例

```javascript
// tests/utils/format.test.js
const { formatPrice } = require('../../utils/format.js')

describe('formatPrice', () => {
  test('应该正确格式化正常价格', () => {
    expect(formatPrice(29.9)).toBe('¥29.90')
    expect(formatPrice(100)).toBe('¥100.00')
  })
  
  test('应该处理异常输入', () => {
    expect(formatPrice(null)).toBe('¥0.00')
    expect(formatPrice(undefined)).toBe('¥0.00')
    expect(formatPrice('invalid')).toBe('¥0.00')
  })
  
  test('应该支持自定义货币符号', () => {
    expect(formatPrice(29.9, '$')).toBe('$29.90')
  })
})
```

### 组件测试示例

```javascript
// tests/components/product-card.test.js
const componentTest = require('../../utils/component-test.js')

describe('ProductCard组件', () => {
  let component
  
  beforeEach(() => {
    component = componentTest.create('product-card')
  })
  
  test('应该正确渲染商品信息', () => {
    const product = {
      title: '测试商品',
      price: 29.9,
      image: 'test.jpg'
    }
    
    component.setData({ product })
    
    expect(component.data.product.title).toBe('测试商品')
    expect(component.querySelector('.product-card__title').textContent).toBe('测试商品')
  })
  
  test('应该触发点击事件', () => {
    const mockFn = jest.fn()
    component.addEventListener('tap', mockFn)
    
    component.querySelector('.product-card').click()
    
    expect(mockFn).toHaveBeenCalled()
  })
})
```

---

*编码规范会根据项目发展和团队反馈持续更新和完善*