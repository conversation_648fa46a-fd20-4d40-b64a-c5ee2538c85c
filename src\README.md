# 美团联盟微信小程序

## 🚀 快速开始

### 方式一：使用启动脚本（推荐）
1. 双击运行项目根目录下的 `start.bat` 文件
2. 选择"使用微信开发者工具打开项目"
3. 等待微信开发者工具自动打开项目

### 方式二：手动启动
1. **环境准备**
   - 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
   - 注册微信小程序账号，获取 AppID

2. **项目配置**
   - 使用微信开发者工具打开 `src` 目录
   - 在 `project.config.json` 中配置你的 AppID
   - 在 `utils/apiService.js` 中配置美团联盟API凭证

3. **运行项目**
   - 在微信开发者工具中点击"编译"
   - 在模拟器中预览效果
   - 点击"预览"生成二维码，在手机上测试

### 4. 功能测试
- 在个人中心页面找到"功能测试"入口
- 或直接访问 `/pages/test/test` 页面
- 运行各项功能测试，确保API和工具类正常工作

## 📱 功能特性

### ✅ 已实现功能
- 🏠 **首页**: 城市选择、搜索、商品瀑布流展示
- 🎯 **活动页**: 活动列表、分类筛选、排序功能
- 📤 **分享页**: 基础页面结构
- 👤 **个人中心**: 基础页面结构
- 🔍 **搜索功能**: 搜索栏组件、历史记录
- 📍 **位置服务**: 城市选择、定位功能
- 🧩 **组件系统**: 瀑布流、商品卡片、按钮、加载状态等

### 🔧 核心组件
- `city-selector`: 城市选择器
- `waterfall-list`: 瀑布流列表
- `product-card`: 商品卡片
- `search-bar`: 搜索栏
- `custom-button`: 自定义按钮
- `custom-loading`: 加载状态

### 🛠 工具类
- `apiService`: 美团联盟API服务
- `cacheManager`: 缓存管理
- `locationService`: 位置服务
- `crypto`: 加密工具（支持MD5、HMAC-SHA256）

## 🔗 API集成

### 美团联盟API
项目已集成美团联盟CPS API，支持：
- 商品查询 (`query_coupon`)
- 推广链接生成 (`get_referral_link`)
- 正确的签名算法实现

### 配置说明
在 `app.js` 中配置API凭证：
```javascript
apiConfig: {
  baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
  appkey: 'your_appkey',
  secret: 'your_secret',
  mediaId: 'your_media_id',
  allianceId: 'your_alliance_id',
  sid: 'your_sid'
}
```

## 🎨 UI设计

### 设计规范
- 主色调: `#FF6600` (美团橙)
- 辅助色: `#FFD100` (美团黄)
- 圆角: `16rpx` (卡片), `8rpx` (按钮)
- 间距: `32rpx` (页面边距), `24rpx` (组件间距)

### 响应式设计
- 支持不同屏幕尺寸
- 瀑布流自适应布局
- 组件化设计，易于维护

## 📦 项目结构

```
src/
├── app.js                 # 小程序入口
├── app.json              # 全局配置
├── app.wxss              # 全局样式
├── components/           # 组件目录
│   ├── city-selector/    # 城市选择器
│   ├── waterfall-list/   # 瀑布流列表
│   ├── product-card/     # 商品卡片
│   ├── search-bar/       # 搜索栏
│   ├── button/           # 按钮组件
│   └── loading/          # 加载组件
├── pages/                # 页面目录
│   ├── index/            # 首页
│   ├── activity/         # 活动页
│   ├── share/            # 分享页
│   ├── profile/          # 个人中心
│   ├── search/           # 搜索页
│   ├── city/             # 城市选择页
│   └── detail/           # 商品详情页
├── utils/                # 工具类
│   ├── apiService.js     # API服务
│   ├── cacheManager.js   # 缓存管理
│   ├── locationService.js # 位置服务
│   ├── crypto.js         # 加密工具
│   └── ...
└── assets/               # 静态资源
    ├── images/           # 图片
    ├── icons/            # 图标
    └── styles/           # 样式文件
```

## 🔧 开发说明

### 注意事项
1. 美团联盟API需要正确的签名算法，已在项目中实现
2. 位置服务需要用户授权，已处理权限申请
3. 图片资源为占位文件，实际使用时需要替换为真实图标
4. 部分功能使用模拟数据，可根据需要接入真实API

### 扩展开发
- 可以继续完善分享页面和个人中心功能
- 可以添加更多的商品筛选和排序功能
- 可以集成更多的美团API接口
- 可以添加用户登录和个人信息管理

## 🚀 部署上线

### 1. 代码审核准备
- 确保所有功能正常运行
- 完善用户隐私协议和服务条款
- 准备小程序图标和截图
- 填写小程序基本信息

### 2. 提交审核
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台，提交审核
4. 等待微信官方审核（通常1-7个工作日）

### 3. 发布上线
- 审核通过后，在微信公众平台点击"发布"
- 设置体验版供内部测试
- 正式发布后用户即可搜索使用

## 🛠️ 开发指南

### 代码规范
- 使用ES6+语法
- 遵循微信小程序开发规范
- 组件化开发，提高代码复用性
- 添加必要的注释和文档

### 性能优化
- 图片懒加载和压缩
- 合理使用缓存机制
- 避免频繁的API调用
- 优化页面渲染性能

### 调试技巧
- 使用微信开发者工具的调试功能
- 利用console.log输出调试信息
- 使用真机调试测试兼容性
- 监控小程序性能数据

## 🔧 常见问题

### Q: API调用失败怎么办？
A:
1. 检查网络连接
2. 验证API密钥配置
3. 查看控制台错误信息
4. 使用测试页面验证API功能

### Q: 位置获取失败？
A:
1. 确保用户已授权位置权限
2. 检查手机GPS是否开启
3. 在真机上测试（模拟器可能不支持）

### Q: 图片显示不出来？
A:
1. 检查图片路径是否正确
2. 确保图片文件存在
3. 验证图片格式是否支持
4. 检查网络图片的域名是否在白名单中

## 📞 技术支持

如果在使用过程中遇到问题，可以：
1. 查看项目文档和注释
2. 使用功能测试页面排查问题
3. 查看微信开发者工具的错误日志
4. 参考微信小程序官方文档

## 📄 许可证

本项目仅供学习和参考使用。使用本项目时请遵守相关法律法规和平台规则。
