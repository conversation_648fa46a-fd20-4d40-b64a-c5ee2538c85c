const apiService = require('../../utils/api-service-fixed')

Page({
  data: {
    productId: '',
    product: null,
    loading: true,
    currentImageIndex: 0,
    showShareModal: false
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ productId: options.id })
      this.loadProductDetail()
    }
  },

  onShareAppMessage() {
    return {
      title: this.data.product?.title || '美团优惠商品',
      desc: `仅需¥${this.data.product?.price}，原价¥${this.data.product?.originalPrice}`,
      path: `/pages/detail/detail?id=${this.data.productId}`,
      imageUrl: this.data.product?.images?.[0] || '/assets/images/share-logo.png'
    }
  },

  // 加载商品详情
  async loadProductDetail() {
    this.setData({ loading: true })

    try {
      // 模拟商品详情数据
      const product = {
        id: this.data.productId,
        title: '懒猪手特色！经典美味双人餐',
        images: [
          '/assets/images/product1.jpg',
          '/assets/images/product2.jpg',
          '/assets/images/product3.jpg'
        ],
        price: '19.9',
        originalPrice: '39.9',
        discount: '5折',
        tags: ['外卖', '新用户立减'],
        shopName: '懒猪手餐厅',
        shopLogo: '/assets/images/shop-logo.png',
        rating: '4.8',
        sales: '月销1000+',
        distance: '1.2km',
        deliveryTime: '30分钟',
        deliveryFee: '3',
        description: '精选优质食材，传统工艺制作，口感丰富，营养均衡。适合2-3人享用，包含主食、配菜、汤品等。',
        specifications: [
          { name: '份量', value: '双人餐' },
          { name: '口味', value: '微辣' },
          { name: '保质期', value: '当日制作' }
        ],
        couponInfo: {
          type: '满减券',
          condition: '满30减10',
          validTime: '2024-07-31前有效'
        }
      }

      this.setData({ product })
    } catch (error) {
      console.error('加载商品详情失败:', error)
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 图片切换
  onImageChange(e) {
    this.setData({
      currentImageIndex: e.detail.current
    })
  },

  // 预览图片
  onImageTap() {
    const { images, currentImageIndex } = this.data.product
    wx.previewImage({
      urls: images,
      current: images[currentImageIndex]
    })
  },

  // 点击店铺
  onShopTap() {
    wx.showToast({ title: '店铺功能开发中', icon: 'none' })
  },

  // 收藏商品
  onFavoriteTap() {
    wx.showToast({ title: '收藏成功', icon: 'success' })
  },

  // 分享商品
  onShareTap() {
    this.setData({ showShareModal: true })
  },

  // 关闭分享弹窗
  onShareModalClose() {
    this.setData({ showShareModal: false })
  },

  // 复制链接
  onCopyLink() {
    wx.setClipboardData({
      data: `https://miniprogram.com/pages/detail/detail?id=${this.data.productId}`,
      success: () => {
        wx.showToast({ title: '链接已复制', icon: 'success' })
        this.onShareModalClose()
      }
    })
  },

  // 立即购买
  async onBuyNow() {
    wx.showLoading({ title: '生成链接中...' })

    try {
      // 调用美团联盟API生成推广链接
      const response = await apiService.getReferralLink({
        productId: this.data.productId,
        linkType: 1 // 商品链接
      })

      if (response.success && response.data.referralLink) {
        // 跳转到美团小程序
        wx.navigateToMiniProgram({
          appId: 'wxde8ac0a21135c07d', // 美团小程序AppID
          path: response.data.referralLink,
          success: () => {
            console.log('跳转美团成功')
          },
          fail: (error) => {
            console.error('跳转美团失败:', error)
            wx.showToast({ title: '跳转失败', icon: 'none' })
          }
        })
      } else {
        throw new Error('生成推广链接失败')
      }
    } catch (error) {
      console.error('购买失败:', error)
      wx.showToast({ title: '购买失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 加入购物车
  onAddToCart() {
    wx.showToast({ title: '已加入购物车', icon: 'success' })
  }
})