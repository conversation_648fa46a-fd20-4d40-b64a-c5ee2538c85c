/**
 * 图片资源检查脚本
 * 用于验证所有必需的图片资源是否存在
 */

const fs = require('fs');
const path = require('path');

// 必需的图片资源列表
const requiredAssets = {
  icons: [
    // 基础功能图标
    'arrow-down.png',
    'arrow-right.png', 
    'close.png',
    'empty.png',
    'home.png',
    'location.png',
    'search.png',
    'delete.png',
    
    // 快速入口图标
    'tea.png',
    'seckill.png',
    'coupon.png',
    'signin.png',
    'free.png',
    
    // 用户相关图标
    'user-default.png',
    'about.png',
    'feedback.png',
    
    // 社交互动图标
    'like.png',
    'comment.png',
    'share-small.png',
    
    // 底部导航栏图标
    'home-active.png',
    'activity.png',
    'activity-active.png',
    'share.png',
    'share-active.png',
    'profile.png',
    'profile-active.png'
  ],
  images: [
    'banner1.png',
    'placeholder.png',
    'activity-placeholder.png',
    'product-placeholder.png',
    'avatar-placeholder.png',
    'share-image1.png',
    'share-image2.png',
    'share-image3.png',
    'share-logo.png'
  ]
};

function checkAssets() {
  console.log('🔍 开始检查图片资源...\n');
  
  let missingAssets = [];
  let totalChecked = 0;
  let totalFound = 0;

  // 检查图标
  console.log('📱 检查图标资源:');
  requiredAssets.icons.forEach(icon => {
    const iconPath = path.join(__dirname, '../src/assets/icons', icon);
    totalChecked++;
    
    if (fs.existsSync(iconPath)) {
      console.log(`  ✅ ${icon}`);
      totalFound++;
    } else {
      console.log(`  ❌ ${icon} - 缺失`);
      missingAssets.push(`icons/${icon}`);
    }
  });

  console.log('\n🖼️ 检查图片资源:');
  requiredAssets.images.forEach(image => {
    const imagePath = path.join(__dirname, '../src/assets/images', image);
    totalChecked++;
    
    if (fs.existsSync(imagePath)) {
      console.log(`  ✅ ${image}`);
      totalFound++;
    } else {
      console.log(`  ❌ ${image} - 缺失`);
      missingAssets.push(`images/${image}`);
    }
  });

  // 输出结果
  console.log('\n📊 检查结果:');
  console.log(`  总计检查: ${totalChecked} 个文件`);
  console.log(`  找到文件: ${totalFound} 个`);
  console.log(`  缺失文件: ${missingAssets.length} 个`);
  
  if (missingAssets.length === 0) {
    console.log('\n🎉 所有图片资源检查完成，项目资源完整！');
    console.log('✨ 可以正常启动微信小程序开发工具了');
    return true;
  } else {
    console.log('\n⚠️ 发现缺失的资源文件:');
    missingAssets.forEach(asset => {
      console.log(`  - ${asset}`);
    });
    return false;
  }
}

// 执行检查
if (require.main === module) {
  const result = checkAssets();
  process.exit(result ? 0 : 1);
}

module.exports = { checkAssets, requiredAssets };