// 城市选择页面功能测试
const locationService = require('../src/utils/locationService')

console.log('=== 城市选择页面功能测试 ===')

// 测试1: 获取热门城市
console.log('\n1. 测试热门城市获取:')
const hotCities = locationService.getHotCities()
console.log(`热门城市数量: ${hotCities.length}`)
console.log('热门城市列表:', hotCities.map(city => city.name).join(', '))

// 测试2: 获取所有城市
console.log('\n2. 测试所有城市获取:')
const allCities = locationService.getAllCities()
const letters = Object.keys(allCities).sort()
console.log(`字母分组数量: ${letters.length}`)
console.log('字母分组:', letters.join(', '))

let totalCities = 0
letters.forEach(letter => {
  totalCities += allCities[letter].length
  console.log(`${letter}: ${allCities[letter].length}个城市`)
})
console.log(`总城市数量: ${totalCities}`)

// 测试3: 搜索功能
console.log('\n3. 测试城市搜索功能:')
const searchTests = ['北京', '上海', 'beijing', 'sh', '广']
searchTests.forEach(keyword => {
  const results = locationService.searchCities(keyword)
  console.log(`搜索"${keyword}": 找到${results.length}个结果`)
  if (results.length > 0) {
    console.log(`  结果: ${results.slice(0, 3).map(city => city.name).join(', ')}${results.length > 3 ? '...' : ''}`)
  }
})

// 测试4: 城市设置和获取
console.log('\n4. 测试城市设置和获取:')
const originalCity = locationService.getCurrentCity()
console.log(`原始城市: ${originalCity}`)

locationService.setCurrentCity('北京')
console.log(`设置后城市: ${locationService.getCurrentCity()}`)

locationService.setCurrentCity(originalCity)
console.log(`恢复后城市: ${locationService.getCurrentCity()}`)

// 测试5: 权限检查功能
console.log('\n5. 测试权限检查功能:')
locationService.checkLocationPermission().then(permission => {
  console.log('权限状态:', permission)
}).catch(error => {
  console.log('权限检查失败:', error)
})

console.log('\n=== 测试完成 ===')