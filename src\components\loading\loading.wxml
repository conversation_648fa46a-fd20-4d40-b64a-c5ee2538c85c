<!--加载状态组件-->
<view class="loading-container {{fullscreen ? 'fullscreen' : ''}}" wx:if="{{loading}}" bindtap="onTap">
  
  <!-- 转圈加载 -->
  <view class="loading-spinner {{size}}" wx:if="{{type === 'spinner'}}" style="border-top-color: {{color}}">
    <view class="spinner-inner"></view>
  </view>

  <!-- 点点点加载 -->
  <view class="loading-dots {{size}}" wx:elif="{{type === 'dots'}}">
    <view class="dot" style="background-color: {{color}}"></view>
    <view class="dot" style="background-color: {{color}}"></view>
    <view class="dot" style="background-color: {{color}}"></view>
  </view>

  <!-- 骨架屏加载 -->
  <view class="loading-skeleton {{size}}" wx:elif="{{type === 'skeleton'}}">
    <view class="skeleton-line"></view>
    <view class="skeleton-line short"></view>
    <view class="skeleton-line"></view>
  </view>

  <!-- 加载文本 -->
  <text class="loading-text {{size}}" wx:if="{{showText && type !== 'skeleton'}}" style="color: {{color}}">
    {{type === 'dots' ? dotsText : text}}
  </text>
</view>
