# UI设计规范

## 🎨 设计原则

### 核心原则
- **一致性**: 保持与美团品牌风格一致
- **易用性**: 简洁直观的用户界面
- **响应性**: 快速响应用户操作
- **可访问性**: 支持无障碍访问

### 设计理念
- **以用户为中心**: 优先考虑用户体验和使用习惯
- **内容优先**: 突出商品和优惠信息
- **视觉层次**: 通过颜色、字体、间距建立清晰的信息层次
- **品牌一致**: 遵循美团品牌视觉规范

## 🌈 颜色系统

### 主色调
```css
/* 美团品牌色 */
--primary-yellow: #FFD100;     /* 美团黄 - 主要按钮、强调元素 */
--primary-orange: #FF6600;     /* 美团橙 - 价格、优惠标签 */
--accent-green: #00AA90;       /* 辅助绿 - 成功状态、环保标签 */

/* 渐变色 */
--gradient-orange: linear-gradient(135deg, #FF6B35 0%, #FF8C42 100%);
--gradient-yellow: linear-gradient(135deg, #FFD100 0%, #FFA500 100%);
```

### 文字颜色
```css
--text-primary: #333333;       /* 主要文字 - 标题、重要信息 */
--text-secondary: #666666;     /* 次要文字 - 描述、说明 */
--text-tertiary: #999999;      /* 辅助文字 - 占位符、提示 */
--text-disabled: #CCCCCC;      /* 禁用文字 */
--text-white: #FFFFFF;         /* 白色文字 - 深色背景上使用 */
--text-error: #FF4444;         /* 错误文字 */
--text-success: #00AA90;       /* 成功文字 */
```

### 背景颜色
```css
--bg-primary: #FFFFFF;         /* 主背景 - 页面主体 */
--bg-secondary: #F8F8F8;       /* 次背景 - 分区背景 */
--bg-tertiary: #F0F0F0;        /* 三级背景 - 输入框、卡片 */
--bg-card: #FFFFFF;            /* 卡片背景 */
--bg-overlay: rgba(0,0,0,0.5); /* 遮罩背景 */
```

### 边框颜色
```css
--border-light: #E5E5E5;       /* 浅色边框 */
--border-medium: #CCCCCC;      /* 中等边框 */
--border-dark: #999999;        /* 深色边框 */
```

## 📝 字体系统

### 字体族
```css
font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
```

### 字体大小
```css
--font-size-h1: 24px;          /* 页面主标题 */
--font-size-h2: 20px;          /* 区块标题 */
--font-size-h3: 18px;          /* 卡片标题 */
--font-size-large: 16px;       /* 重要文字 */
--font-size-medium: 14px;      /* 正文文字 */
--font-size-small: 12px;       /* 辅助文字 */
--font-size-mini: 10px;        /* 极小文字 */
```

### 字体权重
```css
--font-weight-light: 300;      /* 细体 */
--font-weight-normal: 400;     /* 正常 */
--font-weight-medium: 500;     /* 中等 */
--font-weight-semibold: 600;   /* 半粗 */
--font-weight-bold: 700;       /* 粗体 */
```

### 行高
```css
--line-height-tight: 1.2;      /* 紧密行高 - 标题 */
--line-height-normal: 1.4;     /* 正常行高 - 正文 */
--line-height-loose: 1.6;      /* 宽松行高 - 长文本 */
```

## 📏 间距系统

### 基础间距单位
```css
--spacing-xs: 4px;             /* 极小间距 - 图标与文字 */
--spacing-sm: 8px;             /* 小间距 - 相关元素 */
--spacing-md: 16px;            /* 中等间距 - 组件内部 */
--spacing-lg: 24px;            /* 大间距 - 组件之间 */
--spacing-xl: 32px;            /* 极大间距 - 区块之间 */
--spacing-xxl: 48px;           /* 超大间距 - 页面区域 */
```

### 页面布局间距
```css
--page-padding: 16px;          /* 页面左右内边距 */
--section-margin: 24px;        /* 区块间距 */
--card-padding: 16px;          /* 卡片内边距 */
--card-margin: 12px;           /* 卡片间距 */
```

## 🔘 圆角系统

```css
--radius-xs: 2px;              /* 极小圆角 - 标签 */
--radius-sm: 4px;              /* 小圆角 - 按钮、输入框 */
--radius-md: 8px;              /* 中等圆角 - 卡片 */
--radius-lg: 12px;             /* 大圆角 - 模态框 */
--radius-xl: 16px;             /* 极大圆角 - 特殊组件 */
--radius-round: 50%;           /* 圆形 - 头像、图标 */
```

## 🌟 阴影系统

```css
--shadow-xs: 0 1px 2px rgba(0,0,0,0.05);           /* 极小阴影 */
--shadow-sm: 0 2px 4px rgba(0,0,0,0.1);            /* 小阴影 */
--shadow-md: 0 4px 8px rgba(0,0,0,0.12);           /* 中等阴影 */
--shadow-lg: 0 8px 16px rgba(0,0,0,0.15);          /* 大阴影 */
--shadow-xl: 0 12px 24px rgba(0,0,0,0.18);         /* 极大阴影 */
```

## 📱 页面布局规范

### 首页布局

#### 顶部状态栏
```css
.status-bar {
  height: 44px;
  background: var(--gradient-orange);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--page-padding);
  color: var(--text-white);
}

.status-time {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
}

.status-icons {
  display: flex;
  gap: var(--spacing-xs);
}
```

#### 城市选择器
```css
.city-selector {
  background: var(--gradient-orange);
  padding: var(--spacing-md) var(--page-padding);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.city-name {
  color: var(--text-white);
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
}

.city-arrow {
  width: 12px;
  height: 12px;
  border-right: 2px solid var(--text-white);
  border-bottom: 2px solid var(--text-white);
  transform: rotate(45deg);
}
```

#### 搜索栏
```css
.search-bar {
  background: var(--gradient-orange);
  padding: 0 var(--page-padding) var(--spacing-md);
}

.search-input {
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.search-placeholder {
  color: var(--text-tertiary);
  font-size: var(--font-size-medium);
  flex: 1;
}

.search-button {
  background: var(--primary-yellow);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
}
```

#### 活动横幅
```css
.activity-banner {
  background: var(--primary-orange);
  margin: var(--spacing-md) var(--page-padding);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  position: relative;
  overflow: hidden;
}

.banner-title {
  color: var(--text-white);
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.banner-subtitle {
  color: var(--text-white);
  font-size: var(--font-size-small);
  opacity: 0.9;
  margin-bottom: var(--spacing-sm);
}

.banner-time {
  background: var(--primary-yellow);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-mini);
  display: inline-block;
}
```

#### 品牌推广区
```css
.brand-grid {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-md) var(--page-padding);
  background: var(--bg-primary);
}

.brand-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  flex: 1;
}

.brand-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-text {
  font-size: var(--font-size-mini);
  color: var(--text-secondary);
  text-align: center;
}
```

#### 分类标签栏
```css
.category-tabs {
  display: flex;
  padding: var(--spacing-sm) var(--page-padding);
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  overflow-x: auto;
  gap: var(--spacing-md);
}

.category-tab {
  white-space: nowrap;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  position: relative;
}

.category-tab.active {
  color: var(--primary-orange);
  font-weight: var(--font-weight-medium);
}

.category-tab.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--primary-orange);
  border-radius: var(--radius-xs);
}
```

### 商品卡片规范

#### 瀑布流商品卡片
```css
.product-card {
  background: var(--bg-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  margin-bottom: var(--card-margin);
}

.product-image {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
}

.product-content {
  padding: var(--card-padding);
}

.product-title {
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.price-current {
  color: var(--primary-orange);
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-bold);
}

.price-original {
  color: var(--text-tertiary);
  font-size: var(--font-size-small);
  text-decoration: line-through;
}

.product-tags {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.product-tag {
  background: var(--primary-orange);
  color: var(--text-white);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-mini);
}

.product-shop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.shop-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shop-distance {
  color: var(--text-tertiary);
}
```

#### 活动页商品列表
```css
.activity-product-item {
  display: flex;
  background: var(--bg-card);
  padding: var(--card-padding);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.activity-product-image {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-sm);
  object-fit: cover;
  margin-right: var(--spacing-md);
}

.activity-product-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.activity-product-title {
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-product-desc {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.activity-product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-product-price {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.activity-product-distance {
  font-size: var(--font-size-small);
  color: var(--text-tertiary);
}

.activity-buy-button {
  background: var(--primary-orange);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
}
```

## 📱 缺少页面的UI设计规范

### 城市选择页面详细规范

#### 页面整体布局
```css
.city-select-page {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 导航栏 */
.city-navbar {
  background: var(--primary-orange);
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 var(--page-padding);
  color: var(--text-white);
}

.city-navbar-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
}

.city-back-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

#### 当前定位区域
```css
.location-section {
  background: var(--bg-primary);
  margin: var(--spacing-md);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.location-title {
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.location-city {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
}

.location-city-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-large);
  color: var(--text-primary);
}

.location-icon {
  color: var(--primary-orange);
  font-size: var(--font-size-large);
}

.relocate-btn {
  color: var(--primary-orange);
  font-size: var(--font-size-small);
  background: transparent;
  border: none;
}
```

#### 搜索框区域
```css
.city-search {
  margin: 0 var(--spacing-md) var(--spacing-md);
  position: relative;
}

.city-search-input {
  width: 100%;
  height: 40px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: 0 var(--spacing-md) 0 40px;
  font-size: var(--font-size-medium);
}

.city-search-icon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  font-size: var(--font-size-medium);
}
```

#### 热门城市区域
```css
.hot-cities-section {
  background: var(--bg-primary);
  margin: 0 var(--spacing-md) var(--spacing-md);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.hot-cities-title {
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.hot-cities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-sm);
}

.hot-city-item {
  height: 40px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-medium);
  color: var(--text-primary);
}

.hot-city-item:active {
  background: var(--primary-orange);
  color: var(--text-white);
  border-color: var(--primary-orange);
}
```

#### 全部城市列表
```css
.all-cities-section {
  background: var(--bg-primary);
  margin: 0 var(--spacing-md);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.all-cities-title {
  padding: var(--spacing-md);
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
}

.city-group {
  border-bottom: 1px solid var(--border-light);
}

.city-group:last-child {
  border-bottom: none;
}

.group-letter {
  background: var(--bg-tertiary);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.city-item {
  height: 44px;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  background: transparent;
  border: none;
  width: 100%;
  text-align: left;
}

.city-item:last-child {
  border-bottom: none;
}

.city-item:active {
  background: var(--bg-secondary);
}
```

#### 字母索引
```css
.letter-index {
  position: fixed;
  right: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs);
  z-index: 100;
}

.letter-item {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-mini);
  color: var(--text-white);
  margin-bottom: 2px;
}

.letter-item:last-child {
  margin-bottom: 0;
}

.letter-item.active {
  background: var(--primary-orange);
  border-radius: var(--radius-xs);
}
```

### 商品详情页面详细规范

#### 页面整体布局
```css
.product-detail-page {
  background: var(--bg-secondary);
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部操作栏留空间 */
}

/* 透明导航栏 */
.product-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: transparent;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
}

.navbar-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.navbar-btn-icon {
  font-size: var(--font-size-large);
  color: var(--text-primary);
}
```

#### 商品图片轮播
```css
.product-gallery {
  position: relative;
  width: 100%;
  height: 400px;
  background: var(--bg-primary);
}

.gallery-swiper {
  width: 100%;
  height: 100%;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-indicator {
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-xs);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-round);
  background: rgba(255, 255, 255, 0.5);
}

.indicator-dot.active {
  background: var(--text-white);
}

.gallery-counter {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  background: rgba(0, 0, 0, 0.5);
  color: var(--text-white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-small);
}
```

#### 商品信息卡片
```css
.product-info-card {
  background: var(--bg-primary);
  margin: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.product-price-section {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.product-current-price {
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  color: var(--primary-orange);
}

.product-original-price {
  font-size: var(--font-size-medium);
  color: var(--text-tertiary);
  text-decoration: line-through;
}

.product-discount-tags {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.discount-tag {
  background: var(--primary-orange);
  color: var(--text-white);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-xs);
  font-size: var(--font-size-mini);
  font-weight: var(--font-weight-medium);
}

.product-title {
  font-size: var(--font-size-large);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-sm);
}

.product-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}
```

#### 商家信息卡片
```css
.merchant-info-card {
  background: var(--bg-primary);
  margin: 0 var(--spacing-md) var(--spacing-sm);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.merchant-logo {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-round);
  object-fit: cover;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.merchant-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.merchant-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.rating-stars {
  color: var(--primary-yellow);
}

.enter-store-btn {
  background: transparent;
  border: 1px solid var(--primary-orange);
  color: var(--primary-orange);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-small);
}
```

#### 底部操作栏
```css
.product-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-sm) var(--spacing-md);
  padding-bottom: calc(var(--spacing-sm) + env(safe-area-inset-bottom));
  display: flex;
  gap: var(--spacing-sm);
  z-index: 50;
}

.action-btn {
  height: 44px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-medium);
  font-weight: var(--font-weight-medium);
}

.action-btn-secondary {
  width: 80px;
  background: transparent;
  border: 1px solid var(--border-medium);
  color: var(--text-primary);
}

.action-btn-primary {
  flex: 1;
  background: var(--primary-orange);
  color: var(--text-white);
  border: none;
}

.action-btn-primary:active {
  background: var(--primary-orange-dark);
}
```

### 搜索结果页面详细规范

#### 页面整体布局
```css
.search-result-page {
  background: var(--bg-secondary);
  min-height: 100vh;
}

/* 搜索导航栏 */
.search-navbar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.search-back-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  height: 36px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-medium);
}

.search-submit-btn {
  background: var(--primary-orange);
  color: var(--text-white);
  padding: 0 var(--spacing-md);
  height: 36px;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-medium);
  border: none;
}
```

#### 搜索结果统计
```css
.search-result-stats {
  background: var(--bg-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.result-count {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.result-count-number {
  color: var(--primary-orange);
  font-weight: var(--font-weight-medium);
}
```

#### 筛选排序栏
```css
.filter-bar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  height: 44px;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  background: transparent;
  border: none;
  border-right: 1px solid var(--border-light);
}

.filter-item:last-child {
  border-right: none;
}

.filter-item.active {
  color: var(--primary-orange);
}

.filter-arrow {
  font-size: var(--font-size-small);
  transition: transform 0.2s ease;
}

.filter-arrow.up {
  transform: rotate(180deg);
}
```

#### 空状态设计
```css
.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl) var(--spacing-md);
  text-align: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-md);
}

.empty-title {
  font-size: var(--font-size-large);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.empty-desc {
  font-size: var(--font-size-medium);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-lg);
}

.search-suggestions {
  width: 100%;
}

.suggestions-title {
  font-size: var(--font-size-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.suggestion-tag {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-small);
}

.suggestion-tag:active {
  background: var(--primary-orange);
  color: var(--text-white);
  border-color: var(--primary-orange);
}
```

## 📐 响应式设计

### 屏幕尺寸适配
```css
/* iPhone SE (375px) */
@media (max-width: 375px) {
  .product-card {
    margin: 0 4px 8px;
  }
  
  .brand-grid {
    padding: 12px 8px;
  }
  
  .hot-cities-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .product-actions {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}

/* iPhone 12/13/14 (390px) */
@media (min-width: 376px) and (max-width: 414px) {
  .page-padding {
    padding: 0 16px;
  }
  
  .product-gallery {
    height: 350px;
  }
}

/* iPhone 12/13/14 Pro Max (428px) */
@media (min-width: 415px) {
  .brand-item {
    padding: 0 8px;
  }
  
  .product-card {
    margin: 0 6px 12px;
  }
  
  .product-gallery {
    height: 450px;
  }
  
  .hot-cities-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

### 安全区域适配
```css
/* 顶部安全区域 */
.status-bar {
  padding-top: env(safe-area-inset-top);
}

.product-navbar {
  top: env(safe-area-inset-top);
}

/* 底部安全区域 */
.tab-bar {
  padding-bottom: env(safe-area-inset-bottom);
}

.product-actions {
  padding-bottom: calc(var(--spacing-sm) + env(safe-area-inset-bottom));
}
```

## 🎯 交互状态

### 按钮状态
```css
.button {
  transition: all 0.2s ease;
}

.button:hover {
  opacity: 0.8;
}

.button:active {
  transform: scale(0.95);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
```

### 卡片状态
```css
.product-card {
  transition: all 0.2s ease;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}
```

### 加载状态
```css
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
```

## 🔧 工具类

### 文本工具类
```css
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
```

### 布局工具类
```css
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.absolute { position: absolute; }
.relative { position: relative; }
.fixed { position: fixed; }
```

### 间距工具类
```css
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.mt-xs { margin-top: var(--spacing-xs); }
.mb-xs { margin-bottom: var(--spacing-xs); }
.ml-xs { margin-left: var(--spacing-xs); }
.mr-xs { margin-right: var(--spacing-xs); }
```

---

*设计规范基于美团品牌指南和微信小程序设计规范制定*
## 🔄
 页面逻辑流程详细说明

### 完整用户流程图

```mermaid
graph TD
    A[小程序启动] --> B{位置权限检查}
    B -->|已授权| C[获取当前位置]
    B -->|未授权| D[显示位置授权弹窗]
    C --> E[解析城市信息]
    D --> F{用户选择}
    F -->|同意| C
    F -->|拒绝| G[使用默认城市广州]
    E --> H[首页初始化]
    G --> H
    
    H --> I[首页展示]
    I --> J{用户操作}
    
    J -->|点击城市| K[城市选择页]
    J -->|点击搜索| L[搜索结果页]
    J -->|点击商品| M[商品详情页]
    J -->|点击活动标签| N[活动页面]
    J -->|点击分享标签| O[分享页面]
    J -->|点击我的标签| P[个人中心]
    
    K --> Q[选择城市]
    Q --> R[更新城市信息]
    R --> I
    
    L --> S[输入搜索关键词]
    S --> T[显示搜索结果]
    T --> U{搜索结果操作}
    U -->|点击商品| M
    U -->|筛选排序| V[筛选弹窗]
    V --> T
    
    M --> W[查看商品详情]
    W --> X{详情页操作}
    X -->|立即购买| Y[生成推广链接]
    X -->|分享商品| Z[调用微信分享]
    X -->|收藏商品| AA[添加到收藏]
    Y --> BB[跳转美团小程序]
    
    N --> CC[活动列表展示]
    CC --> DD{活动页操作}
    DD -->|点击商品| M
    DD -->|筛选分类| EE[切换分类标签]
    EE --> CC
    
    O --> FF[分享内容展示]
    FF --> GG{分享页操作}
    GG -->|查看动态| HH[动态详情]
    GG -->|分享内容| Z
    
    P --> II[个人中心展示]
    II --> JJ{个人中心操作}
    JJ -->|订单中心| KK[订单列表页]
    JJ -->|客服咨询| LL[客服对话]
    JJ -->|退出登录| MM[登录页面]
```

### 数据流转逻辑

#### 1. 应用启动数据流
```javascript
// 应用启动时的数据初始化流程
const appLaunchFlow = {
  step1: '获取系统信息',
  step2: '检查位置权限',
  step3: '获取用户位置',
  step4: '解析城市信息',
  step5: '初始化缓存服务',
  step6: '加载首页数据',
  step7: '渲染首页界面'
}

// 数据流转示例
App({
  onLaunch() {
    // 1. 获取系统信息
    this.getSystemInfo()
    // 2. 初始化位置服务
    this.initLocationService()
    // 3. 初始化API服务
    this.initApiService()
    // 4. 清理过期缓存
    this.clearExpiredCache()
  }
})
```

#### 2. 页面数据加载流程
```javascript
// 页面数据加载的标准流程
const pageDataFlow = {
  onLoad: {
    step1: '解析页面参数',
    step2: '初始化页面状态',
    step3: '检查缓存数据',
    step4: '加载初始数据',
    step5: '渲染页面内容'
  },
  
  onShow: {
    step1: '检查数据是否需要刷新',
    step2: '更新页面状态',
    step3: '刷新动态数据'
  },
  
  onHide: {
    step1: '暂停定时器',
    step2: '保存页面状态',
    step3: '清理临时数据'
  }
}
```

#### 3. API调用数据流
```javascript
// API调用的完整数据流程
const apiCallFlow = {
  prepare: {
    step1: '构建请求参数',
    step2: '生成API签名',
    step3: '设置请求头'
  },
  
  request: {
    step1: '发送HTTP请求',
    step2: '处理网络异常',
    step3: '验证响应数据'
  },
  
  process: {
    step1: '解析响应数据',
    step2: '数据格式化',
    step3: '缓存处理',
    step4: '更新页面状态'
  },
  
  error: {
    step1: '错误分类处理',
    step2: '用户友好提示',
    step3: '降级方案执行'
  }
}
```

### 页面间跳转逻辑

#### 1. 导航跳转规则
```javascript
// 页面跳转的统一管理
const NavigationManager = {
  // 标准页面跳转
  navigateTo(url, params = {}) {
    const queryString = this.buildQueryString(params)
    wx.navigateTo({
      url: `${url}?${queryString}`,
      success: () => console.log('跳转成功'),
      fail: (error) => console.error('跳转失败', error)
    })
  },
  
  // Tab页面切换
  switchTab(url) {
    wx.switchTab({
      url: url,
      success: () => console.log('切换Tab成功'),
      fail: (error) => console.error('切换Tab失败', error)
    })
  },
  
  // 返回上一页
  navigateBack(delta = 1) {
    wx.navigateBack({
      delta: delta,
      success: () => console.log('返回成功'),
      fail: (error) => console.error('返回失败', error)
    })
  }
}
```

#### 2. 页面参数传递
```javascript
// 页面参数传递的标准格式
const PageParams = {
  // 商品详情页参数
  productDetail: {
    id: 'string',        // 商品ID
    from: 'string',      // 来源页面
    share: 'boolean'     // 是否分享进入
  },
  
  // 搜索结果页参数
  searchResult: {
    keyword: 'string',   // 搜索关键词
    category: 'string',  // 商品分类
    city: 'string'       // 城市信息
  },
  
  // 城市选择页参数
  citySelect: {
    current: 'string',   // 当前城市
    callback: 'string'   // 回调页面
  }
}
```

### 状态管理逻辑

#### 1. 全局状态管理
```javascript
// 全局状态管理器
const GlobalState = {
  // 用户状态
  user: {
    info: null,
    isLogin: false,
    permissions: {}
  },
  
  // 应用状态
  app: {
    currentCity: '广州',
    systemInfo: null,
    networkStatus: 'online'
  },
  
  // 业务状态
  business: {
    searchHistory: [],
    favoriteProducts: [],
    cartItems: []
  },
  
  // 更新状态的方法
  updateState(key, value) {
    this[key] = { ...this[key], ...value }
    this.notifyObservers(key, value)
  },
  
  // 观察者模式
  observers: [],
  subscribe(callback) {
    this.observers.push(callback)
  },
  
  notifyObservers(key, value) {
    this.observers.forEach(callback => callback(key, value))
  }
}
```

#### 2. 页面状态管理
```javascript
// 页面状态管理的标准模式
const PageState = {
  // 数据状态
  data: {
    loading: false,
    error: null,
    items: [],
    hasMore: true,
    page: 1
  },
  
  // UI状态
  ui: {
    showModal: false,
    activeTab: 0,
    scrollTop: 0
  },
  
  // 状态更新方法
  setState(newState) {
    this.setData({
      ...this.data,
      ...newState
    })
  },
  
  // 重置状态
  resetState() {
    this.setData({
      loading: false,
      error: null,
      items: [],
      hasMore: true,
      page: 1
    })
  }
}
```

## 🎯 交互状态规范

### 基础交互状态
```css
/* 可交互元素基础样式 */
.interactive {
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 悬停状态 */
.interactive:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* 激活状态 */
.interactive:active {
  opacity: 0.6;
  transform: translateY(0);
}

/* 禁用状态 */
.interactive:disabled,
.interactive.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
```

### 按钮交互状态
```css
/* 主要按钮状态 */
.button-primary {
  background: var(--primary-orange);
  color: var(--text-white);
  transition: all 0.2s ease;
}

.button-primary:active {
  background: var(--primary-orange-dark);
  transform: scale(0.98);
}

.button-primary:disabled {
  background: var(--text-tertiary);
  color: var(--text-white);
}

/* 次要按钮状态 */
.button-secondary {
  background: transparent;
  border: 1px solid var(--primary-orange);
  color: var(--primary-orange);
  transition: all 0.2s ease;
}

.button-secondary:active {
  background: var(--primary-orange);
  color: var(--text-white);
}
```

### 卡片交互状态
```css
/* 商品卡片交互 */
.product-card {
  transition: all 0.2s ease;
  cursor: pointer;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

/* 选中状态 */
.product-card.selected {
  border: 2px solid var(--primary-orange);
  box-shadow: var(--shadow-lg);
}
```

### 输入框交互状态
```css
/* 输入框状态 */
.input {
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.input:focus {
  border-color: var(--primary-orange);
  box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.1);
  outline: none;
}

.input:disabled {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

.input.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.1);
}
```

## 📋 UI一致性检查清单

### 全局一致性要求

#### 状态栏规范
- [ ] 所有页面状态栏高度一致（44px + 安全区域）
- [ ] 时间显示格式统一（HH:MM，如"21:23"）
- [ ] 状态图标位置和样式一致（信号、WiFi、电量）
- [ ] 状态栏背景色根据页面主题适配

#### 导航栏规范
- [ ] 导航栏高度统一（44px）
- [ ] 返回按钮样式和位置一致（左侧，44px点击区域）
- [ ] 标题文字居中，字体大小16px，粗体
- [ ] 右侧按钮样式统一（分享、搜索等）
- [ ] 导航栏背景色符合页面设计要求

#### 颜色使用规范
- [ ] 美团橙色#FF6600用于主要按钮和强调元素
- [ ] 价格显示统一使用红色#FF3333
- [ ] 文字颜色层级正确（#333333、#666666、#999999）
- [ ] 背景色使用规范（#FFFFFF、#F5F5F5）
- [ ] 边框和分割线颜色统一（#E5E5E5、#F0F0F0）

#### 字体规范检查
- [ ] 标题字体大小层级正确（24px、20px、18px、16px）
- [ ] 正文字体统一使用14px
- [ ] 说明文字统一使用12px
- [ ] 字体粗细使用规范（600粗体、400正常）
- [ ] 行高设置合理（标题1.2、正文1.4）

#### 间距系统检查
- [ ] 页面左右边距统一16px
- [ ] 卡片内边距统一16px
- [ ] 区块间距统一24px
- [ ] 列表项间距统一12px
- [ ] 网格间距统一16px

#### 圆角和阴影检查
- [ ] 卡片圆角统一8px
- [ ] 按钮圆角统一4px
- [ ] 搜索框圆角统一20px
- [ ] 头像圆角统一50%（圆形）
- [ ] 卡片阴影效果统一

### 组件一致性要求

#### 按钮组件检查
- [ ] 主要按钮样式统一（橙色背景，白色文字）
- [ ] 次要按钮样式统一（橙色边框，橙色文字）
- [ ] 按钮最小高度44px，符合触摸规范
- [ ] 按钮点击反馈动画一致
- [ ] 禁用状态样式统一

#### 卡片组件检查
- [ ] 商品卡片样式在所有页面保持一致
- [ ] 卡片阴影效果统一
- [ ] 卡片内容布局规范
- [ ] 卡片点击反馈动画一致
- [ ] 卡片间距在不同页面保持一致

#### 输入框组件检查
- [ ] 搜索框样式在所有页面一致
- [ ] 占位符文字颜色统一
- [ ] 输入框聚焦状态样式一致
- [ ] 输入框高度统一40px
- [ ] 输入框边框和背景色统一

#### 标签组件检查
- [ ] 优惠标签样式统一（红色背景，白色文字）
- [ ] 分类标签样式统一
- [ ] 标签选中状态样式一致
- [ ] 标签字体大小和内边距统一
- [ ] 标签圆角大小一致

### 页面特定检查

#### 首页检查项
- [ ] 城市选择器样式符合设计规范
- [ ] 搜索栏样式和占位符文字正确
- [ ] 活动横幅样式完全按照UI图片实现
- [ ] 品牌推广区图标和文字对齐
- [ ] 分类标签栏滚动和选中状态正确
- [ ] 瀑布流商品卡片样式统一

#### 活动页检查项
- [ ] 页面布局与首页保持一致性
- [ ] 品牌推广区图标样式正确
- [ ] 筛选功能栏样式符合设计
- [ ] 商品列表卡片样式统一
- [ ] 抢购按钮样式和位置正确

#### 分享页检查项
- [ ] 页面标题样式符合设计（黄色艺术字）
- [ ] 搜索栏样式与其他页面一致
- [ ] 分类标签栏样式统一
- [ ] 推荐内容四宫格布局正确
- [ ] 动态内容卡片样式统一

#### 个人中心检查项
- [ ] 黄色渐变背景效果正确
- [ ] 用户信息区域布局符合设计
- [ ] 统计信息卡片样式统一
- [ ] 功能入口列表样式一致
- [ ] 版本信息显示位置和样式正确

#### 商品详情页检查项
- [ ] 透明导航栏样式正确
- [ ] 图片轮播功能正常
- [ ] 商品信息卡片布局符合设计
- [ ] 商家信息区域样式统一
- [ ] 底部操作栏适配安全区域

#### 搜索结果页检查项
- [ ] 搜索导航栏样式统一
- [ ] 结果统计信息显示正确
- [ ] 筛选排序栏功能正常
- [ ] 商品列表样式与首页一致
- [ ] 空状态页面设计友好

#### 城市选择页检查项
- [ ] 导航栏橙色背景正确
- [ ] 当前定位区域样式符合设计
- [ ] 搜索框样式统一
- [ ] 热门城市网格布局正确
- [ ] 全部城市列表和字母索引功能正常

---

*UI规范会根据实际开发情况持续更新和完善*