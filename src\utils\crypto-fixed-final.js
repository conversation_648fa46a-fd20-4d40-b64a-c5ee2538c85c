/**
 * 修复版加密工具类
 * 使用标准的MD5和HMAC-SHA256实现
 * 解决"body md5值不一致"错误
 * 增强时间戳处理和验证功能
 */

class CryptoUtil {
  constructor() {
    this.timeOffset = 0 // 时间偏移量（秒）
    this.lastSyncTime = 0 // 上次时间同步时间
    this.syncInterval = 300000 // 同步间隔5分钟
  }

  /**
   * 获取校正后的时间戳
   * @param {boolean} useOffset 是否使用时间偏移
   * @returns {number} 校正后的时间戳（秒）
   */
  getCorrectedTimestamp(useOffset = true) {
    let timestamp = Math.floor(Date.now() / 1000)
    
    if (useOffset && this.timeOffset) {
      timestamp += this.timeOffset
    }
    
    return timestamp
  }

  /**
   * 设置时间偏移
   * @param {number} offset 时间偏移量（秒）
   */
  setTimeOffset(offset) {
    this.timeOffset = offset
    this.lastSyncTime = Date.now()
    console.log('设置时间偏移:', offset, '秒')
  }

  /**
   * 验证时间戳是否有效
   * @param {string|number} timestamp 要验证的时间戳
   * @param {number} tolerance 允许的时间差（秒），默认300秒（5分钟）
   * @returns {boolean} 时间戳是否有效
   */
  validateTimestamp(timestamp, tolerance = 300) {
    const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp
    const currentTime = this.getCorrectedTimestamp()
    const diff = Math.abs(currentTime - ts)
    
    console.log('时间戳验证:', {
      timestamp: ts,
      currentTime: currentTime,
      diff: diff,
      tolerance: tolerance,
      valid: diff <= tolerance
    })
    
    return diff <= tolerance
  }

  /**
   * 生成带有时间窗口的时间戳
   * 确保时间戳在有效范围内
   * @param {number} windowSize 时间窗口大小（秒），默认60秒
   * @returns {string} 时间戳字符串
   */
  generateValidTimestamp(windowSize = 60) {
    const baseTime = this.getCorrectedTimestamp()
    
    // 在时间窗口内随机选择一个时间点，避免时间戳过于精确
    const randomOffset = Math.floor(Math.random() * windowSize) - Math.floor(windowSize / 2)
    const timestamp = baseTime + randomOffset
    
    console.log('生成时间戳:', {
      baseTime: baseTime,
      randomOffset: randomOffset,
      finalTimestamp: timestamp,
      date: new Date(timestamp * 1000).toISOString()
    })
    
    return timestamp.toString()
  }

  /**
   * 检查是否需要重新同步时间
   * @returns {boolean} 是否需要同步
   */
  needTimeSync() {
    return (Date.now() - this.lastSyncTime) > this.syncInterval
  }
  /**
   * 标准MD5算法实现
   */
  md5(str) {
    // 使用经过验证的MD5实现
    function md5cycle(x, k) {
      var a = x[0], b = x[1], c = x[2], d = x[3];
      
      a = ff(a, b, c, d, k[0], 7, -680876936);
      d = ff(d, a, b, c, k[1], 12, -389564586);
      c = ff(c, d, a, b, k[2], 17, 606105819);
      b = ff(b, c, d, a, k[3], 22, -1044525330);
      a = ff(a, b, c, d, k[4], 7, -176418897);
      d = ff(d, a, b, c, k[5], 12, 1200080426);
      c = ff(c, d, a, b, k[6], 17, -1473231341);
      b = ff(b, c, d, a, k[7], 22, -45705983);
      a = ff(a, b, c, d, k[8], 7, 1770035416);
      d = ff(d, a, b, c, k[9], 12, -1958414417);
      c = ff(c, d, a, b, k[10], 17, -42063);
      b = ff(b, c, d, a, k[11], 22, -1990404162);
      a = ff(a, b, c, d, k[12], 7, 1804603682);
      d = ff(d, a, b, c, k[13], 12, -40341101);
      c = ff(c, d, a, b, k[14], 17, -1502002290);
      b = ff(b, c, d, a, k[15], 22, 1236535329);
      
      a = gg(a, b, c, d, k[1], 5, -165796510);
      d = gg(d, a, b, c, k[6], 9, -1069501632);
      c = gg(c, d, a, b, k[11], 14, 643717713);
      b = gg(b, c, d, a, k[0], 20, -373897302);
      a = gg(a, b, c, d, k[5], 5, -701558691);
      d = gg(d, a, b, c, k[10], 9, 38016083);
      c = gg(c, d, a, b, k[15], 14, -660478335);
      b = gg(b, c, d, a, k[4], 20, -405537848);
      a = gg(a, b, c, d, k[9], 5, 568446438);
      d = gg(d, a, b, c, k[14], 9, -1019803690);
      c = gg(c, d, a, b, k[3], 14, -187363961);
      b = gg(b, c, d, a, k[8], 20, 1163531501);
      a = gg(a, b, c, d, k[13], 5, -1444681467);
      d = gg(d, a, b, c, k[2], 9, -51403784);
      c = gg(c, d, a, b, k[7], 14, 1735328473);
      b = gg(b, c, d, a, k[12], 20, -1926607734);
      
      a = hh(a, b, c, d, k[5], 4, -378558);
      d = hh(d, a, b, c, k[8], 11, -2022574463);
      c = hh(c, d, a, b, k[11], 16, 1839030562);
      b = hh(b, c, d, a, k[14], 23, -35309556);
      a = hh(a, b, c, d, k[1], 4, -1530992060);
      d = hh(d, a, b, c, k[4], 11, 1272893353);
      c = hh(c, d, a, b, k[7], 16, -155497632);
      b = hh(b, c, d, a, k[10], 23, -1094730640);
      a = hh(a, b, c, d, k[13], 4, 681279174);
      d = hh(d, a, b, c, k[0], 11, -358537222);
      c = hh(c, d, a, b, k[3], 16, -722521979);
      b = hh(b, c, d, a, k[6], 23, 76029189);
      a = hh(a, b, c, d, k[9], 4, -640364487);
      d = hh(d, a, b, c, k[12], 11, -421815835);
      c = hh(c, d, a, b, k[15], 16, 530742520);
      b = hh(b, c, d, a, k[2], 23, -995338651);
      
      a = ii(a, b, c, d, k[0], 6, -198630844);
      d = ii(d, a, b, c, k[7], 10, 1126891415);
      c = ii(c, d, a, b, k[14], 15, -1416354905);
      b = ii(b, c, d, a, k[5], 21, -57434055);
      a = ii(a, b, c, d, k[12], 6, 1700485571);
      d = ii(d, a, b, c, k[3], 10, -1894986606);
      c = ii(c, d, a, b, k[10], 15, -1051523);
      b = ii(b, c, d, a, k[1], 21, -2054922799);
      a = ii(a, b, c, d, k[8], 6, 1873313359);
      d = ii(d, a, b, c, k[15], 10, -30611744);
      c = ii(c, d, a, b, k[6], 15, -1560198380);
      b = ii(b, c, d, a, k[13], 21, 1309151649);
      a = ii(a, b, c, d, k[4], 6, -145523070);
      d = ii(d, a, b, c, k[11], 10, -1120210379);
      c = ii(c, d, a, b, k[2], 15, 718787259);
      b = ii(b, c, d, a, k[9], 21, -343485551);
      
      x[0] = add32(a, x[0]);
      x[1] = add32(b, x[1]);
      x[2] = add32(c, x[2]);
      x[3] = add32(d, x[3]);
    }
    
    function cmn(q, a, b, x, s, t) {
      a = add32(add32(a, q), add32(x, t));
      return add32((a << s) | (a >>> (32 - s)), b);
    }
    
    function ff(a, b, c, d, x, s, t) {
      return cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }
    
    function gg(a, b, c, d, x, s, t) {
      return cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }
    
    function hh(a, b, c, d, x, s, t) {
      return cmn(b ^ c ^ d, a, b, x, s, t);
    }
    
    function ii(a, b, c, d, x, s, t) {
      return cmn(c ^ (b | (~d)), a, b, x, s, t);
    }
    
    function add32(a, b) {
      return (a + b) & 0xFFFFFFFF;
    }
    
    function hex_chr(n) {
      return "0123456789abcdef".charAt(n);
    }
    
    function rhex(n) {
      var s = "", j = 0;
      for(; j < 4; j++)
        s += hex_chr((n >> (j * 8 + 4)) & 0x0F) + hex_chr((n >> (j * 8)) & 0x0F);
      return s;
    }
    
    function str2blks_MD5(str) {
      var nblk = ((str.length + 8) >> 6) + 1;
      var blks = new Array(nblk * 16);
      for(var i = 0; i < nblk * 16; i++) blks[i] = 0;
      for(i = 0; i < str.length; i++)
        blks[i >> 2] |= str.charCodeAt(i) << ((i % 4) * 8);
      blks[str.length >> 2] |= 0x80 << ((str.length % 4) * 8);
      blks[nblk * 16 - 2] = str.length * 8;
      return blks;
    }
    
    // UTF-8编码
    str = this.utf8Encode(str);
    var x = str2blks_MD5(str);
    var a = 1732584193, b = -271733879, c = -1732584194, d = 271733878;
    
    for(var i = 0; i < x.length; i += 16) {
      var olda = a, oldb = b, oldc = c, oldd = d;
      md5cycle([a, b, c, d], x.slice(i, i + 16));
      a = add32(a, olda); b = add32(b, oldb);
      c = add32(c, oldc); d = add32(d, oldd);
    }
    
    return rhex(a) + rhex(b) + rhex(c) + rhex(d);
  }

  /**
   * UTF-8编码
   */
  utf8Encode(str) {
    str = str.replace(/\r\n/g, "\n");
    var utftext = "";
    for (var n = 0; n < str.length; n++) {
      var c = str.charCodeAt(n);
      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if ((c > 127) && (c < 2048)) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }
    return utftext;
  }

  /**
   * HMAC-SHA256算法实现 - 修复版
   */
  hmacSHA256(message, key) {
    const blockSize = 64;
    
    // 如果密钥长度超过块大小，使用SHA256哈希处理
    if (key.length > blockSize) {
      const hashedKey = this.sha256(key);
      key = this.hexToString(hashedKey);
    }
    
    // 将密钥填充到块大小
    while (key.length < blockSize) {
      key += '\x00';
    }
    
    // 创建内外填充
    let oKeyPad = '';
    let iKeyPad = '';
    
    for (let i = 0; i < blockSize; i++) {
      const keyByte = key.charCodeAt(i);
      oKeyPad += String.fromCharCode(keyByte ^ 0x5c);
      iKeyPad += String.fromCharCode(keyByte ^ 0x36);
    }
    
    // 计算HMAC
    const innerHash = this.sha256(iKeyPad + message);
    const outerHash = this.sha256(oKeyPad + this.hexToString(innerHash));
    
    return outerHash;
  }

  /**
   * SHA256算法实现
   */
  sha256(str) {
    // 常量
    const K = [
      0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
      0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
      0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
      0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
      0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
      0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
      0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
      0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
    ];
    
    // 初始哈希值
    const H = [
      0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
    ];
    
    // 辅助函数
    function ROTR(x, n) {
      return (x >>> n) | (x << (32 - n));
    }
    
    function Ch(x, y, z) {
      return (x & y) ^ (~x & z);
    }
    
    function Maj(x, y, z) {
      return (x & y) ^ (x & z) ^ (y & z);
    }
    
    function Sigma0(x) {
      return ROTR(x, 2) ^ ROTR(x, 13) ^ ROTR(x, 22);
    }
    
    function Sigma1(x) {
      return ROTR(x, 6) ^ ROTR(x, 11) ^ ROTR(x, 25);
    }
    
    function sigma0(x) {
      return ROTR(x, 7) ^ ROTR(x, 18) ^ (x >>> 3);
    }
    
    function sigma1(x) {
      return ROTR(x, 17) ^ ROTR(x, 19) ^ (x >>> 10);
    }
    
    // 预处理
    str = this.utf8Encode(str);
    const strLen = str.length;
    const wordArray = [];
    
    // 将字符串转换为字节数组
    for (let i = 0; i < strLen; i++) {
      wordArray[i >> 2] |= str.charCodeAt(i) << (24 - (i % 4) * 8);
    }
    
    // 添加填充
    wordArray[strLen >> 2] |= 0x80 << (24 - (strLen % 4) * 8);
    wordArray[(((strLen + 8) >> 6) << 4) + 15] = strLen * 8;
    
    // 处理块
    const w = new Array(64);
    let a, b, c, d, e, f, g, h, i, j;
    let t1, t2;
    
    for (i = 0; i < wordArray.length; i += 16) {
      a = H[0];
      b = H[1];
      c = H[2];
      d = H[3];
      e = H[4];
      f = H[5];
      g = H[6];
      h = H[7];
      
      for (j = 0; j < 64; j++) {
        if (j < 16) {
          w[j] = wordArray[i + j] || 0;
        } else {
          w[j] = (sigma1(w[j - 2]) + w[j - 7] + sigma0(w[j - 15]) + w[j - 16]) >>> 0;
        }
        
        t1 = (h + Sigma1(e) + Ch(e, f, g) + K[j] + w[j]) >>> 0;
        t2 = (Sigma0(a) + Maj(a, b, c)) >>> 0;
        
        h = g;
        g = f;
        f = e;
        e = (d + t1) >>> 0;
        d = c;
        c = b;
        b = a;
        a = (t1 + t2) >>> 0;
      }
      
      H[0] = (H[0] + a) >>> 0;
      H[1] = (H[1] + b) >>> 0;
      H[2] = (H[2] + c) >>> 0;
      H[3] = (H[3] + d) >>> 0;
      H[4] = (H[4] + e) >>> 0;
      H[5] = (H[5] + f) >>> 0;
      H[6] = (H[6] + g) >>> 0;
      H[7] = (H[7] + h) >>> 0;
    }
    
    // 输出
    let result = '';
    for (i = 0; i < 8; i++) {
      result += ('00000000' + H[i].toString(16)).slice(-8);
    }
    
    return result;
  }

  /**
   * 十六进制字符串转字符串
   */
  hexToString(hex) {
    let str = '';
    for (let i = 0; i < hex.length; i += 2) {
      str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
    }
    return str;
  }

  /**
   * 十六进制字符串转字节数组
   */
  hexToBytes(hex) {
    // 确保hex是偶数长度
    if (hex.length % 2 !== 0) {
      hex = '0' + hex;
    }
    
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
    }
    return bytes;
  }

  /**
   * Base64编码
   */
  base64Encode(input) {
    // 微信小程序环境
    if (typeof wx !== 'undefined' && wx.arrayBufferToBase64) {
      if (typeof input === 'string') {
        // 字符串转ArrayBuffer再编码
        return wx.arrayBufferToBase64(this.stringToArrayBuffer(input));
      } else if (input instanceof ArrayBuffer) {
        // ArrayBuffer直接编码
        return wx.arrayBufferToBase64(input);
      } else if (input instanceof Uint8Array) {
        // Uint8Array转ArrayBuffer再编码
        return wx.arrayBufferToBase64(input.buffer.slice(input.byteOffset, input.byteOffset + input.byteLength));
      }
    }
    
    // Node.js环境
    if (typeof Buffer !== 'undefined') {
      if (typeof input === 'string') {
        return Buffer.from(input, 'binary').toString('base64');
      } else if (input instanceof Uint8Array) {
        return Buffer.from(input).toString('base64');
      } else if (input instanceof ArrayBuffer) {
        return Buffer.from(input).toString('base64');
      }
    }
    
    // 浏览器环境或其他环境
    return this.simpleBase64Encode(input);
  }

  /**
   * 简单Base64编码实现
   */
  simpleBase64Encode(input) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let str = '';
    
    if (typeof input === 'string') {
      str = input;
    } else {
      const bytes = input instanceof Uint8Array ? input : new Uint8Array(input);
      for (let i = 0; i < bytes.length; i++) {
        str += String.fromCharCode(bytes[i]);
      }
    }
    
    let i = 0;
    while (i < str.length) {
      const a = str.charCodeAt(i++);
      const b = i < str.length ? str.charCodeAt(i++) : 0;
      const c = i < str.length ? str.charCodeAt(i++) : 0;
      
      const bitmap = (a << 16) | (b << 8) | c;
      
      result += chars.charAt((bitmap >> 18) & 63);
      result += chars.charAt((bitmap >> 12) & 63);
      result += i - 2 < str.length ? chars.charAt((bitmap >> 6) & 63) : '=';
      result += i - 1 < str.length ? chars.charAt(bitmap & 63) : '=';
    }
    
    return result;
  }

  /**
   * 字符串转ArrayBuffer
   */
  stringToArrayBuffer(str) {
    const buf = new ArrayBuffer(str.length);
    const bufView = new Uint8Array(buf);
    for (let i = 0; i < str.length; i++) {
      bufView[i] = str.charCodeAt(i);
    }
    return buf;
  }

  /**
   * 生成Content-MD5
   * 修复后的正确实现 - 不进行UTF-8编码
   */
  generateContentMD5(body) {
    // 空字符串的处理
    if (!body || body === '') {
      const emptyMd5 = this.md5Raw('');
      const emptyBytes = this.hexToBytes(emptyMd5);
      return this.base64Encode(emptyBytes);
    }
    
    const bodyStr = typeof body === 'string' ? body : JSON.stringify(body);
    
    console.log('Content-MD5 Body字符串:', bodyStr);
    
    // 计算MD5哈希 - 使用原始字符串，不进行UTF-8编码
    const md5Hash = this.md5Raw(bodyStr);
    console.log('MD5哈希值:', md5Hash);
    
    // 将MD5十六进制字符串转换为字节数组
    const md5Bytes = this.hexToBytes(md5Hash);
    
    // Base64编码
    const base64Result = this.base64Encode(md5Bytes);
    console.log('最终Content-MD5:', base64Result);
    
    return base64Result;
  }

  /**
   * 标准MD5算法实现 - 经过验证的版本
   */
  md5Raw(str) {
    function rotateLeft(lValue, iShiftBits) {
      return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
    }

    function addUnsigned(lX, lY) {
      var lX4, lY4, lX8, lY8, lResult;
      lX8 = (lX & 0x80000000);
      lY8 = (lY & 0x80000000);
      lX4 = (lX & 0x40000000);
      lY4 = (lY & 0x40000000);
      lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
      if (lX4 & lY4) {
        return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
      }
      if (lX4 | lY4) {
        if (lResult & 0x40000000) {
          return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
        } else {
          return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
        }
      } else {
        return (lResult ^ lX8 ^ lY8);
      }
    }

    function F(x, y, z) { return (x & y) | ((~x) & z); }
    function G(x, y, z) { return (x & z) | (y & (~z)); }
    function H(x, y, z) { return (x ^ y ^ z); }
    function I(x, y, z) { return (y ^ (x | (~z))); }

    function FF(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function GG(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function HH(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function II(a, b, c, d, x, s, ac) {
      a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac));
      return addUnsigned(rotateLeft(a, s), b);
    }

    function convertToWordArray(str) {
      var lWordCount;
      var lMessageLength = str.length;
      var lNumberOfWords_temp1 = lMessageLength + 8;
      var lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
      var lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
      var lWordArray = Array(lNumberOfWords - 1);
      var lBytePosition = 0;
      var lByteCount = 0;
      while (lByteCount < lMessageLength) {
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] = (lWordArray[lWordCount] | (str.charCodeAt(lByteCount) << lBytePosition));
        lByteCount++;
      }
      lWordCount = (lByteCount - (lByteCount % 4)) / 4;
      lBytePosition = (lByteCount % 4) * 8;
      lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
      lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
      lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
      return lWordArray;
    }

    function wordToHex(lValue) {
      var wordToHexValue = "", wordToHexValue_temp = "", lByte, lCount;
      for (lCount = 0; lCount <= 3; lCount++) {
        lByte = (lValue >>> (lCount * 8)) & 255;
        wordToHexValue_temp = "0" + lByte.toString(16);
        wordToHexValue = wordToHexValue + wordToHexValue_temp.substr(wordToHexValue_temp.length - 2, 2);
      }
      return wordToHexValue;
    }

    var x = Array();
    var k, AA, BB, CC, DD, a, b, c, d;
    var S11 = 7, S12 = 12, S13 = 17, S14 = 22;
    var S21 = 5, S22 = 9, S23 = 14, S24 = 20;
    var S31 = 4, S32 = 11, S33 = 16, S34 = 23;
    var S41 = 6, S42 = 10, S43 = 15, S44 = 21;

    x = convertToWordArray(str);
    a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;

    for (k = 0; k < x.length; k += 16) {
      AA = a; BB = b; CC = c; DD = d;
      a = FF(a, b, c, d, x[k + 0], S11, 0xD76AA478);
      d = FF(d, a, b, c, x[k + 1], S12, 0xE8C7B756);
      c = FF(c, d, a, b, x[k + 2], S13, 0x242070DB);
      b = FF(b, c, d, a, x[k + 3], S14, 0xC1BDCEEE);
      a = FF(a, b, c, d, x[k + 4], S11, 0xF57C0FAF);
      d = FF(d, a, b, c, x[k + 5], S12, 0x4787C62A);
      c = FF(c, d, a, b, x[k + 6], S13, 0xA8304613);
      b = FF(b, c, d, a, x[k + 7], S14, 0xFD469501);
      a = FF(a, b, c, d, x[k + 8], S11, 0x698098D8);
      d = FF(d, a, b, c, x[k + 9], S12, 0x8B44F7AF);
      c = FF(c, d, a, b, x[k + 10], S13, 0xFFFF5BB1);
      b = FF(b, c, d, a, x[k + 11], S14, 0x895CD7BE);
      a = FF(a, b, c, d, x[k + 12], S11, 0x6B901122);
      d = FF(d, a, b, c, x[k + 13], S12, 0xFD987193);
      c = FF(c, d, a, b, x[k + 14], S13, 0xA679438E);
      b = FF(b, c, d, a, x[k + 15], S14, 0x49B40821);
      a = GG(a, b, c, d, x[k + 1], S21, 0xF61E2562);
      d = GG(d, a, b, c, x[k + 6], S22, 0xC040B340);
      c = GG(c, d, a, b, x[k + 11], S23, 0x265E5A51);
      b = GG(b, c, d, a, x[k + 0], S24, 0xE9B6C7AA);
      a = GG(a, b, c, d, x[k + 5], S21, 0xD62F105D);
      d = GG(d, a, b, c, x[k + 10], S22, 0x02441453);
      c = GG(c, d, a, b, x[k + 15], S23, 0xD8A1E681);
      b = GG(b, c, d, a, x[k + 4], S24, 0xE7D3FBC8);
      a = GG(a, b, c, d, x[k + 9], S21, 0x21E1CDE6);
      d = GG(d, a, b, c, x[k + 14], S22, 0xC33707D6);
      c = GG(c, d, a, b, x[k + 3], S23, 0xF4D50D87);
      b = GG(b, c, d, a, x[k + 8], S24, 0x455A14ED);
      a = GG(a, b, c, d, x[k + 13], S21, 0xA9E3E905);
      d = GG(d, a, b, c, x[k + 2], S22, 0xFCEFA3F8);
      c = GG(c, d, a, b, x[k + 7], S23, 0x676F02D9);
      b = GG(b, c, d, a, x[k + 12], S24, 0x8D2A4C8A);
      a = HH(a, b, c, d, x[k + 5], S31, 0xFFFA3942);
      d = HH(d, a, b, c, x[k + 8], S32, 0x8771F681);
      c = HH(c, d, a, b, x[k + 11], S33, 0x6D9D6122);
      b = HH(b, c, d, a, x[k + 14], S34, 0xFDE5380C);
      a = HH(a, b, c, d, x[k + 1], S31, 0xA4BEEA44);
      d = HH(d, a, b, c, x[k + 4], S32, 0x4BDECFA9);
      c = HH(c, d, a, b, x[k + 7], S33, 0xF6BB4B60);
      b = HH(b, c, d, a, x[k + 10], S34, 0xBEBFBC70);
      a = HH(a, b, c, d, x[k + 13], S31, 0x289B7EC6);
      d = HH(d, a, b, c, x[k + 0], S32, 0xEAA127FA);
      c = HH(c, d, a, b, x[k + 3], S33, 0xD4EF3085);
      b = HH(b, c, d, a, x[k + 6], S34, 0x04881D05);
      a = HH(a, b, c, d, x[k + 9], S31, 0xD9D4D039);
      d = HH(d, a, b, c, x[k + 12], S32, 0xE6DB99E5);
      c = HH(c, d, a, b, x[k + 15], S33, 0x1FA27CF8);
      b = HH(b, c, d, a, x[k + 2], S34, 0xC4AC5665);
      a = II(a, b, c, d, x[k + 0], S41, 0xF4292244);
      d = II(d, a, b, c, x[k + 7], S42, 0x432AFF97);
      c = II(c, d, a, b, x[k + 14], S43, 0xAB9423A7);
      b = II(b, c, d, a, x[k + 5], S44, 0xFC93A039);
      a = II(a, b, c, d, x[k + 12], S41, 0x655B59C3);
      d = II(d, a, b, c, x[k + 3], S42, 0x8F0CCC92);
      c = II(c, d, a, b, x[k + 10], S43, 0xFFEFF47D);
      b = II(b, c, d, a, x[k + 1], S44, 0x85845DD1);
      a = II(a, b, c, d, x[k + 8], S41, 0x6FA87E4F);
      d = II(d, a, b, c, x[k + 15], S42, 0xFE2CE6E0);
      c = II(c, d, a, b, x[k + 6], S43, 0xA3014314);
      b = II(b, c, d, a, x[k + 13], S44, 0x4E0811A1);
      a = II(a, b, c, d, x[k + 4], S41, 0xF7537E82);
      d = II(d, a, b, c, x[k + 11], S42, 0xBD3AF235);
      c = II(c, d, a, b, x[k + 2], S43, 0x2AD7D2BB);
      b = II(b, c, d, a, x[k + 9], S44, 0xEB86D391);
      a = addUnsigned(a, AA);
      b = addUnsigned(b, BB);
      c = addUnsigned(c, CC);
      d = addUnsigned(d, DD);
    }

    var temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
    return temp.toLowerCase();
  }
}

module.exports = new CryptoUtil();