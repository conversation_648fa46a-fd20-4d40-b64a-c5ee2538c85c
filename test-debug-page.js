/**
 * 小程序调试页面
 * 在小程序中使用这个文件来测试API
 */

// 引入调试版本的API服务
const apiServiceDebug = require('./src/utils/api-service-miniprogram-debug.js')

Page({
  data: {
    testResult: '',
    isLoading: false
  },

  onLoad() {
    console.log('🔧 调试页面加载')
    this.runDebugTest()
  },

  async runDebugTest() {
    console.log('🚀 开始运行调试测试')
    
    this.setData({
      isLoading: true,
      testResult: '正在测试...'
    })

    try {
      // 测试API调用
      const result = await apiServiceDebug.testAPI()
      
      this.setData({
        isLoading: false,
        testResult: `✅ 测试成功!\n响应: ${JSON.stringify(result, null, 2)}`
      })
      
      console.log('🎉 调试测试成功')
      
    } catch (error) {
      console.error('💥 调试测试失败:', error)
      
      this.setData({
        isLoading: false,
        testResult: `❌ 测试失败: ${error.message}\n\n请查看控制台获取详细信息`
      })
      
      // 提供详细的错误分析
      console.log('🔍 错误分析建议:')
      
      if (error.message.includes('签名验证失败')) {
        console.log('签名验证失败可能的原因:')
        console.log('1. 🔑 API密钥问题:')
        console.log('   - AppKey或Secret配置错误')
        console.log('   - 密钥已过期或被重置')
        console.log('   - 复制时包含了多余字符')
        console.log('')
        console.log('2. 📋 账户状态问题:')
        console.log('   - 账户未完成实名认证')
        console.log('   - 应用审核未通过')
        console.log('   - 账户被暂停或限制')
        console.log('')
        console.log('3. 🔐 权限问题:')
        console.log('   - 应用没有相应API权限')
        console.log('   - 接口需要特殊申请')
        console.log('')
        console.log('4. 🌐 网络问题:')
        console.log('   - IP地址被限制')
        console.log('   - 网络环境问题')
        console.log('')
        console.log('建议解决步骤:')
        console.log('1. 登录美团联盟后台重新获取密钥')
        console.log('2. 检查账户和应用状态')
        console.log('3. 确认API权限配置')
        console.log('4. 联系美团技术支持')
      }
    }
  },

  // 手动重新测试
  onRetryTest() {
    this.runDebugTest()
  },

  // 复制结果到剪贴板
  onCopyResult() {
    wx.setClipboardData({
      data: this.data.testResult,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  }
})

/**
 * 对应的WXML文件内容:
 * 
 * <view class="container">
 *   <view class="title">美团API调试测试</view>
 *   
 *   <view class="status" wx:if="{{isLoading}}">
 *     <text>正在测试中...</text>
 *   </view>
 *   
 *   <view class="result">
 *     <text class="result-text">{{testResult}}</text>
 *   </view>
 *   
 *   <view class="buttons">
 *     <button bindtap="onRetryTest" disabled="{{isLoading}}">重新测试</button>
 *     <button bindtap="onCopyResult" disabled="{{isLoading}}">复制结果</button>
 *   </view>
 *   
 *   <view class="tips">
 *     <text class="tips-title">使用说明:</text>
 *     <text class="tips-text">1. 查看控制台获取详细日志</text>
 *     <text class="tips-text">2. 如果测试失败，按照控制台建议操作</text>
 *     <text class="tips-text">3. 可以复制结果发送给技术支持</text>
 *   </view>
 * </view>
 */

/**
 * 对应的WXSS文件内容:
 * 
 * .container {
 *   padding: 20rpx;
 *   background-color: #f5f5f5;
 *   min-height: 100vh;
 * }
 * 
 * .title {
 *   font-size: 36rpx;
 *   font-weight: bold;
 *   text-align: center;
 *   margin-bottom: 40rpx;
 *   color: #333;
 * }
 * 
 * .status {
 *   text-align: center;
 *   margin-bottom: 20rpx;
 *   color: #666;
 * }
 * 
 * .result {
 *   background-color: #fff;
 *   border-radius: 10rpx;
 *   padding: 20rpx;
 *   margin-bottom: 30rpx;
 *   box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
 * }
 * 
 * .result-text {
 *   font-size: 28rpx;
 *   line-height: 1.6;
 *   white-space: pre-wrap;
 *   word-break: break-all;
 * }
 * 
 * .buttons {
 *   display: flex;
 *   justify-content: space-around;
 *   margin-bottom: 40rpx;
 * }
 * 
 * .buttons button {
 *   width: 200rpx;
 *   height: 80rpx;
 *   line-height: 80rpx;
 *   font-size: 28rpx;
 * }
 * 
 * .tips {
 *   background-color: #fff;
 *   border-radius: 10rpx;
 *   padding: 20rpx;
 *   box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
 * }
 * 
 * .tips-title {
 *   font-size: 32rpx;
 *   font-weight: bold;
 *   color: #333;
 *   display: block;
 *   margin-bottom: 20rpx;
 * }
 * 
 * .tips-text {
 *   font-size: 28rpx;
 *   color: #666;
 *   display: block;
 *   margin-bottom: 10rpx;
 * }
 */
