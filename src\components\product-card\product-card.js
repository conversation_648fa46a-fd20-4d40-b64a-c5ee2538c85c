/**
 * 商品卡片组件
 */
const { formatPrice, formatDistance } = require('../../utils/common.js')

Component({
  properties: {
    product: {
      type: Object,
      value: {}
    },
    layout: {
      type: String,
      value: 'waterfall' // waterfall | list
    },
    showDistance: {
      type: Boolean,
      value: true
    },
    showTags: {
      type: Boolean,
      value: true
    }
  },

  data: {
    imageLoaded: false,
    imageError: false
  },

  methods: {
    onCardTap() {
      this.triggerEvent('tap', {
        product: this.data.product
      })
    },

    onImageLoad() {
      this.setData({
        imageLoaded: true
      })
    },

    onImageError() {
      this.setData({
        imageError: true
      })
    },

    onActionTap(e) {
      e.stopPropagation()
      this.triggerEvent('action', {
        product: this.data.product
      })
    }
  },

  observers: {
    'product': function(product) {
      if (product && product.price) {
        this.setData({
          formattedPrice: formatPrice(product.price),
          formattedOriginalPrice: product.originalPrice ? formatPrice(product.originalPrice) : null,
          formattedDistance: product.distance ? formatDistance(product.distance) : null
        })
      }
    }
  }
})