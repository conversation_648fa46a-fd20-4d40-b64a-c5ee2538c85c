# 组件库文档

## 🧩 组件概览

本组件库基于微信小程序原生框架开发，遵循美团品牌设计规范，提供一套完整的UI组件，确保整个小程序的视觉一致性和交互体验。

## 📱 页面组件

### 1. 瀑布流组件 (WaterfallList)

**功能描述**: 双列瀑布流布局，支持虚拟滚动和懒加载

**使用场景**: 首页商品展示、搜索结果展示

**组件结构**:
```
components/waterfall-list/
├── waterfall-list.wxml
├── waterfall-list.wxss
├── waterfall-list.js
└── waterfall-list.json
```

**属性配置**:
```javascript
properties: {
  items: {
    type: Array,
    value: []
  },
  columnCount: {
    type: Number,
    value: 2
  },
  columnGap: {
    type: Number,
    value: 16
  },
  loading: {
    type: Boolean,
    value: false
  },
  hasMore: {
    type: Boolean,
    value: true
  }
}
```

**事件回调**:
```javascript
methods: {
  onItemTap(e) {
    const { item, index } = e.currentTarget.dataset
    this.triggerEvent('itemtap', { item, index })
  },
  
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.triggerEvent('loadmore')
    }
  }
}
```

**使用示例**:
```xml
<waterfall-list 
  items="{{products}}"
  loading="{{loading}}"
  has-more="{{hasMore}}"
  bind:itemtap="onProductTap"
  bind:loadmore="onLoadMore">
</waterfall-list>
```

### 2. 商品卡片组件 (ProductCard)

**功能描述**: 商品信息展示卡片，支持多种布局模式

**使用场景**: 瀑布流商品展示、列表商品展示

**组件结构**:
```
components/product-card/
├── product-card.wxml
├── product-card.wxss
├── product-card.js
└── product-card.json
```

**属性配置**:
```javascript
properties: {
  product: {
    type: Object,
    value: {}
  },
  layout: {
    type: String,
    value: 'waterfall' // waterfall | list
  },
  showDistance: {
    type: Boolean,
    value: true
  },
  showTags: {
    type: Boolean,
    value: true
  }
}
```

**WXML模板**:
```xml
<view class="product-card product-card--{{layout}}" bind:tap="onCardTap">
  <image class="product-image" src="{{product.image}}" mode="aspectFill" lazy-load />
  
  <view class="product-content">
    <text class="product-title">{{product.title}}</text>
    
    <view class="product-price">
      <text class="price-current">¥{{product.price}}</text>
      <text class="price-original" wx:if="{{product.originalPrice}}">¥{{product.originalPrice}}</text>
    </view>
    
    <view class="product-tags" wx:if="{{showTags && product.tags.length}}">
      <text class="product-tag" wx:for="{{product.tags}}" wx:key="*this">{{item}}</text>
    </view>
    
    <view class="product-footer">
      <text class="shop-name">{{product.shopName}}</text>
      <text class="shop-distance" wx:if="{{showDistance}}">{{product.distance}}</text>
    </view>
  </view>
  
  <view class="product-action" wx:if="{{layout === 'list'}}">
    <button class="action-button">抢</button>
  </view>
</view>
```

### 3. 搜索栏组件 (SearchBar)

**功能描述**: 搜索输入框，支持历史记录和热门搜索

**使用场景**: 页面顶部搜索、搜索页面

**组件结构**:
```
components/search-bar/
├── search-bar.wxml
├── search-bar.wxss
├── search-bar.js
└── search-bar.json
```

**属性配置**:
```javascript
properties: {
  placeholder: {
    type: String,
    value: '搜索商品'
  },
  value: {
    type: String,
    value: ''
  },
  showHistory: {
    type: Boolean,
    value: false
  },
  historyList: {
    type: Array,
    value: []
  },
  hotKeywords: {
    type: Array,
    value: []
  }
}
```

**WXML模板**:
```xml
<view class="search-bar">
  <view class="search-input-wrapper">
    <input 
      class="search-input"
      placeholder="{{placeholder}}"
      value="{{value}}"
      bind:input="onInput"
      bind:confirm="onSearch"
      confirm-type="search"
    />
    <button class="search-button" bind:tap="onSearch">搜索</button>
  </view>
  
  <view class="search-suggestions" wx:if="{{showHistory}}">
    <view class="suggestion-section" wx:if="{{historyList.length}}">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <button class="clear-button" bind:tap="onClearHistory">清空</button>
      </view>
      <view class="keyword-list">
        <text 
          class="keyword-item" 
          wx:for="{{historyList}}" 
          wx:key="*this"
          bind:tap="onKeywordTap"
          data-keyword="{{item}}">
          {{item}}
        </text>
      </view>
    </view>
    
    <view class="suggestion-section" wx:if="{{hotKeywords.length}}">
      <text class="section-title">热门搜索</text>
      <view class="keyword-list">
        <text 
          class="keyword-item keyword-item--hot" 
          wx:for="{{hotKeywords}}" 
          wx:key="*this"
          bind:tap="onKeywordTap"
          data-keyword="{{item}}">
          {{item}}
        </text>
      </view>
    </view>
  </view>
</view>
```

### 4. 城市选择器组件 (CitySelector)

**功能描述**: 城市选择器，支持定位和手动选择

**使用场景**: 页面顶部城市切换

**组件结构**:
```
components/city-selector/
├── city-selector.wxml
├── city-selector.wxss
├── city-selector.js
└── city-selector.json
```

**属性配置**:
```javascript
properties: {
  currentCity: {
    type: String,
    value: '广州'
  },
  showModal: {
    type: Boolean,
    value: false
  }
}
```

**WXML模板**:
```xml
<view class="city-selector">
  <view class="city-current" bind:tap="onShowModal">
    <text class="city-name">{{currentCity}}</text>
    <view class="city-arrow"></view>
  </view>
  
  <view class="city-modal" wx:if="{{showModal}}">
    <view class="modal-mask" bind:tap="onHideModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择城市</text>
        <button class="modal-close" bind:tap="onHideModal">×</button>
      </view>
      
      <view class="location-section" wx:if="{{locationCity}}">
        <text class="section-title">当前定位</text>
        <button class="location-city" bind:tap="onSelectCity" data-city="{{locationCity}}">
          <text class="city-name">{{locationCity}}</text>
          <text class="location-icon">📍</text>
        </button>
      </view>
      
      <view class="hot-cities-section">
        <text class="section-title">热门城市</text>
        <view class="cities-grid">
          <button 
            class="city-item" 
            wx:for="{{hotCities}}" 
            wx:key="*this"
            bind:tap="onSelectCity"
            data-city="{{item}}">
            {{item}}
          </button>
        </view>
      </view>
      
      <view class="all-cities-section">
        <text class="section-title">全部城市</text>
        <scroll-view class="cities-list" scroll-y>
          <view class="city-group" wx:for="{{cityGroups}}" wx:key="letter">
            <text class="group-letter">{{item.letter}}</text>
            <button 
              class="city-item" 
              wx:for="{{item.cities}}" 
              wx:key="*this"
              wx:for-item="city"
              bind:tap="onSelectCity"
              data-city="{{city}}">
              {{city}}
            </button>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</view>
```

## 🎨 基础组件

### 1. 按钮组件 (Button)

**功能描述**: 统一样式的按钮组件

**组件结构**:
```
components/button/
├── button.wxml
├── button.wxss
├── button.js
└── button.json
```

**属性配置**:
```javascript
properties: {
  type: {
    type: String,
    value: 'primary' // primary | secondary | ghost | text
  },
  size: {
    type: String,
    value: 'medium' // large | medium | small | mini
  },
  disabled: {
    type: Boolean,
    value: false
  },
  loading: {
    type: Boolean,
    value: false
  },
  block: {
    type: Boolean,
    value: false
  }
}
```

**WXSS样式**:
```css
.custom-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.custom-button--primary {
  background: var(--primary-yellow);
  color: var(--text-primary);
}

.custom-button--secondary {
  background: var(--primary-orange);
  color: var(--text-white);
}

.custom-button--ghost {
  background: transparent;
  border: 1px solid var(--primary-orange);
  color: var(--primary-orange);
}

.custom-button--text {
  background: transparent;
  color: var(--primary-orange);
}

.custom-button--large {
  height: 48px;
  padding: 0 24px;
  font-size: var(--font-size-large);
}

.custom-button--medium {
  height: 40px;
  padding: 0 20px;
  font-size: var(--font-size-medium);
}

.custom-button--small {
  height: 32px;
  padding: 0 16px;
  font-size: var(--font-size-small);
}

.custom-button--mini {
  height: 24px;
  padding: 0 12px;
  font-size: var(--font-size-mini);
}

.custom-button--block {
  width: 100%;
}

.custom-button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.custom-button--loading {
  color: transparent;
}

.button-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
```

### 2. 标签组件 (Tag)

**功能描述**: 标签展示组件

**属性配置**:
```javascript
properties: {
  type: {
    type: String,
    value: 'default' // default | primary | success | warning | danger
  },
  size: {
    type: String,
    value: 'medium' // large | medium | small
  },
  closable: {
    type: Boolean,
    value: false
  }
}
```

**WXML模板**:
```xml
<view class="tag tag--{{type}} tag--{{size}}">
  <text class="tag-text">{{text}}</text>
  <view class="tag-close" wx:if="{{closable}}" bind:tap="onClose">×</view>
</view>
```

### 3. 加载组件 (Loading)

**功能描述**: 加载状态展示

**组件类型**:
- 页面加载
- 列表加载更多
- 按钮加载
- 骨架屏加载

**WXML模板**:
```xml
<!-- 页面加载 -->
<view class="loading-page" wx:if="{{type === 'page'}}">
  <view class="loading-spinner"></view>
  <text class="loading-text">{{text || '加载中...'}}</text>
</view>

<!-- 列表加载更多 -->
<view class="loading-more" wx:if="{{type === 'more'}}">
  <view class="loading-spinner loading-spinner--small"></view>
  <text class="loading-text">{{text || '加载更多...'}}</text>
</view>

<!-- 骨架屏 -->
<view class="loading-skeleton" wx:if="{{type === 'skeleton'}}">
  <view class="skeleton-item" wx:for="{{skeletonCount}}" wx:key="*this">
    <view class="skeleton-image"></view>
    <view class="skeleton-content">
      <view class="skeleton-title"></view>
      <view class="skeleton-desc"></view>
      <view class="skeleton-price"></view>
    </view>
  </view>
</view>
```

## 🔧 工具组件

### 1. 图片懒加载组件 (LazyImage)

**功能描述**: 图片懒加载，支持占位图和加载失败处理

**属性配置**:
```javascript
properties: {
  src: {
    type: String,
    value: ''
  },
  placeholder: {
    type: String,
    value: '/images/placeholder.png'
  },
  errorImage: {
    type: String,
    value: '/images/error.png'
  },
  mode: {
    type: String,
    value: 'aspectFill'
  },
  lazyLoad: {
    type: Boolean,
    value: true
  }
}
```

### 2. 空状态组件 (Empty)

**功能描述**: 空状态展示

**属性配置**:
```javascript
properties: {
  type: {
    type: String,
    value: 'default' // default | search | network | error
  },
  title: {
    type: String,
    value: ''
  },
  description: {
    type: String,
    value: ''
  },
  showButton: {
    type: Boolean,
    value: false
  },
  buttonText: {
    type: String,
    value: '重试'
  }
}
```

### 3. 下拉刷新组件 (PullRefresh)

**功能描述**: 下拉刷新功能

**使用示例**:
```xml
<pull-refresh bind:refresh="onRefresh" refreshing="{{refreshing}}">
  <view class="content">
    <!-- 页面内容 -->
  </view>
</pull-refresh>
```

## 📊 数据展示组件

### 1. 统计卡片组件 (StatCard)

**功能描述**: 个人中心统计信息展示

**WXML模板**:
```xml
<view class="stat-card">
  <view class="stat-item" wx:for="{{stats}}" wx:key="key">
    <view class="stat-icon">
      <image src="{{item.icon}}" mode="aspectFit" />
    </view>
    <view class="stat-content">
      <text class="stat-number">{{item.value}}</text>
      <text class="stat-label">{{item.label}}</text>
    </view>
  </view>
</view>
```

### 2. 功能入口组件 (MenuList)

**功能描述**: 个人中心功能入口列表

**WXML模板**:
```xml
<view class="menu-list">
  <view class="menu-item" wx:for="{{menuItems}}" wx:key="key" bind:tap="onMenuTap" data-item="{{item}}">
    <view class="menu-icon">
      <image src="{{item.icon}}" mode="aspectFit" />
    </view>
    <view class="menu-content">
      <text class="menu-title">{{item.title}}</text>
      <text class="menu-desc">{{item.description}}</text>
    </view>
    <view class="menu-arrow">></view>
  </view>
</view>
```

## 🎯 交互组件

### 1. 模态框组件 (Modal)

**功能描述**: 模态对话框

**属性配置**:
```javascript
properties: {
  visible: {
    type: Boolean,
    value: false
  },
  title: {
    type: String,
    value: ''
  },
  showClose: {
    type: Boolean,
    value: true
  },
  maskClosable: {
    type: Boolean,
    value: true
  }
}
```

### 2. 轻提示组件 (Toast)

**功能描述**: 消息提示

**使用方法**:
```javascript
// 在页面中使用
this.selectComponent('#toast').show({
  type: 'success',
  message: '操作成功',
  duration: 2000
})
```

### 3. 操作面板组件 (ActionSheet)

**功能描述**: 底部操作面板

**WXML模板**:
```xml
<view class="action-sheet" wx:if="{{visible}}">
  <view class="action-mask" bind:tap="onMaskTap"></view>
  <view class="action-content">
    <view class="action-header" wx:if="{{title}}">
      <text class="action-title">{{title}}</text>
    </view>
    <view class="action-body">
      <button 
        class="action-item" 
        wx:for="{{actions}}" 
        wx:key="key"
        bind:tap="onActionTap"
        data-action="{{item}}">
        {{item.text}}
      </button>
    </view>
    <view class="action-footer">
      <button class="action-cancel" bind:tap="onCancel">取消</button>
    </view>
  </view>
</view>
```

## 📋 表单组件

### 1. 输入框组件 (Input)

**功能描述**: 统一样式的输入框

**属性配置**:
```javascript
properties: {
  type: {
    type: String,
    value: 'text' // text | number | password | search
  },
  placeholder: {
    type: String,
    value: ''
  },
  value: {
    type: String,
    value: ''
  },
  disabled: {
    type: Boolean,
    value: false
  },
  clearable: {
    type: Boolean,
    value: false
  },
  showPassword: {
    type: Boolean,
    value: false
  }
}
```

### 2. 选择器组件 (Picker)

**功能描述**: 选择器组件

**类型支持**:
- 单列选择器
- 多列选择器
- 时间选择器
- 日期选择器

## 🚀 使用指南

### 组件引入

1. **在页面json中配置**:
```json
{
  "usingComponents": {
    "waterfall-list": "/components/waterfall-list/waterfall-list",
    "product-card": "/components/product-card/product-card",
    "search-bar": "/components/search-bar/search-bar"
  }
}
```

2. **在页面wxml中使用**:
```xml
<search-bar 
  placeholder="搜索商品"
  bind:search="onSearch">
</search-bar>

<waterfall-list 
  items="{{products}}"
  bind:itemtap="onProductTap">
</waterfall-list>
```

### 全局组件注册

在app.json中注册全局组件:
```json
{
  "usingComponents": {
    "custom-button": "/components/button/button",
    "custom-loading": "/components/loading/loading",
    "lazy-image": "/components/lazy-image/lazy-image"
  }
}
```

### 组件开发规范

1. **文件命名**: 使用kebab-case命名法
2. **属性定义**: 提供完整的属性类型和默认值
3. **事件命名**: 使用驼峰命名法，以on开头
4. **样式隔离**: 使用组件样式隔离
5. **文档注释**: 提供详细的组件说明和使用示例

---

*组件库持续更新中，如有问题请参考具体组件源码*