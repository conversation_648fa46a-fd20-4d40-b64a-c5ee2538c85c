/* 测试页面样式 */
.test-page {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666666;
}

/* 测试按钮区域 */
.test-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 48rpx;
}

/* 批量操作 */
.batch-actions {
  margin-bottom: 48rpx;
}

.action-row {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}

.action-row custom-button {
  flex: 1;
}

/* 测试结果 */
.test-results {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.results-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.results-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.results-list {
  height: 600rpx;
  padding: 0 32rpx;
}

/* 结果项 */
.result-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-type {
  font-size: 24rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  color: #ffffff;
}

.result-item.success .result-type {
  background: #52C41A;
}

.result-item.error .result-type {
  background: #FF4D4F;
}

.result-item.warning .result-type {
  background: #FAAD14;
}

.result-item.info .result-type {
  background: #1890FF;
}

.result-time {
  font-size: 24rpx;
  color: #999999;
}

.result-message {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  word-break: break-all;
}

/* 空状态 */
.empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: #cccccc;
}
