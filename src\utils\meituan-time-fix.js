/**
 * 美团API时间戳修复工具
 * 专门处理美团联盟API的时间戳过期问题
 */

const timeSync = require('./time-sync.js')

class MeituanTimeFix {
  constructor() {
    this.serverTimeOffset = 0 // 服务器时间偏移（毫秒）
    this.lastServerTime = 0 // 上次获取的服务器时间
    this.networkDelay = 0 // 网络延迟估算
    this.syncAttempts = 0 // 同步尝试次数
    this.maxSyncAttempts = 5 // 最大同步尝试次数
  }

  /**
   * 获取适合美团API的时间戳
   * @returns {number} 时间戳（毫秒）- 按照美团官方文档要求
   */
  getMeituanTimestamp() {
    // 获取当前时间戳（毫秒）- 美团要求毫秒数
    let currentTimestamp = Date.now()
    
    // 应用服务器时间偏移
    let adjustedTimestamp = currentTimestamp + this.serverTimeOffset
    
    // 应用安全缓冲 - 美团时间戳有效期为2分钟，我们提前15秒
    // 与官方实现保持一致，使用15秒安全缓冲
    const safetyBufferMs = 15000 // 15秒安全缓冲（毫秒）
    const finalTimestamp = adjustedTimestamp - safetyBufferMs
    
    console.log('美团时间戳生成:', {
      currentTimestamp: currentTimestamp,
      currentTime: new Date(currentTimestamp).toISOString(),
      serverOffsetMs: this.serverTimeOffset,
      adjustedTimestamp: adjustedTimestamp,
      adjustedTime: new Date(adjustedTimestamp).toISOString(),
      safetyBufferMs: safetyBufferMs,
      finalTimestamp: finalTimestamp,
      finalTime: new Date(finalTimestamp).toISOString(),
      validFor: Math.floor((currentTimestamp - finalTimestamp) / 1000) + '秒前',
      format: '毫秒格式（符合美团要求）',
      validPeriod: '2分钟有效期'
    })
    
    return finalTimestamp
  }

  /**
  /**
   * 从美团API响应中学习服务器时间
   * @param {Object} response 响应对象
   */
  learnFromResponse(response) {
    try {
      if (!response || !response.header) return
      
      // 尝试从多个可能的响应头中获取服务器时间
      const serverTimeStr = response.header['Date'] || 
                           response.header['date'] || 
                           response.header['Server-Time'] || 
                           response.header['server-time'] || 
                           response.header['X-Server-Time'] || 
                           response.header['x-server-time']
                           
      if (serverTimeStr) {
        const serverTime = this.parseServerTime(serverTimeStr)
        if (serverTime) {
          const clientTime = Date.now()
          // 计算服务器时间与客户端时间的差值
          // 如果服务器时间比客户端时间早，offset为负数
          // 如果服务器时间比客户端时间晚，offset为正数
          const offset = serverTime - clientTime
          
          // 更新服务器时间偏移
          this.updateServerOffset(offset)
          this.lastServerTime = serverTime
          
          // 记录详细的时间同步信息
          console.log('从美团API响应学习时间:', {
            serverTimeStr: serverTimeStr,
            serverTime: new Date(serverTime).toISOString(),
            clientTime: new Date(clientTime).toISOString(),
            rawOffset: offset,
            finalOffset: this.serverTimeOffset,
            explanation: offset < 0 ? '服务器时间比客户端早' : '服务器时间比客户端晚',
            offsetSeconds: Math.abs(offset) / 1000 + '秒',
            finalOffsetSeconds: Math.abs(this.serverTimeOffset) / 1000 + '秒',
            syncAttempts: this.syncAttempts,
            responseStatus: response.statusCode
          })
          
          // 如果偏移量很大，记录警告
          if (Math.abs(offset) > 30000) { // 30秒
            console.warn('服务器时间偏移较大:', {
              offsetSeconds: Math.abs(offset) / 1000 + '秒',
              action: '已应用平滑调整'
            })
          }
        }
      } else {
        console.log('响应中未找到服务器时间头', Object.keys(response.header))
      }
    } catch (error) {
      console.warn('从响应学习时间失败:', error)
    }
  }

  /**
   * 更新服务器时间偏移
   * @param {number} newOffset 新的偏移量（毫秒）
   */
  updateServerOffset(newOffset) {
    // 记录原始偏移量，用于日志
    const originalOffset = this.serverTimeOffset
    
    // 如果是第一次设置偏移
    if (this.serverTimeOffset === 0) {
      // 首次设置，直接使用新偏移量
      this.serverTimeOffset = newOffset
    } else {
      // 使用动态权重平均来平滑偏移变化
      // 如果新偏移与当前偏移差异较大，使用较小的权重
      const offsetDiff = Math.abs(newOffset - this.serverTimeOffset)
      let weight = 0.3 // 默认权重
      
      if (offsetDiff > 10000) { // 10秒以上的差异
        weight = 0.1 // 使用较小的权重，更保守地调整
      } else if (offsetDiff < 1000) { // 1秒以内的差异
        weight = 0.5 // 使用较大的权重，更积极地调整
      }
      
      this.serverTimeOffset = this.serverTimeOffset * (1 - weight) + newOffset * weight
    }
    
    // 限制偏移范围（不超过2分钟，因为有效期就是2分钟）
    const maxOffset = 2 * 60 * 1000 // 2分钟
    if (Math.abs(this.serverTimeOffset) > maxOffset) {
      console.warn('服务器时间偏移超过2分钟，限制为最大值:', {
        originalOffset: originalOffset,
        newRawOffset: newOffset,
        calculatedOffset: this.serverTimeOffset,
        maxAllowed: maxOffset,
        action: '限制为最大允许值'
      })
      
      // 限制为最大允许值，而不是直接重置为0
      this.serverTimeOffset = Math.sign(this.serverTimeOffset) * maxOffset
    }
    
    // 记录偏移量变化
    if (Math.abs(this.serverTimeOffset - originalOffset) > 1000) { // 变化超过1秒才记录
      console.log('服务器时间偏移已更新:', {
        oldOffset: originalOffset,
        newOffset: this.serverTimeOffset,
        changeMs: this.serverTimeOffset - originalOffset,
        changeSec: (this.serverTimeOffset - originalOffset) / 1000 + '秒'
      })
    }
  }

  /**
   * 解析服务器时间字符串
   * @param {string} timeStr 时间字符串
   * @returns {number|null} 时间戳（毫秒）
   */
  parseServerTime(timeStr) {
    try {
      // 处理GMT格式，兼容iOS
      if (timeStr.includes('GMT')) {
        // 例: "Fri, 25 Jul 2025 02:55:58 GMT"
        const match = timeStr.match(/(\w+),\s+(\d+)\s+(\w+)\s+(\d+)\s+(\d+):(\d+):(\d+)\s+GMT/)
        if (match) {
          const [, , day, monthStr, year, hour, minute, second] = match
          const monthMap = {
            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
            'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
            'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
          }
          const month = monthMap[monthStr]
          if (month) {
            const isoStr = `${year}-${month}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}.000Z`
            const date = new Date(isoStr)
            if (!isNaN(date.getTime())) {
              return date.getTime()
            }
          }
        }
      }
      
      // 尝试直接解析
      const date = new Date(timeStr)
      if (!isNaN(date.getTime())) {
        return date.getTime()
      }
      
      return null
    } catch (error) {
      console.warn('解析服务器时间失败:', error)
      return null
    }
  }

  /**
  /**
   * 处理时间戳过期错误
   * @param {Object} errorData 错误数据
   * @returns {boolean} 是否处理了错误
   */
  handleTimestampExpired(errorData) {
    if (this.isTimestampExpiredError(errorData)) {
      this.syncAttempts++
      
      if (this.syncAttempts <= this.maxSyncAttempts) {
        // 基于美团2分钟有效期的调整策略
        let adjustment
        if (this.syncAttempts === 1) {
          // 第一次：减少15秒
          adjustment = this.serverTimeOffset - 15000
        } else if (this.syncAttempts === 2) {
          // 第二次：减少30秒
          adjustment = this.serverTimeOffset - 30000
        } else if (this.syncAttempts === 3) {
          // 第三次：减少60秒
          adjustment = this.serverTimeOffset - 60000
        } else if (this.syncAttempts === 4) {
          // 第四次：减少90秒
          adjustment = this.serverTimeOffset - 90000
        } else {
          // 最后一次：重置为0
          adjustment = 0
        }
        
        this.serverTimeOffset = adjustment
        
        console.log(`时间戳过期处理 第${this.syncAttempts}次 (2分钟有效期):`, {
          previousOffset: this.serverTimeOffset,
          adjustment: adjustment,
          newOffset: this.serverTimeOffset,
          adjustmentSeconds: Math.abs(adjustment) / 1000 + '秒',
          strategy: this.syncAttempts <= 4 ? '渐进调整' : '重置'
        })
        
        return true
      }
    }
    
    return false
  }

  /**
   * 判断是否为时间戳过期错误
   * @param {Object} errorData 错误数据
   * @returns {boolean} 是否为时间戳过期错误
   */
  isTimestampExpiredError(errorData) {
    if (!errorData) return false
    
    const timestampErrors = [
      '请求时间戳已过期',
      'timestamp expired',
      'timestamp invalid',
      'request timestamp expired',
      '时间戳过期',
      '时间戳无效'
    ]
    
    const message = (errorData.message || '').toLowerCase()
    return errorData.code === 400 && timestampErrors.some(error => 
      message.includes(error.toLowerCase())
    )
  }

  /**
   * 重置同步状态
   */
  reset() {
    this.syncAttempts = 0
    this.serverTimeOffset = 0
    this.networkDelay = 0
    console.log('美团时间修复工具已重置')
  }

  /**
   * 获取状态信息
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      serverTimeOffset: this.serverTimeOffset,
      networkDelay: this.networkDelay,
      syncAttempts: this.syncAttempts,
      lastServerTime: this.lastServerTime,
      currentTimestamp: this.getMeituanTimestamp(),
      estimatedServerTime: new Date(Date.now() + this.serverTimeOffset).toISOString()
    }
  }
}

// 创建全局实例
const meituanTimeFix = new MeituanTimeFix()

module.exports = meituanTimeFix