/**
 * 美团API配置验证工具
 * 检查API配置是否正确
 */

console.log('=== 美团API配置验证 ===\n')

// 检查配置文件
const config = {
  appkey: '9498b0824d214ee4b65bfab1be6dbed0',
  secret: '2a1463cb8f364cafbfd8d2a19c48eb48'
}

console.log('1. 配置检查:')
console.log('AppKey:', config.appkey)
console.log('AppKey长度:', config.appkey.length)
console.log('AppKey格式:', /^[a-f0-9]{32}$/.test(config.appkey) ? '✅ 正确（32位十六进制）' : '❌ 格式错误')

console.log('Secret:', config.secret.substring(0, 8) + '...')
console.log('Secret长度:', config.secret.length)
console.log('Secret格式:', /^[a-f0-9]{32}$/.test(config.secret) ? '✅ 正确（32位十六进制）' : '❌ 格式错误')

console.log('\n2. 配置来源验证:')
console.log('这些配置应该来自美团联盟开放平台：')
console.log('- 登录 https://union.meituan.com/')
console.log('- 进入"开发者中心" -> "应用管理"')
console.log('- 查看应用详情获取AppKey和Secret')

console.log('\n3. 权限检查:')
console.log('确认应用是否有以下权限:')
console.log('- 商品查询权限')
console.log('- 优惠券查询权限')
console.log('- API调用权限')

console.log('\n4. 账户状态检查:')
console.log('确认美团联盟账户状态:')
console.log('- 账户是否已实名认证')
console.log('- 账户是否处于正常状态')
console.log('- 是否有未处理的违规记录')

console.log('\n5. API接口状态检查:')
console.log('确认API接口状态:')
console.log('- query_coupon接口是否可用')
console.log('- 是否有接口调用限制')
console.log('- 是否需要白名单配置')

console.log('\n6. 网络环境检查:')
console.log('确认网络环境:')
console.log('- 是否能正常访问 media.meituan.com')
console.log('- 是否有代理或防火墙限制')
console.log('- IP地址是否在允许范围内')

console.log('\n7. 时间同步检查:')
const now = new Date()
console.log('当前系统时间:', now.toISOString())
console.log('时区:', Intl.DateTimeFormat().resolvedOptions().timeZone)
console.log('时间戳:', now.getTime())

// 检查与标准时间的差异
const expectedTime = new Date('2025-01-25T10:35:00.000Z') // 假设的正确时间
const timeDiff = Math.abs(now.getTime() - expectedTime.getTime())
const daysDiff = Math.floor(timeDiff / (24 * 60 * 60 * 1000))

console.log('与预期时间差异:', daysDiff, '天')
if (daysDiff > 1) {
  console.log('⚠️  系统时间可能不准确，建议同步时间')
} else {
  console.log('✅ 系统时间基本正常')
}

console.log('\n8. 建议的排查步骤:')
console.log('如果签名验证仍然失败，请按以下步骤排查:')
console.log('')
console.log('步骤1: 验证API配置')
console.log('- 重新获取AppKey和Secret')
console.log('- 确认配置没有多余的空格或特殊字符')
console.log('- 检查配置是否复制完整')
console.log('')
console.log('步骤2: 检查账户状态')
console.log('- 登录美团联盟后台查看账户状态')
console.log('- 确认应用状态为"正常"')
console.log('- 检查是否有待处理的审核')
console.log('')
console.log('步骤3: 测试其他接口')
console.log('- 尝试调用其他简单的API接口')
console.log('- 确认是否是特定接口的问题')
console.log('')
console.log('步骤4: 联系技术支持')
console.log('- 如果以上步骤都无法解决')
console.log('- 可以联系美团联盟技术支持')
console.log('- 提供详细的错误日志和配置信息')

console.log('\n=== 验证完成 ===')

// 生成测试报告
const report = {
  timestamp: new Date().toISOString(),
  config: {
    appkey: config.appkey,
    appkeyLength: config.appkey.length,
    appkeyFormat: /^[a-f0-9]{32}$/.test(config.appkey),
    secretLength: config.secret.length,
    secretFormat: /^[a-f0-9]{32}$/.test(config.secret)
  },
  system: {
    time: now.toISOString(),
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timestamp: now.getTime(),
    timeDiffDays: daysDiff
  },
  recommendations: [
    '验证API配置的准确性',
    '检查美团联盟账户状态',
    '确认网络连接正常',
    '同步系统时间',
    '联系技术支持（如需要）'
  ]
}

console.log('\n测试报告已生成:')
console.log(JSON.stringify(report, null, 2))
