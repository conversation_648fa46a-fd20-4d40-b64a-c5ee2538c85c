# 微信小程序API时间戳过期问题修复说明

## 问题描述

微信小程序在调用美团联盟API时出现"请求时间戳已过期"错误，导致商品券查询和推荐商品获取功能无法正常工作。

## 修复方案

### 1. 核心问题分析

- **时间同步问题**：客户端与服务端时间不同步
- **时间戳精度问题**：时间戳生成和验证逻辑不够精确
- **重试机制缺陷**：时间戳过期后重试时未重新生成时间戳
- **网络延迟影响**：未考虑网络传输延迟对时间戳的影响

### 2. 修复内容

#### 2.1 新增时间同步工具 (`src/utils/time-sync.js`)

- **功能**：处理客户端与服务端时间差异
- **特性**：
  - 自动从HTTP响应头同步服务器时间
  - 支持主动同步时间（通过时间API）
  - 智能时间偏移计算和应用
  - 网络延迟补偿

#### 2.2 增强API服务 (`src/utils/api-service-fixed.js`)

- **时间戳生成优化**：
  - 集成时间同步工具
  - 每次请求重新生成时间戳
  - 增强日志记录

- **重试机制改进**：
  - 智能识别时间戳过期错误
  - 时间戳过期时自动重新同步时间
  - 指数退避重试策略

- **错误处理增强**：
  - 详细的错误日志
  - 多种时间戳过期错误格式识别

#### 2.3 加密工具优化 (`src/utils/crypto-fixed-final.js`)

- **时间戳验证**：
  - 新增时间戳有效性验证
  - 支持时间窗口验证
  - 时间偏移处理

## 3. 使用方法

### 3.1 基本使用

修复后的API服务使用方法保持不变：

```javascript
const apiService = require('./src/utils/api-service-fixed.js')

// 查询商品券
try {
  const result = await apiService.queryCoupon(latitude, longitude, options)
  console.log('查询成功:', result)
} catch (error) {
  console.error('查询失败:', error)
}

// 获取推荐商品
try {
  const products = await apiService.getRecommendProducts(page, pageSize)
  console.log('获取成功:', products)
} catch (error) {
  console.error('获取失败:', error)
}
```

### 3.2 时间同步工具使用

```javascript
const timeSync = require('./src/utils/time-sync.js')

// 获取校正后的时间戳
const timestamp = timeSync.getCurrentTimestamp()

// 手动同步时间
await timeSync.syncTime()

// 检查同步状态
const status = timeSync.getStatus()
console.log('时间同步状态:', status)
```

### 3.3 小程序集成

在小程序的 `app.js` 中添加：

```javascript
const timeSync = require('./utils/time-sync.js')

App({
  onLaunch() {
    // 启动时同步时间
    timeSync.autoSync()
  },
  
  onShow() {
    // 每次显示时检查是否需要同步
    timeSync.autoSync()
  }
})
```

## 4. 配置选项

### 4.1 时间同步配置

```javascript
// 在 time-sync.js 中可以调整的参数
const timeSync = require('./src/utils/time-sync.js')

// 设置同步间隔（默认5分钟）
timeSync.syncInterval = 5 * 60 * 1000

// 设置最大重试次数（默认3次）
timeSync.maxRetries = 3
```

### 4.2 API服务配置

```javascript
// 在 api-service-fixed.js 中可以调整的参数
const apiService = require('./src/utils/api-service-fixed.js')

// 设置重试次数（默认3次）
apiService.retryTimes = 3

// 设置重试延迟（默认1000ms）
apiService.retryDelay = 1000
```

## 5. 错误处理

### 5.1 常见错误类型

- **时间戳过期**：自动重试并重新同步时间
- **网络错误**：使用指数退避重试
- **服务器错误**：记录详细日志并重试

### 5.2 日志监控

修复后的系统会输出详细的日志信息：

```
[请求] /query_coupon
请求头: { S-Ca-Timestamp: "1703123456", ... }
时间戳: 1703123456 当前时间: 2023-12-21T02:17:36.000Z

[时间戳过期重试] /query_coupon 第1次重试
时间同步: 设置偏移量 -2000 ms

[请求成功] /query_coupon
```

## 6. 测试验证

### 6.1 运行测试

```bash
# 运行时间戳修复测试
node test/test-timestamp-fix.js
```

### 6.2 测试内容

- 时间同步功能测试
- 时间戳生成测试
- API请求签名测试
- 重试机制测试
- 时间戳过期场景模拟

## 7. 性能影响

### 7.1 优化措施

- **缓存时间偏移**：避免频繁计算
- **智能同步**：只在必要时同步时间
- **异步处理**：时间同步不阻塞主流程

### 7.2 资源消耗

- **内存**：增加约1KB（时间同步状态）
- **网络**：偶尔的时间同步请求（5分钟一次）
- **CPU**：时间戳计算开销可忽略

## 8. 兼容性

- **微信小程序**：完全兼容
- **Node.js环境**：支持测试和开发
- **旧版本API**：向后兼容，无需修改现有调用代码

## 9. 故障排除

### 9.1 时间戳仍然过期

1. 检查设备系统时间是否正确
2. 确认网络连接稳定
3. 查看时间同步日志
4. 手动触发时间同步

### 9.2 请求失败

1. 检查API密钥配置
2. 确认网络权限
3. 查看详细错误日志
4. 验证请求参数格式

## 10. 更新日志

### v1.1.0 (当前版本)
- ✅ 修复时间戳过期问题
- ✅ 新增时间同步工具
- ✅ 增强重试机制
- ✅ 优化错误处理
- ✅ 添加详细日志

### v1.0.0 (原始版本)
- 基础API服务功能
- 基础加密和签名功能