/* 城市选择器组件样式 */
.city-selector {
  position: relative;
}

/* 城市显示区域 */
.city-display {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  min-width: 120rpx;
}

.city-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.city-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

/* 弹窗遮罩 */
.city-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

/* 弹窗内容 */
.modal-content {
  width: 100%;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 0 0 env(safe-area-inset-bottom);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 区域标题 */
.section-title {
  font-size: 28rpx;
  color: #666666;
  padding: 32rpx 32rpx 24rpx;
  font-weight: 500;
}

/* 当前定位 */
.location-section {
  border-bottom: 1rpx solid #f0f0f0;
}

.location-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.location-text {
  font-size: 28rpx;
  color: #FF6600;
}

/* 热门城市 */
.hot-cities-section {
  border-bottom: 1rpx solid #f0f0f0;
}

.hot-cities-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 32rpx 32rpx;
  gap: 24rpx;
}

.hot-city-item {
  flex: 0 0 calc(25% - 18rpx);
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid transparent;
  transition: all 0.2s ease;
}

.hot-city-item:active {
  background: #fff2e6;
  border-color: #FF6600;
}

.hot-city-name {
  font-size: 26rpx;
  color: #333333;
}

/* 全部城市 */
.all-cities-section {
  flex: 1;
  min-height: 0;
}

.cities-scroll {
  height: 400rpx;
  padding: 0 32rpx;
}

.city-group {
  margin-bottom: 32rpx;
}

.group-letter {
  font-size: 24rpx;
  color: #999999;
  font-weight: 600;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.group-cities {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.city-item {
  flex: 0 0 calc(33.333% - 11rpx);
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  border: 1rpx solid transparent;
  transition: all 0.2s ease;
}

.city-item:active {
  background: #fff2e6;
  border-color: #FF6600;
}

.city-item-name {
  font-size: 26rpx;
  color: #333333;
}
