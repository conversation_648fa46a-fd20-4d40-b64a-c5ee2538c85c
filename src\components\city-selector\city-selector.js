/**
 * 城市选择器组件
 */
Component({
  properties: {
    currentCity: {
      type: String,
      value: '广州'
    },
    showArrow: {
      type: Boolean,
      value: true
    }
  },

  data: {
    showModal: false,
    hotCities: [
      '北京', '上海', '广州', '深圳', '杭州', '南京', 
      '苏州', '成都', '武汉', '重庆', '西安', '天津'
    ],
    allCities: [
      { letter: 'A', cities: ['安庆', '安阳', '鞍山'] },
      { letter: 'B', cities: ['北京', '保定', '包头', '本溪'] },
      { letter: 'C', cities: ['重庆', '成都', '长沙', '长春', '常州', '沧州'] },
      { letter: 'D', cities: ['大连', '东莞', '大庆', '丹东'] },
      { letter: 'F', cities: ['福州', '佛山', '抚顺'] },
      { letter: 'G', cities: ['广州', '贵阳', '桂林'] },
      { letter: 'H', cities: ['杭州', '哈尔滨', '合肥', '海口', '呼和浩特'] },
      { letter: 'J', cities: ['济南', '济宁', '吉林', '锦州'] },
      { letter: 'K', cities: ['昆明', '开封'] },
      { letter: 'L', cities: ['兰州', '廊坊', '洛阳', '连云港'] },
      { letter: 'M', cities: ['绵阳', '牡丹江'] },
      { letter: 'N', cities: ['南京', '南昌', '南宁', '宁波'] },
      { letter: 'Q', cities: ['青岛', '秦皇岛', '泉州'] },
      { letter: 'S', cities: ['上海', '深圳', '沈阳', '石家庄', '苏州', '汕头'] },
      { letter: 'T', cities: ['天津', '太原', '唐山'] },
      { letter: 'W', cities: ['武汉', '无锡', '威海', '潍坊'] },
      { letter: 'X', cities: ['西安', '厦门', '徐州', '襄阳'] },
      { letter: 'Y', cities: ['银川', '烟台', '扬州'] },
      { letter: 'Z', cities: ['郑州', '珠海', '中山', '淄博'] }
    ]
  },

  methods: {
    /**
     * 显示城市选择弹窗
     */
    showCityModal() {
      this.setData({ showModal: true })
    },

    /**
     * 隐藏城市选择弹窗
     */
    hideCityModal() {
      this.setData({ showModal: false })
    },

    /**
     * 选择城市
     */
    selectCity(e) {
      const { city } = e.currentTarget.dataset
      this.setData({ 
        showModal: false
      })
      
      // 触发城市改变事件
      this.triggerEvent('change', { 
        city: city,
        oldCity: this.data.currentCity 
      })
    },

    /**
     * 选择热门城市
     */
    selectHotCity(e) {
      const { city } = e.currentTarget.dataset
      this.selectCity({ currentTarget: { dataset: { city } } })
    },

    /**
     * 获取当前位置
     */
    getCurrentLocation() {
      wx.showLoading({ title: '定位中...' })
      
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 调用逆地理编码获取城市名称
          this.getCityByLocation(res.latitude, res.longitude)
        },
        fail: (error) => {
          wx.hideLoading()
          wx.showToast({
            title: '定位失败',
            icon: 'none'
          })
          console.error('获取位置失败:', error)
        }
      })
    },

    /**
     * 根据坐标获取城市名称
     */
    getCityByLocation(latitude, longitude) {
      // 这里应该调用地理编码API，暂时使用模拟数据
      wx.hideLoading()
      
      // 模拟根据坐标返回城市
      const mockCity = '当前城市'
      this.triggerEvent('change', { 
        city: mockCity,
        oldCity: this.data.currentCity,
        location: { latitude, longitude }
      })
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      // 阻止点击弹窗内容时关闭弹窗
    }
  }
})
