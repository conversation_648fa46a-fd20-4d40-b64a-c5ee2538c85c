/**
 * 本地存储工具类
 * 提供缓存管理、数据持久化等功能
 */

class StorageUtil {
  constructor() {
    this.prefix = 'meituan_'
    this.cachePrefix = 'meituan_cache_'
  }

  /**
   * 设置存储
   */
  set(key, value, expire = null) {
    const data = {
      value,
      timestamp: Date.now(),
      expire: expire ? Date.now() + expire : null
    }
    
    try {
      wx.setStorageSync(this.prefix + key, data)
      return true
    } catch (error) {
      console.error('存储失败:', error)
      return false
    }
  }

  /**
   * 获取存储
   */
  get(key) {
    try {
      const data = wx.getStorageSync(this.prefix + key)
      if (!data) return null
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.remove(key)
        return null
      }
      
      return data.value
    } catch (error) {
      console.error('读取存储失败:', error)
      return null
    }
  }

  /**
   * 删除存储
   */
  remove(key) {
    try {
      wx.removeStorageSync(this.prefix + key)
      return true
    } catch (error) {
      console.error('删除存储失败:', error)
      return false
    }
  }

  /**
   * 清空所有存储
   */
  clear() {
    try {
      const info = wx.getStorageInfoSync()
      info.keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          wx.removeStorageSync(key)
        }
      })
      return true
    } catch (error) {
      console.error('清空存储失败:', error)
      return false
    }
  }

  /**
   * 设置缓存（带过期时间）
   */
  setCache(key, value, expire = 30 * 60 * 1000) { // 默认30分钟
    const cacheData = {
      value,
      expireTime: Date.now() + expire
    }
    
    try {
      wx.setStorageSync(this.cachePrefix + key, cacheData)
      return true
    } catch (error) {
      console.error('设置缓存失败:', error)
      return false
    }
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    try {
      const cacheData = wx.getStorageSync(this.cachePrefix + key)
      if (!cacheData) return null
      
      // 检查是否过期
      if (Date.now() > cacheData.expireTime) {
        wx.removeStorageSync(this.cachePrefix + key)
        return null
      }
      
      return cacheData.value
    } catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpiredCache() {
    try {
      const info = wx.getStorageInfoSync()
      const now = Date.now()
      
      info.keys.forEach(key => {
        if (key.startsWith(this.cachePrefix)) {
          const cacheData = wx.getStorageSync(key)
          if (cacheData && cacheData.expireTime && now > cacheData.expireTime) {
            wx.removeStorageSync(key)
          }
        }
      })
    } catch (error) {
      console.error('清理缓存失败:', error)
    }
  }

  /**
   * 获取存储信息
   */
  getInfo() {
    try {
      return wx.getStorageInfoSync()
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return null
    }
  }
}

module.exports = new StorageUtil()