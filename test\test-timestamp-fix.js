/**
 * 时间戳修复测试
 * 测试API时间戳过期问题的修复效果
 */

const ApiService = require('../src/utils/api-service-fixed.js')
const timeSync = require('../src/utils/time-sync.js')

class TimestampFixTest {
  constructor() {
    this.apiService = ApiService
    this.testResults = []
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('=== 开始时间戳修复测试 ===')
    
    try {
      await this.testTimeSync()
      await this.testTimestampGeneration()
      await this.testApiRequest()
      await this.testRetryMechanism()
      
      this.printResults()
    } catch (error) {
      console.error('测试运行失败:', error)
    }
  }

  /**
   * 测试时间同步功能
   */
  async testTimeSync() {
    console.log('\n--- 测试时间同步功能 ---')
    
    try {
      // 测试获取当前时间戳
      const timestamp1 = timeSync.getCurrentTimestamp()
      const timestamp2 = Math.floor(Date.now() / 1000)
      
      console.log('时间同步工具时间戳:', timestamp1)
      console.log('系统时间戳:', timestamp2)
      console.log('时间差:', Math.abs(timestamp1 - timestamp2), '秒')
      
      // 测试时间偏移设置
      timeSync.setOffset(5000) // 设置5秒偏移
      const offsetTimestamp = timeSync.getCurrentTimestamp()
      console.log('设置偏移后时间戳:', offsetTimestamp)
      console.log('偏移效果:', offsetTimestamp - timestamp2, '秒')
      
      // 重置偏移
      timeSync.setOffset(0)
      
      this.addTestResult('时间同步功能', true, '时间同步工具工作正常')
    } catch (error) {
      console.error('时间同步测试失败:', error)
      this.addTestResult('时间同步功能', false, error.message)
    }
  }

  /**
   * 测试时间戳生成
   */
  async testTimestampGeneration() {
    console.log('\n--- 测试时间戳生成 ---')
    
    try {
      // 测试多次生成时间戳
      const timestamps = []
      for (let i = 0; i < 5; i++) {
        const timestamp = this.apiService.generateTimestamp()
        timestamps.push(timestamp)
        console.log(`时间戳 ${i + 1}:`, timestamp, new Date(parseInt(timestamp) * 1000).toISOString())
        
        // 短暂延迟
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // 检查时间戳是否递增
      let isIncreasing = true
      for (let i = 1; i < timestamps.length; i++) {
        if (parseInt(timestamps[i]) < parseInt(timestamps[i - 1])) {
          isIncreasing = false
          break
        }
      }
      
      console.log('时间戳是否递增:', isIncreasing)
      this.addTestResult('时间戳生成', isIncreasing, isIncreasing ? '时间戳生成正常' : '时间戳生成异常')
    } catch (error) {
      console.error('时间戳生成测试失败:', error)
      this.addTestResult('时间戳生成', false, error.message)
    }
  }

  /**
   * 测试API请求（模拟）
   */
  async testApiRequest() {
    console.log('\n--- 测试API请求签名 ---')
    
    try {
      // 测试签名生成
      const testData = { test: 'data' }
      const signature = this.apiService.generateSignature('POST', '/test', testData)
      
      console.log('生成的签名信息:')
      console.log('- 时间戳:', signature.timestamp)
      console.log('- Content-MD5:', signature.contentMD5)
      console.log('- 签名:', signature.signature.substring(0, 20) + '...')
      
      // 验证签名组件
      const hasTimestamp = signature.timestamp && signature.timestamp.length > 0
      const hasContentMD5 = signature.contentMD5 && signature.contentMD5.length > 0
      const hasSignature = signature.signature && signature.signature.length > 0
      
      const isValid = hasTimestamp && hasContentMD5 && hasSignature
      
      this.addTestResult('API请求签名', isValid, isValid ? '签名生成正常' : '签名生成异常')
    } catch (error) {
      console.error('API请求测试失败:', error)
      this.addTestResult('API请求签名', false, error.message)
    }
  }

  /**
   * 测试重试机制
   */
  async testRetryMechanism() {
    console.log('\n--- 测试重试机制 ---')
    
    try {
      // 测试时间戳过期错误识别
      const expiredError1 = { code: 400, message: '请求时间戳已过期' }
      const expiredError2 = { code: 400, message: 'timestamp expired' }
      const normalError = { code: 500, message: '服务器错误' }
      
      const isExpired1 = this.apiService.isTimestampExpiredError(expiredError1)
      const isExpired2 = this.apiService.isTimestampExpiredError(expiredError2)
      const isExpired3 = this.apiService.isTimestampExpiredError(normalError)
      
      console.log('时间戳过期错误识别:')
      console.log('- "请求时间戳已过期":', isExpired1)
      console.log('- "timestamp expired":', isExpired2)
      console.log('- "服务器错误":', isExpired3)
      
      const isCorrect = isExpired1 && isExpired2 && !isExpired3
      
      this.addTestResult('重试机制', isCorrect, isCorrect ? '错误识别正常' : '错误识别异常')
    } catch (error) {
      console.error('重试机制测试失败:', error)
      this.addTestResult('重试机制', false, error.message)
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    this.testResults.push({
      name: testName,
      success: success,
      message: message
    })
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n=== 测试结果汇总 ===')
    
    let passCount = 0
    let totalCount = this.testResults.length
    
    this.testResults.forEach(result => {
      const status = result.success ? '✅ 通过' : '❌ 失败'
      console.log(`${status} ${result.name}: ${result.message}`)
      if (result.success) passCount++
    })
    
    console.log(`\n总计: ${passCount}/${totalCount} 个测试通过`)
    
    if (passCount === totalCount) {
      console.log('🎉 所有测试通过！时间戳修复成功！')
    } else {
      console.log('⚠️  部分测试失败，需要进一步检查')
    }
  }

  /**
   * 模拟时间戳过期场景测试
   */
  async testTimestampExpiredScenario() {
    console.log('\n--- 模拟时间戳过期场景 ---')
    
    try {
      // 设置一个很大的负偏移，模拟时间戳过期
      timeSync.setOffset(-600000) // 设置-10分钟偏移
      
      const expiredTimestamp = timeSync.getCurrentTimestamp()
      console.log('过期时间戳:', expiredTimestamp, new Date(expiredTimestamp * 1000).toISOString())
      
      // 重置偏移
      timeSync.setOffset(0)
      
      const normalTimestamp = timeSync.getCurrentTimestamp()
      console.log('正常时间戳:', normalTimestamp, new Date(normalTimestamp * 1000).toISOString())
      
      console.log('时间差:', normalTimestamp - expiredTimestamp, '秒')
      
      this.addTestResult('时间戳过期模拟', true, '过期场景模拟成功')
    } catch (error) {
      console.error('时间戳过期场景测试失败:', error)
      this.addTestResult('时间戳过期模拟', false, error.message)
    }
  }
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  const test = new TimestampFixTest()
  test.runAllTests().then(() => {
    console.log('\n测试完成')
  }).catch(error => {
    console.error('测试执行失败:', error)
  })
}

module.exports = TimestampFixTest