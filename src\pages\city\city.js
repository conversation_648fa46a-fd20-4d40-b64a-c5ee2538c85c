const locationService = require('../../utils/locationService')

Page({
  data: {
    currentCity: '广州',
    searchKeyword: '',
    hotCities: [],
    allCities: {},
    searchResults: [],
    showSearch: false,
    letters: []
  },

  onLoad() {
    this.initPage()
  },

  // 初始化页面
  initPage() {
    const currentCity = locationService.getCurrentCity()
    const hotCities = locationService.getHotCities()
    const allCities = locationService.getAllCities()
    const letters = Object.keys(allCities).sort()

    this.setData({
      currentCity,
      hotCities,
      allCities,
      letters
    })
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })

    if (keyword.trim()) {
      const searchResults = locationService.searchCities(keyword)
      this.setData({
        searchResults,
        showSearch: true
      })
    } else {
      this.setData({
        searchResults: [],
        showSearch: false
      })
    }
  },

  // 清空搜索
  onSearchClear() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      showSearch: false
    })
  },

  // 选择城市
  onCitySelect(e) {
    const { name } = e.currentTarget.dataset
    locationService.setCurrentCity(name)
    
    wx.showToast({
      title: `已切换到${name}`,
      icon: 'success'
    })

    setTimeout(() => {
      wx.navigateBack()
    }, 1000)
  },

  // 获取当前位置
  onLocationTap() {
    wx.showLoading({ title: '定位中...' })
    
    locationService.getUserLocation()
      .then(location => {
        return locationService.getCityByCoordinates(location.latitude, location.longitude)
      })
      .then(city => {
        locationService.setCurrentCity(city)
        this.setData({ currentCity: city })
        wx.showToast({
          title: `定位到${city}`,
          icon: 'success'
        })
      })
      .catch(error => {
        wx.showToast({
          title: '定位失败',
          icon: 'none'
        })
      })
      .finally(() => {
        wx.hideLoading()
      })
  }
})