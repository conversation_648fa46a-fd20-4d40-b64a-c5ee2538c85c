const locationService = require('../../utils/locationService')

Page({
  data: {
    currentCity: '广州',
    searchKeyword: '',
    hotCities: [],
    allCities: {},
    searchResults: [],
    showSearch: false,
    letters: [],
    selectedCity: '',
    currentLetter: '',
    showLetterTip: false,
    scrollIntoView: '',
    searchFocus: false,
    locationLoading: false,
    locationStatus: 'success', // success, loading, error
    locationStatusText: '定位成功',
    showPermissionModal: false,
    showSelectAnimation: false,
    searchTimer: null,
    letterTipTimer: null
  },

  onLoad() {
    this.initPage()
    this.checkLocationPermission()
  },

  onUnload() {
    // 清理定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }
    if (this.data.letterTipTimer) {
      clearTimeout(this.data.letterTipTimer)
    }
  },

  // 初始化页面
  initPage() {
    const currentCity = locationService.getCurrentCity()
    const hotCities = locationService.getHotCities()
    const allCities = locationService.getAllCities()
    const letters = Object.keys(allCities).sort()

    this.setData({
      currentCity,
      hotCities,
      allCities,
      letters,
      selectedCity: currentCity
    })
  },

  // 检查位置权限
  checkLocationPermission() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation'] === undefined) {
          // 未授权，显示引导弹窗
          this.setData({ showPermissionModal: true })
        } else if (res.authSetting['scope.userLocation'] === false) {
          // 拒绝授权
          this.setData({ 
            locationStatus: 'error',
            locationStatusText: '定位权限已关闭'
          })
        } else {
          // 已授权
          this.setData({ 
            locationStatus: 'success',
            locationStatusText: '定位成功'
          })
        }
      }
    })
  },

  // 关闭权限弹窗
  onClosePermissionModal() {
    this.setData({ showPermissionModal: false })
  },

  // 请求位置权限
  onRequestLocation() {
    this.setData({ showPermissionModal: false })
    this.onLocationTap()
  },

  // 搜索输入（防抖处理）
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })

    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }

    // 设置新的定时器，实现防抖
    const timer = setTimeout(() => {
      if (keyword.trim()) {
        const searchResults = locationService.searchCities(keyword)
        this.setData({
          searchResults,
          showSearch: true
        })
      } else {
        this.setData({
          searchResults: [],
          showSearch: false
        })
      }
    }, 300) // 300ms 防抖延迟

    this.setData({ searchTimer: timer })
  },

  // 清空搜索
  onSearchClear() {
    this.setData({
      searchKeyword: '',
      searchResults: [],
      showSearch: false,
      searchFocus: false
    })
  },

  // 选择城市
  onCitySelect(e) {
    const { name } = e.currentTarget.dataset
    
    // 更新选中状态
    this.setData({ selectedCity: name })
    
    // 显示选择动画
    this.showSelectAnimation(name)
    
    // 保存城市选择
    locationService.setCurrentCity(name)
    
    // 延迟返回，让用户看到动画效果
    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  },

  // 显示选择动画
  showSelectAnimation(cityName) {
    this.setData({ 
      selectedCity: cityName,
      showSelectAnimation: true 
    })
    
    setTimeout(() => {
      this.setData({ showSelectAnimation: false })
    }, 1200)
  },

  // 获取当前位置
  onLocationTap() {
    if (this.data.locationLoading) return
    
    this.setData({ 
      locationLoading: true,
      locationStatus: 'loading',
      locationStatusText: '定位中...'
    })
    
    locationService.getUserLocation()
      .then(location => {
        return locationService.getCityByCoordinates(location.latitude, location.longitude)
      })
      .then(city => {
        locationService.setCurrentCity(city)
        this.setData({ 
          currentCity: city,
          selectedCity: city,
          locationStatus: 'success',
          locationStatusText: '定位成功'
        })
        
        wx.showToast({
          title: `定位到${city}`,
          icon: 'success',
          duration: 2000
        })
      })
      .catch(error => {
        console.error('定位失败:', error)
        
        this.setData({
          locationStatus: 'error',
          locationStatusText: '定位失败'
        })
        
        // 根据错误类型显示不同提示
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '位置权限未开启',
            content: '请在设置中开启位置权限，以获得更好的服务体验',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
          })
        } else {
          wx.showToast({
            title: '定位失败，请重试',
            icon: 'none',
            duration: 2000
          })
        }
      })
      .finally(() => {
        this.setData({ locationLoading: false })
      })
  },

  // 字母索引点击
  onLetterTap(e) {
    const { letter } = e.currentTarget.dataset
    this.scrollToLetter(letter)
  },

  // 字母索引触摸开始
  onLetterTouchStart(e) {
    const { letter } = e.currentTarget.dataset
    this.setData({ 
      currentLetter: letter,
      showLetterTip: true 
    })
    this.scrollToLetter(letter)
  },

  // 字母索引触摸结束
  onLetterTouchEnd() {
    // 延迟隐藏提示
    if (this.data.letterTipTimer) {
      clearTimeout(this.data.letterTipTimer)
    }
    
    const timer = setTimeout(() => {
      this.setData({ 
        showLetterTip: false,
        currentLetter: ''
      })
    }, 500)
    
    this.setData({ letterTipTimer: timer })
  },

  // 滚动到指定字母
  scrollToLetter(letter) {
    this.setData({ 
      scrollIntoView: `letter-${letter}`,
      currentLetter: letter
    })
  },

  // 页面显示时刷新数据
  onShow() {
    const currentCity = locationService.getCurrentCity()
    this.setData({ 
      currentCity,
      selectedCity: currentCity
    })
  }
})