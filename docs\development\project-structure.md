# 项目结构说明

## 📁 整体目录结构

```
meituan-miniprogram/
├── .kiro/                          # Kiro AI助手配置
│   ├── specs/                      # 项目规格说明
│   │   └── meituan-miniprogram/    # 当前项目规格
│   │       ├── requirements.md     # 需求文档
│   │       ├── design.md          # 设计文档
│   │       └── tasks.md           # 任务清单
│   └── steering/                   # AI助手指导文档
│       ├── product.md             # 产品概述
│       ├── structure.md           # 项目结构
│       └── tech.md                # 技术栈说明
├── docs/                          # 项目文档
│   ├── README.md                  # 项目概览
│   ├── api/                       # API文档
│   │   ├── meituan-api.md         # 美团联盟API
│   │   └── wechat-api.md          # 微信小程序API
│   ├── design/                    # 设计文档
│   │   ├── ui-specifications.md   # UI设计规范
│   │   ├── component-library.md   # 组件库文档
│   │   └── style-guide.md         # 样式指南
│   ├── development/               # 开发文档
│   │   ├── project-structure.md   # 项目结构（本文档）
│   │   ├── coding-standards.md    # 编码规范
│   │   └── deployment.md          # 部署指南
│   └── user-guide/               # 用户指南
│       ├── user-manual.md         # 用户使用手册
│       └── admin-guide.md         # 管理员指南
├── src/                          # 源代码目录
│   ├── app.js                    # 小程序入口文件
│   ├── app.json                  # 小程序配置文件
│   ├── app.wxss                  # 全局样式文件
│   ├── sitemap.json              # 搜索优化配置
│   ├── pages/                    # 页面目录
│   │   ├── index/                # 首页
│   │   │   ├── index.wxml        # 页面结构
│   │   │   ├── index.wxss        # 页面样式
│   │   │   ├── index.js          # 页面逻辑
│   │   │   └── index.json        # 页面配置
│   │   ├── activity/             # 活动页
│   │   ├── share/                # 分享页
│   │   ├── profile/              # 个人中心
│   │   ├── search/               # 搜索页
│   │   ├── city/                 # 城市选择页
│   │   └── product/              # 商品详情页
│   ├── components/               # 组件目录
│   │   ├── waterfall-list/       # 瀑布流组件
│   │   ├── product-card/         # 商品卡片组件
│   │   ├── search-bar/           # 搜索栏组件
│   │   ├── city-selector/        # 城市选择器
│   │   ├── button/               # 按钮组件
│   │   ├── loading/              # 加载组件
│   │   └── ...                   # 其他组件
│   ├── utils/                    # 工具类目录
│   │   ├── api.js                # API服务
│   │   ├── cache.js              # 缓存服务
│   │   ├── location.js           # 位置服务
│   │   ├── waterfall.js          # 瀑布流工具
│   │   ├── http.js               # 网络请求封装
│   │   ├── storage.js            # 存储工具
│   │   ├── format.js             # 格式化工具
│   │   └── constants.js          # 常量定义
│   ├── services/                 # 业务服务目录
│   │   ├── product.js            # 商品服务
│   │   ├── activity.js           # 活动服务
│   │   ├── user.js               # 用户服务
│   │   └── share.js              # 分享服务
│   ├── assets/                   # 静态资源目录
│   │   ├── images/               # 图片资源
│   │   │   ├── icons/            # 图标
│   │   │   ├── logos/            # Logo
│   │   │   └── placeholders/     # 占位图
│   │   ├── styles/               # 样式资源
│   │   │   ├── variables.wxss    # CSS变量
│   │   │   ├── mixins.wxss       # 样式混入
│   │   │   └── reset.wxss        # 样式重置
│   │   └── fonts/                # 字体资源
│   └── config/                   # 配置文件目录
│       ├── env.js                # 环境配置
│       ├── api.js                # API配置
│       └── constants.js          # 常量配置
├── tests/                        # 测试目录
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   └── e2e/                      # 端到端测试
├── tools/                        # 工具脚本
│   ├── build.js                  # 构建脚本
│   ├── deploy.js                 # 部署脚本
│   └── mock.js                   # 数据模拟
├── re.md                         # API凭证信息
├── package.json                  # 项目依赖配置
├── project.config.json           # 微信开发者工具配置
└── README.md                     # 项目说明
```

## 📄 核心文件说明

### 小程序入口文件

#### app.js
```javascript
// 小程序入口文件
App({
  // 全局数据
  globalData: {
    userInfo: null,
    currentCity: '广州',
    systemInfo: null,
    apiConfig: {
      baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
      appkey: '9498b0824d214ee4b65bfab1be6dbed0',
      mediaId: '1000304354',
      allianceId: '1904722314357399633',
      sid: 'yjd2025'
    }
  },

  // 小程序启动
  onLaunch(options) {
    console.log('小程序启动', options)
    this.initApp()
  },

  // 小程序显示
  onShow(options) {
    console.log('小程序显示', options)
  },

  // 小程序隐藏
  onHide() {
    console.log('小程序隐藏')
  },

  // 初始化应用
  initApp() {
    // 获取系统信息
    this.getSystemInfo()
    // 初始化缓存
    this.initCache()
    // 获取用户位置
    this.getUserLocation()
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 初始化缓存
  initCache() {
    const CacheService = require('./utils/cache.js')
    CacheService.clearExpired()
  },

  // 获取用户位置
  getUserLocation() {
    const LocationService = require('./utils/location.js')
    LocationService.getCurrentLocation()
      .then(location => {
        console.log('用户位置:', location)
        return LocationService.getCityByCoordinates(location.latitude, location.longitude)
      })
      .then(city => {
        this.globalData.currentCity = city
      })
      .catch(error => {
        console.error('获取位置失败:', error)
      })
  }
})
```

#### app.json
```json
{
  "pages": [
    "pages/index/index",
    "pages/activity/activity",
    "pages/share/share",
    "pages/profile/profile",
    "pages/search/search",
    "pages/city/city",
    "pages/product/product"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#FF6600",
    "navigationBarTitleText": "美团优惠",
    "navigationBarTextStyle": "white",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#666666",
    "selectedColor": "#FF6600",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "assets/images/icons/home.png",
        "selectedIconPath": "assets/images/icons/home-active.png"
      },
      {
        "pagePath": "pages/activity/activity",
        "text": "活动",
        "iconPath": "assets/images/icons/activity.png",
        "selectedIconPath": "assets/images/icons/activity-active.png"
      },
      {
        "pagePath": "pages/share/share",
        "text": "分享",
        "iconPath": "assets/images/icons/share.png",
        "selectedIconPath": "assets/images/icons/share-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "assets/images/icons/profile.png",
        "selectedIconPath": "assets/images/icons/profile-active.png"
      }
    ]
  },
  "usingComponents": {
    "custom-button": "/components/button/button",
    "custom-loading": "/components/loading/loading",
    "lazy-image": "/components/lazy-image/lazy-image"
  },
  "permission": {
    "scope.userLocation": {
      "desc": "你的位置信息将用于小程序位置接口的效果展示"
    }
  },
  "requiredBackgroundModes": ["location"],
  "sitemapLocation": "sitemap.json"
}
```

#### app.wxss
```css
/* 全局样式 */
@import "assets/styles/variables.wxss";
@import "assets/styles/reset.wxss";

/* 全局字体 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
}

/* 全局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.text-center {
  text-align: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 全局动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
```

## 📱 页面结构

### 页面目录规范

每个页面目录包含四个核心文件：

```
pages/[page-name]/
├── [page-name].wxml    # 页面结构文件
├── [page-name].wxss    # 页面样式文件
├── [page-name].js      # 页面逻辑文件
└── [page-name].json    # 页面配置文件
```

### 页面文件模板

#### 页面逻辑文件模板 (.js)
```javascript
// pages/example/example.js
const app = getApp()
const ApiService = require('../../utils/api.js')
const CacheService = require('../../utils/cache.js')

Page({
  // 页面数据
  data: {
    loading: false,
    hasMore: true,
    page: 1,
    limit: 20,
    items: []
  },

  // 页面加载
  onLoad(options) {
    console.log('页面加载', options)
    this.initPage(options)
  },

  // 页面显示
  onShow() {
    console.log('页面显示')
    this.refreshData()
  },

  // 页面隐藏
  onHide() {
    console.log('页面隐藏')
  },

  // 页面卸载
  onUnload() {
    console.log('页面卸载')
    this.clearTimer()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
    wx.stopPullDownRefresh()
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore()
  },

  // 页面滚动
  onPageScroll(e) {
    // 处理滚动事件
  },

  // 分享配置
  onShareAppMessage() {
    return {
      title: '页面标题',
      path: '/pages/example/example'
    }
  },

  // 初始化页面
  initPage(options) {
    this.loadData()
  },

  // 刷新数据
  refreshData() {
    this.setData({
      page: 1,
      items: [],
      hasMore: true
    })
    this.loadData()
  },

  // 加载数据
  async loadData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const response = await ApiService.getData({
        page: this.data.page,
        limit: this.data.limit
      })

      const newItems = this.data.page === 1 ? response.items : [...this.data.items, ...response.items]

      this.setData({
        items: newItems,
        hasMore: response.hasMore,
        loading: false
      })
    } catch (error) {
      console.error('加载数据失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loading) return

    this.setData({
      page: this.data.page + 1
    })
    this.loadData()
  },

  // 清理定时器
  clearTimer() {
    // 清理页面定时器
  }
})
```

#### 页面配置文件模板 (.json)
```json
{
  "navigationBarTitleText": "页面标题",
  "enablePullDownRefresh": true,
  "onReachBottomDistance": 50,
  "usingComponents": {
    "waterfall-list": "/components/waterfall-list/waterfall-list",
    "product-card": "/components/product-card/product-card"
  }
}
```

## 🧩 组件结构

### 组件目录规范

```
components/[component-name]/
├── [component-name].wxml    # 组件结构文件
├── [component-name].wxss    # 组件样式文件
├── [component-name].js      # 组件逻辑文件
└── [component-name].json    # 组件配置文件
```

### 组件文件模板

#### 组件逻辑文件模板 (.js)
```javascript
// components/example/example.js
Component({
  // 组件属性
  properties: {
    items: {
      type: Array,
      value: []
    },
    loading: {
      type: Boolean,
      value: false
    }
  },

  // 组件数据
  data: {
    internalData: ''
  },

  // 组件生命周期
  lifetimes: {
    created() {
      console.log('组件创建')
    },
    attached() {
      console.log('组件挂载')
      this.initComponent()
    },
    detached() {
      console.log('组件卸载')
      this.cleanup()
    }
  },

  // 页面生命周期
  pageLifetimes: {
    show() {
      console.log('页面显示')
    },
    hide() {
      console.log('页面隐藏')
    }
  },

  // 组件方法
  methods: {
    // 初始化组件
    initComponent() {
      // 初始化逻辑
    },

    // 事件处理
    onItemTap(e) {
      const { item, index } = e.currentTarget.dataset
      this.triggerEvent('itemtap', { item, index })
    },

    // 清理资源
    cleanup() {
      // 清理逻辑
    }
  }
})
```

#### 组件配置文件模板 (.json)
```json
{
  "component": true,
  "usingComponents": {
    "sub-component": "/components/sub-component/sub-component"
  }
}
```

## 🛠 工具类结构

### utils目录说明

```
utils/
├── api.js              # API服务封装
├── cache.js            # 缓存服务
├── location.js         # 位置服务
├── waterfall.js        # 瀑布流工具
├── http.js             # HTTP请求封装
├── storage.js          # 存储工具
├── format.js           # 格式化工具
├── constants.js        # 常量定义
├── validator.js        # 数据验证
├── date.js             # 日期工具
└── util.js             # 通用工具
```

### 工具类模板

```javascript
// utils/example.js
class ExampleService {
  // 静态方法
  static method1() {
    // 实现逻辑
  }

  // 实例方法
  method2() {
    // 实现逻辑
  }
}

// 导出
module.exports = ExampleService
```

## 📦 资源文件结构

### assets目录说明

```
assets/
├── images/             # 图片资源
│   ├── icons/          # 图标文件
│   │   ├── home.png
│   │   ├── home-active.png
│   │   └── ...
│   ├── logos/          # Logo文件
│   │   ├── meituan-logo.png
│   │   └── ...
│   └── placeholders/   # 占位图
│       ├── product-placeholder.png
│       └── ...
├── styles/             # 样式文件
│   ├── variables.wxss  # CSS变量
│   ├── mixins.wxss     # 样式混入
│   └── reset.wxss      # 样式重置
└── fonts/              # 字体文件
    └── ...
```

### 样式文件说明

#### variables.wxss
```css
/* CSS变量定义 */
:root {
  /* 颜色变量 */
  --primary-yellow: #FFD100;
  --primary-orange: #FF6600;
  --accent-green: #00AA90;
  
  /* 字体变量 */
  --font-size-large: 16px;
  --font-size-medium: 14px;
  --font-size-small: 12px;
  
  /* 间距变量 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
}
```

## 🔧 配置文件

### 环境配置 (config/env.js)
```javascript
// 环境配置
const env = {
  development: {
    apiBaseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
    debug: true
  },
  production: {
    apiBaseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
    debug: false
  }
}

// 获取当前环境
const getCurrentEnv = () => {
  // 根据实际情况判断环境
  return 'development'
}

module.exports = env[getCurrentEnv()]
```

### API配置 (config/api.js)
```javascript
// API配置
const apiConfig = {
  baseUrl: 'https://media.meituan.com/cps_open/common/api/v1',
  timeout: 10000,
  credentials: {
    appkey: '9498b0824d214ee4b65bfab1be6dbed0',
    secret: '2a1463cb8f364cafbfd8d2a19c48eb48',
    mediaId: '1000304354',
    allianceId: '1904722314357399633',
    sid: 'yjd2025'
  },
  endpoints: {
    getReferralLink: '/get_referral_link',
    queryCoupon: '/query_coupon'
  }
}

module.exports = apiConfig
```

## 📋 开发规范

### 文件命名规范

1. **页面文件**: 使用kebab-case，如`product-detail`
2. **组件文件**: 使用kebab-case，如`product-card`
3. **工具文件**: 使用camelCase，如`apiService.js`
4. **常量文件**: 使用UPPER_CASE，如`API_CONSTANTS.js`

### 代码组织规范

1. **导入顺序**: 
   - 小程序API
   - 第三方库
   - 工具类
   - 组件
   - 样式

2. **函数组织**:
   - 生命周期函数在前
   - 事件处理函数在中
   - 工具函数在后

3. **注释规范**:
   - 文件头部注释
   - 函数注释
   - 复杂逻辑注释

### 性能优化建议

1. **图片优化**: 使用适当的图片格式和尺寸
2. **代码分割**: 按需加载组件和页面
3. **缓存策略**: 合理使用本地缓存
4. **网络优化**: 减少不必要的网络请求
5. **内存管理**: 及时清理不用的资源

---

*项目结构会根据开发进度持续更新和优化*