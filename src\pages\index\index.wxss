/* 首页样式 */
.index-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 顶部区域 */
.header {
  background: var(--white);
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.city-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: var(--bg-color);
  border-radius: 20rpx;
  min-width: 120rpx;
}

.city-name {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: 500;
}

.city-arrow {
  width: 24rpx;
  height: 24rpx;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: var(--bg-color);
  border-radius: 32rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
}

.search-placeholder {
  font-size: 28rpx;
  color: var(--text-lighter);
}

/* 横幅区域 */
.banner-section {
  margin: 24rpx;
}

.banner-swiper {
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-item {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
}

.banner-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.banner-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--white);
}

.banner-subtitle {
  font-size: 24rpx;
  color: var(--white);
  opacity: 0.9;
}

.banner-image {
  width: 120rpx;
  height: 120rpx;
}

/* 快速入口 */
.quick-entries {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 24rpx;
  background: var(--white);
  margin: 0 24rpx 24rpx;
  border-radius: 16rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.quick-icon {
  width: 64rpx;
  height: 64rpx;
}

.quick-name {
  font-size: 24rpx;
  color: var(--text-color);
}

/* 分类标签 */
.category-tabs {
  background: var(--white);
  margin: 0 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 0 24rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 48rpx;
}

.category-item {
  position: relative;
  padding: 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.category-name {
  font-size: 28rpx;
  color: var(--text-light);
  white-space: nowrap;
}

.category-item.active .category-name {
  color: var(--primary-color);
  font-weight: 500;
}

.category-line {
  width: 40rpx;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 2rpx;
}

/* 商品区域 */
.product-section {
  padding-bottom: 40rpx;
}

.waterfall {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 24rpx;
  gap: 24rpx;
}

.waterfall-item {
  width: calc(50% - 12rpx);
}

.product-card {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.product-image {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
}

.product-info {
  padding: 24rpx;
}

.product-title {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 16rpx;
  height: 80rpx;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.price {
  font-size: 32rpx;
  color: #FF3333;
  font-weight: bold;
}

.price-original {
  font-size: 24rpx;
  color: var(--text-lighter);
  text-decoration: line-through;
}

.product-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: var(--white);
}

.tag-primary {
  background: var(--primary-color);
}

.product-shop {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-lighter);
  font-size: 28rpx;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: var(--text-lighter);
  font-size: 28rpx;
}