/* 首页样式 */
.index-page {
  background: var(--bg-color);
  min-height: 100vh;
}

/* 顶部区域 */
.header {
  background: linear-gradient(135deg, #FF6600 0%, #FF8C42 100%);
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 102, 0, 0.2);
}

.city-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  min-width: 120rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.city-selector:active {
  background: rgba(255, 255, 255, 1);
  transform: scale(0.98);
}

.city-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.city-arrow {
  width: 24rpx;
  height: 24rpx;
  transition: transform 0.3s ease;
}

.search-bar {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-bar:active {
  background: rgba(255, 255, 255, 1);
  transform: scale(0.99);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  color: #999999;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999999;
  flex: 1;
}

/* 横幅区域 */
.banner-section {
  margin: 24rpx 32rpx;
}

.banner-swiper {
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(255, 102, 0, 0.3);
}

.banner-item {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8C42 50%, #FFD100 100%);
  animation: bannerGlow 3s ease-in-out infinite alternate;
}

@keyframes bannerGlow {
  0% {
    box-shadow: inset 0 0 20rpx rgba(255, 209, 0, 0.3);
  }
  100% {
    box-shadow: inset 0 0 40rpx rgba(255, 209, 0, 0.6);
  }
}

.banner-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  z-index: 2;
}

.banner-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--white);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  animation: titlePulse 2s ease-in-out infinite alternate;
}

@keyframes titlePulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.02);
  }
}

.banner-subtitle {
  font-size: 24rpx;
  color: var(--white);
  opacity: 0.95;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.banner-image {
  width: 120rpx;
  height: 120rpx;
  z-index: 2;
  animation: imageFloat 3s ease-in-out infinite alternate;
}

@keyframes imageFloat {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-8rpx);
  }
}

/* 快速入口 */
.quick-entries {
  display: flex;
  justify-content: space-around;
  padding: 32rpx 24rpx;
  background: var(--white);
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}

.quick-item:active {
  background: rgba(255, 209, 0, 0.1);
  transform: scale(0.95);
}

.quick-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12rpx;
  background: linear-gradient(135deg, rgba(255, 209, 0, 0.1), rgba(255, 102, 0, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-item:active::before {
  opacity: 1;
}

.quick-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.quick-item:active .quick-icon {
  transform: scale(1.1);
}

.quick-name {
  font-size: 24rpx;
  color: var(--text-color);
  font-weight: 500;
  z-index: 1;
}

/* 分类标签 */
.category-tabs {
  background: var(--white);
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  padding: 0 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 48rpx;
}

.category-item {
  position: relative;
  padding: 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.category-name {
  font-size: 28rpx;
  color: var(--text-light);
  white-space: nowrap;
  transition: all 0.3s ease;
}

.category-item.active .category-name {
  color: var(--primary-color);
  font-weight: 600;
  transform: scale(1.05);
}

.category-line {
  width: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #FF6600, #FFD100);
  border-radius: 2rpx;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: lineGlow 2s ease-in-out infinite alternate;
}

.category-item.active .category-line {
  width: 40rpx;
}

@keyframes lineGlow {
  0% {
    box-shadow: 0 0 8rpx rgba(255, 102, 0, 0.5);
  }
  100% {
    box-shadow: 0 0 16rpx rgba(255, 209, 0, 0.8);
  }
}

/* 商品区域 */
.product-section {
  padding: 0 32rpx 40rpx;
}

.waterfall {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 16rpx;
}

.waterfall-item {
  width: calc(50% - 8rpx);
}

.product-card {
  background: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  position: relative;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 209, 0, 0.05), rgba(255, 102, 0, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.16);
}

.product-card:active::before {
  opacity: 1;
}

.product-image {
  width: 100%;
  height: 240rpx;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:active .product-image {
  transform: scale(1.02);
}

.product-info {
  padding: 24rpx;
  position: relative;
  z-index: 1;
}

.product-title {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 16rpx;
  height: 80rpx;
  font-weight: 500;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.price {
  font-size: 32rpx;
  color: #FF3333;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(255, 51, 51, 0.2);
}

.price-original {
  font-size: 24rpx;
  color: var(--text-lighter);
  text-decoration: line-through;
}

.product-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.tag {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: var(--white);
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.tag-primary {
  background: linear-gradient(135deg, #FF6600, #FF8C42);
}

.product-shop {
  font-size: 24rpx;
  color: var(--text-light);
  font-weight: 500;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: var(--text-lighter);
  font-size: 28rpx;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 128rpx;
  height: 128rpx;
  margin: 0 auto 24rpx;
  opacity: 0.3;
}

.empty-text {
  color: var(--text-lighter);
  font-size: 28rpx;
}