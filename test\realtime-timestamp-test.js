/**
 * 实时时间戳测试
 * 模拟真实的美团API时间戳验证场景
 */

// 模拟微信小程序环境
global.wx = {
  request: function(options) {
    console.log('\n=== 实时API请求测试 ===')
    
    // 获取请求时间戳
    const requestTimestamp = parseInt(options.header['S-Ca-Timestamp'])
    const currentTimestamp = Math.floor(Date.now() / 1000)
    const timeDiff = currentTimestamp - requestTimestamp
    
    console.log('时间戳验证详情:')
    console.log('- 请求时间戳:', requestTimestamp)
    console.log('- 请求时间:', new Date(requestTimestamp * 1000).toISOString())
    console.log('- 服务器时间戳:', currentTimestamp)
    console.log('- 服务器时间:', new Date(currentTimestamp * 1000).toISOString())
    console.log('- 时间差:', timeDiff, '秒')
    console.log('- 请求是否过期:', timeDiff > 300 ? '是' : '否')
    
    // 美团API的时间戳验证逻辑：允许5分钟误差
    const isExpired = Math.abs(timeDiff) > 300
    
    setTimeout(() => {
      if (isExpired) {
        console.log('❌ 时间戳过期，返回错误')
        options.success({
          statusCode: 200,
          data: { 
            code: 400, 
            message: '请求时间戳已过期',
            serverTimestamp: currentTimestamp
          },
          header: { 
            'Date': new Date().toUTCString(),
            'Server-Time': currentTimestamp.toString()
          }
        })
      } else {
        console.log('✅ 时间戳验证通过，请求成功')
        options.success({
          statusCode: 200,
          data: { 
            code: 0, 
            message: 'success',
            data: { products: ['商品1', '商品2', '商品3'] }
          },
          header: { 
            'Date': new Date().toUTCString(),
            'Server-Time': currentTimestamp.toString()
          }
        })
      }
    }, 50)
  }
}

// 模拟getApp函数
global.getApp = function() {
  return {
    globalData: {
      location: { latitude: 39.928, longitude: 116.404 }
    }
  }
}

console.log('=== 实时时间戳修复测试 ===\n')

async function runRealtimeTest() {
  try {
    console.log('1. 加载修复后的API服务')
    const ApiService = require('../src/utils/api-service-fixed.js')
    const meituanTimeFix = require('../src/utils/meituan-time-fix.js')
    
    console.log('初始状态:', meituanTimeFix.getStatus())
    
    console.log('\n2. 测试正常请求')
    try {
      const result1 = await ApiService.queryCoupon(39.928, 116.404, {
        pageNo: 1,
        pageSize: 10,
        listTopiId: 'hot_sale'
      })
      console.log('✅ 第一次请求成功:', result1.message)
    } catch (error) {
      console.log('❌ 第一次请求失败:', error.message)
    }
    
    console.log('\n当前状态:', meituanTimeFix.getStatus())
    
    console.log('\n3. 模拟时间戳严格验证场景')
    // 修改模拟器，使其更严格地验证时间戳
    global.wx.request = function(options) {
      const requestTimestamp = parseInt(options.header['S-Ca-Timestamp'])
      const currentTimestamp = Math.floor(Date.now() / 1000)
      const timeDiff = currentTimestamp - requestTimestamp
      
      console.log(`\n严格验证 - 时间差: ${timeDiff}秒`)
      
      // 更严格的验证：只允许1分钟误差
      const isExpired = Math.abs(timeDiff) > 60
      
      setTimeout(() => {
        if (isExpired) {
          console.log('❌ 严格验证失败')
          options.success({
            statusCode: 200,
            data: { code: 400, message: '请求时间戳已过期' },
            header: { 'Date': new Date().toUTCString() }
          })
        } else {
          console.log('✅ 严格验证通过')
          options.success({
            statusCode: 200,
            data: { code: 0, message: 'success', data: { result: 'ok' } },
            header: { 'Date': new Date().toUTCString() }
          })
        }
      }, 50)
    }
    
    try {
      const result2 = await ApiService.queryCoupon(39.928, 116.404, {
        pageNo: 1,
        pageSize: 10,
        searchText: '美食'
      })
      console.log('✅ 严格验证请求成功:', result2.message)
    } catch (error) {
      console.log('❌ 严格验证请求失败:', error.message)
    }
    
    console.log('\n最终状态:', meituanTimeFix.getStatus())
    
    console.log('\n🎉 实时测试完成！')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    console.error('错误堆栈:', error.stack)
  }
}

runRealtimeTest()