<!--分享页面-->
<view class="share-page">
  <!-- 艺术标题 -->
  <view class="title-section">
    <text class="art-title">分享活动</text>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索活动" 
        placeholder-class="search-placeholder"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <view class="search-icon" bindtap="onSearchConfirm">
        <image src="/assets/icons/search.png" mode="aspectFit" />
      </view>
    </view>
  </view>

  <!-- 分类标签栏 -->
  <view class="category-section">
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-list">
        <view 
          class="category-item {{item.active ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="id"
          data-index="{{index}}"
          bindtap="onCategoryTap"
        >
          <text class="category-name">{{item.name}}</text>
          <view class="category-underline" wx:if="{{item.active}}"></view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 热门活动分享标题 -->
  <view class="content-title">
    <text>热门活动分享</text>
  </view>

  <!-- 活动瀑布流 -->
  <view class="activities-section">
    <view class="waterfall-container">
      <!-- 左列 -->
      <view class="waterfall-column">
        <view 
          class="activity-card" 
          wx:for="{{leftColumnActivities}}" 
          wx:key="id"
          data-activity="{{item}}"
          bindtap="onActivityTap"
        >
          <image class="activity-image" src="{{item.image}}" mode="aspectFill" />
          <view class="activity-info">
            <view class="activity-title">{{item.title}}</view>
            <view class="activity-discount">{{item.discount}}</view>
            <view class="activity-meta">
              <text class="activity-expire">{{item.expireDate}}</text>
              <text class="activity-popularity">{{item.shareCount}}人分享</text>
            </view>
            <view class="share-button" data-activity="{{item}}" bindtap="onShareButtonTap">
              分享
            </view>
          </view>
        </view>
      </view>

      <!-- 右列 -->
      <view class="waterfall-column">
        <view 
          class="activity-card" 
          wx:for="{{rightColumnActivities}}" 
          wx:key="id"
          data-activity="{{item}}"
          bindtap="onActivityTap"
        >
          <image class="activity-image" src="{{item.image}}" mode="aspectFill" />
          <view class="activity-info">
            <view class="activity-title">{{item.title}}</view>
            <view class="activity-discount">{{item.discount}}</view>
            <view class="activity-meta">
              <text class="activity-expire">{{item.expireDate}}</text>
              <text class="activity-popularity">{{item.shareCount}}人分享</text>
            </view>
            <view class="share-button" data-activity="{{item}}" bindtap="onShareButtonTap">
              分享
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && activities.length > 0}}">
      <text>没有更多活动了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{activities.length === 0 && !loading}}">
      <image class="empty-icon" src="/assets/icons/empty.png" mode="aspectFit" />
      <text class="empty-text">暂无活动内容</text>
    </view>
  </view>
</view>