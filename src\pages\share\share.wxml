<!--分享页面-->
<view class="share-page">
  <!-- 分类导航 -->
  <view class="category-nav">
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-list">
        <view 
          class="category-item" 
          wx:for="{{categories}}" 
          wx:key="id"
          data-category="{{item}}"
          bindtap="onCategoryTap"
        >
          <view class="category-icon">
            <image src="/assets/icons/{{item.icon}}.png" mode="aspectFit" />
          </view>
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐内容四宫格 -->
  <view class="recommend-section">
    <view class="section-title">推荐内容</view>
    <view class="recommend-grid">
      <view 
        class="recommend-item" 
        wx:for="{{recommendList}}" 
        wx:key="id"
        data-item="{{item}}"
        bindtap="onRecommendTap"
      >
        <image class="recommend-image" src="{{item.image}}" mode="aspectFill" />
        <view class="recommend-info">
          <view class="recommend-title">{{item.title}}</view>
          <view class="recommend-desc">{{item.desc}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分享动态瀑布流 -->
  <view class="share-section">
    <view class="section-title">用户分享</view>
    <view class="share-list">
      <view 
        class="share-item" 
        wx:for="{{shareList}}" 
        wx:key="id"
        wx:for-index="index"
      >
        <!-- 用户信息 -->
        <view class="user-info">
          <image class="user-avatar" src="{{item.user.avatar}}" mode="aspectFill" />
          <view class="user-details">
            <view class="user-nickname">{{item.user.nickname}}</view>
            <view class="share-time">{{item.time}}</view>
          </view>
        </view>

        <!-- 分享内容 -->
        <view class="share-content">
          <text class="content-text">{{item.content}}</text>
          
          <!-- 图片展示 -->
          <view class="content-images" wx:if="{{item.images.length > 0}}">
            <image 
              class="content-image" 
              wx:for="{{item.images}}" 
              wx:key="*this"
              src="{{item}}" 
              mode="aspectFill"
            />
          </view>
        </view>

        <!-- 互动按钮 -->
        <view class="interaction-bar">
          <view 
            class="interaction-item {{item.isLiked ? 'liked' : ''}}" 
            data-index="{{index}}"
            bindtap="onLikeTap"
          >
            <image class="interaction-icon" src="/assets/icons/like.png" mode="aspectFit" />
            <text class="interaction-text">{{item.likes}}</text>
          </view>
          
          <view 
            class="interaction-item" 
            data-item="{{item}}"
            bindtap="onCommentTap"
          >
            <image class="interaction-icon" src="/assets/icons/comment.png" mode="aspectFit" />
            <text class="interaction-text">{{item.comments}}</text>
          </view>
          
          <view 
            class="interaction-item" 
            data-item="{{item}}"
            bindtap="onShareTap"
          >
            <image class="interaction-icon" src="/assets/icons/share-small.png" mode="aspectFit" />
            <text class="interaction-text">{{item.shares}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && shareList.length > 0}}">
      <text>没有更多内容了</text>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{shareList.length === 0 && !loading}}">
      <image class="empty-icon" src="/assets/icons/empty.png" mode="aspectFit" />
      <text class="empty-text">暂无分享内容</text>
    </view>
  </view>
</view>