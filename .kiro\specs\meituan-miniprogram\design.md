# Design Document

## Overview

This design document details the technical architecture, UI design, and implementation approach for the Meituan Alliance WeChat Mini Program. The application uses WeChat Mini Program native framework, integrates Meituan Alliance CPS API, and provides promotional information display, location services, and social sharing functionality. The interface is implemented with complete 1:1 restoration according to UI design images, ensuring consistency with Meituan brand visual style.

## Architecture

### 整体架构
```
┌─────────────────────────────────────────┐
│                前端层                    │
│  ┌─────────┬─────────┬─────────┬─────────┐│
│  │  首页   │  活动   │  分享   │  我的   ││
│  └─────────┴─────────┴─────────┴─────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                服务层                    │
│  ┌─────────┬─────────┬─────────┬─────────┐│
│  │API服务  │位置服务 │缓存服务 │工具服务 ││
│  └─────────┴─────────┴─────────┴─────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                数据层                    │
│  ┌─────────┬─────────┬─────────┬─────────┐│
│  │本地存储 │微信API  │美团API  │用户数据 ││
│  └─────────┴─────────┴─────────┴─────────┘│
└─────────────────────────────────────────┘
```

### 技术栈
- **前端框架**: 微信小程序原生框架
- **样式系统**: WXSS + Flex布局
- **状态管理**: 页面级状态管理 + 全局数据共享
- **网络请求**: wx.request + 美团API签名
- **本地存储**: wx.storage + 数据缓存
- **位置服务**: wx.getLocation + 地理编码
- **分享功能**: wx.shareAppMessage

## 详细UI设计规范

### 全局设计规范

#### 颜色系统
```css
/* 美团品牌色 */
--meituan-yellow: #FFD100;     /* 美团主黄色 */
--meituan-orange: #FF6600;     /* 美团橙色 */
--meituan-orange-light: #FF8C42; /* 浅橙色 */

/* 文字颜色 */
--text-primary: #333333;       /* 主要文字 */
--text-secondary: #666666;     /* 次要文字 */
--text-tertiary: #999999;      /* 三级文字 */
--text-white: #FFFFFF;         /* 白色文字 */

/* 背景颜色 */
--bg-primary: #FFFFFF;         /* 主背景 */
--bg-secondary: #F5F5F5;       /* 次背景 */
--bg-card: #FFFFFF;            /* 卡片背景 */

/* 功能色彩 */
--price-color: #FF3333;        /* 价格红色 */
--success-color: #00AA90;      /* 成功绿色 */
--warning-color: #FF9500;      /* 警告橙色 */
```

#### 字体规范
```css
--font-size-h1: 36rpx;         /* 一级标题 */
--font-size-h2: 32rpx;         /* 二级标题 */
--font-size-body: 28rpx;       /* 正文 */
--font-size-caption: 24rpx;    /* 说明文字 */
--font-size-mini: 20rpx;       /* 极小文字 */
```

#### 间距规范
```css
--spacing-xs: 8rpx;            /* 极小间距 */
--spacing-sm: 16rpx;           /* 小间距 */
--spacing-md: 32rpx;           /* 中等间距 */
--spacing-lg: 48rpx;           /* 大间距 */
--spacing-xl: 64rpx;           /* 极大间距 */
```

### 首页界面设计

#### 整体布局结构
```
┌─────────────────────────────────────────┐ 88rpx
│ [广州 ▼]                    [🔔] [⋯]    │
├─────────────────────────────────────────┤ 120rpx
│ [搜主菜站____________] [搜索]            │
├─────────────────────────────────────────┤ 160rpx
│ 新用户专享（下单返现）        [礼品盒]    │
│ 活动时间：7月1日-7月8日                  │
├─────────────────────────────────────────┤ 160rpx
│ [下午茶] [秒杀] [神券] [签到] [免单]     │
├─────────────────────────────────────────┤ 100rpx
│ [推荐] [美食] [外卖] [娱乐] [零售] →     │
├─────────────────────────────────────────┤
│ ┌─────────┐  ┌─────────┐                │ 瀑布流
│ │ 商品图片 │  │ 商品图片 │                │ 动态高度
│ │ 标题     │  │ 标题     │                │
│ │ ¥29.9   │  │ ¥19.9   │                │
│ └─────────┘  └─────────┘                │
└─────────────────────────────────────────┘
│ [首页] [活动] [分享] [我的]              │ 100rpx
└─────────────────────────────────────────┘
```

#### 详细尺寸规范

**顶部导航栏区域 (88rpx)**
- 背景：美团橙色渐变 `linear-gradient(135deg, #FF6600 0%, #FF8C42 100%)`
- 标题：居中显示，白色文字，32rpx，粗体
- 返回按钮：左侧，白色图标，32rpx（子页面显示）

**城市选择区域 (100rpx)**
- 背景：继承顶部渐变
- 城市名称："广州"，白色文字，32rpx，左边距32rpx
- 下拉箭头：白色向下箭头图标，24rpx
- 通知图标：铃铛图标，右侧，40rpx
- 更多图标：三点图标，最右侧，40rpx

**搜索栏区域 (120rpx)**
- 背景：继承顶部渐变
- 搜索框：白色背景，圆角40rpx，高度80rpx，左右边距32rpx
- 占位符："搜主菜站"，灰色#999999，28rpx，左边距32rpx
- 搜索按钮："搜索"，橙色背景#FF6600，白色文字，圆角8rpx，宽度120rpx

**主活动横幅 (160rpx)**
- 背景：橙色渐变 #FF6B35
- 主标题："新用户专享（下单返现）"，白色粗体文字，32rpx
- 副标题："点外卖专用，每天限量发放"，白色文字，24rpx
- 时间标签："活动时间：7月1日-7月8日"，黄色背景#FFD100，黑色文字，24rpx
- 礼品盒图标：右侧，128rpx×128rpx

**品牌推广区域 (160rpx)**
- 背景：白色卡片，圆角16rpx，左右边距32rpx，上下边距16rpx
- 布局：5个功能入口横向排列，每列宽度约120rpx
- 图标：彩色图标，64rpx×64rpx，居中
- 文字：功能名称，24rpx，灰色#666666，居中，图标下方8rpx

**分类标签栏 (100rpx)**
- 背景：白色
- 标签：横向滚动，每个标签内边距24rpx×16rpx
- 选中状态：美团橙色背景#FF6600，白色文字，圆角40rpx
- 未选中：透明背景，灰色文字#666666
- 字体：28rpx，居中对齐

**瀑布流商品区域**
- 背景：浅灰色#F5F5F5
- 列数：2列
- 列间距：32rpx
- 行间距：24rpx
- 左右边距：32rpx
- 商品卡片：白色背景，圆角16rpx，阴影 `0 4rpx 16rpx rgba(0,0,0,0.1)`

**商品卡片详细规范**
- 商品图片：宽度100%，高度自适应，圆角8rpx
- 商品标题：28rpx字体，最多显示2行，超出省略，颜色#333333
- 价格显示：红色现价#FF3333，32rpx粗体 + 灰色原价#999999，24rpx删除线
- 优惠标签：红色背景#FF3333，白色文字，圆角4rpx，内边距8rpx×4rpx
- 商家信息：商家名称 + 距离信息，24rpx，灰色#666666
- 卡片内边距：24rpx

**底部导航栏 (100rpx + 安全区域)**
- 背景：白色，上边框#E5E5E5
- 4个标签：等宽分布
- 图标：48rpx×48rpx，选中橙色#FF6600，未选中灰色#999999
- 文字：24rpx，选中橙色#FF6600，未选中灰色#999999

### 活动页面设计

#### 整体布局结构
```
┌─────────────────────────────────────────┐ 88rpx
│ [广州 ▼]                    [🔔] [⋯]    │
├─────────────────────────────────────────┤ 120rpx
│ [搜主菜站____________] [搜索]            │
├─────────────────────────────────────────┤ 160rpx
│ [KFC] [麦当劳] [星巴克] [必胜客] [茶颜]  │
├─────────────────────────────────────────┤ 100rpx
│ [全部新品] [美食] [外卖] [娱乐] [丽人] → │
├─────────────────────────────────────────┤ 88rpx
│ [全部 ▼] [品类 ▼] [智能排序 ▼]         │
├─────────────────────────────────────────┤
│ ┌───┐ 商品标题                    [抢]  │ 200rpx
│ │图片│ 商品描述                         │
│ │160│ ¥29.9 ¥39.9 [外卖] 1.1km       │
│ └───┘                                  │
├─────────────────────────────────────────┤
│ ┌───┐ 另一个商品标题              [抢]  │ 200rpx
│ │图片│ 商品描述                         │
│ └───┘ ¥19.9 ¥29.9 [外卖] 0.8km       │
└─────────────────────────────────────────┘
│ [首页] [活动] [分享] [我的]              │ 100rpx
└─────────────────────────────────────────┘
```

**品牌推广区域 (160rpx)**
- 背景：白色卡片，圆角16rpx，左右边距32rpx，上下边距16rpx
- 布局：5个品牌图标横向排列，等宽分布
- 品牌顺序：肯德基(KFC)、麦当劳(M)、星巴克、必胜客、茶颜悦色
- 图标样式：圆形品牌Logo，96rpx×96rpx，居中
- 品牌名称：24rpx，黑色#333333，居中，图标下方8rpx

**筛选功能区 (88rpx)**
- 背景：白色，下边框#E5E5E5
- 筛选选项：全部 ▼、品类 ▼、智能排序 ▼
- 布局：三个筛选按钮横向排列，等宽分布
- 按钮样式：白色背景，灰色文字#666666，下拉箭头#999999
- 字体：28rpx，居中对齐

**商品列表区域**
- 背景：浅灰色#F5F5F5
- 布局方式：垂直列表，每行一个商品
- 商品卡片：白色背景，圆角16rpx，左右边距32rpx，上下边距12rpx
- 卡片高度：200rpx，内边距24rpx

**商品卡片内部布局**
- 左侧商品图片：160rpx×160rpx，圆角8rpx，左对齐
- 右侧信息区域：占据剩余宽度，左边距24rpx
- 最右侧抢购按钮：宽度120rpx，高度64rpx，右对齐

**商品信息区域**
- 商品标题：28rpx黑色字体#333333，粗体，最多显示2行
- 商品描述：24rpx灰色字体#666666，最多显示1行
- 价格区域：水平排列
  - 现价：32rpx红色字体#FF3333，粗体
  - 原价：24rpx灰色字体#999999，删除线
- 优惠标签：水平排列
  - 优惠标签：黄色背景#FFD100，黑色文字，圆角4rpx，内边距8rpx×4rpx

**抢购按钮**
- 背景：红色#FF3333
- 文字："抢"，白色，28rpx，粗体
- 尺寸：120rpx×64rpx
- 圆角：8rpx
- 位置：右侧居中对齐

### 分享页面设计

#### 整体布局结构
```
┌─────────────────────────────────────────┐ 88rpx
│ [搜索活动_______________] [🔍]           │
├─────────────────────────────────────────┤ 100rpx
│ [推荐] [美食] [外卖] [娱乐] [零售] →     │
├─────────────────────────────────────────┤ 80rpx
│ 热门活动分享                             │
├─────────────────────────────────────────┤
│ ┌─────────┐  ┌─────────┐                │ 瀑布流
│ │ 活动图片 │  │ 活动图片 │                │ 动态高度
│ │ 活动标题 │  │ 活动标题 │                │
│ │ 优惠信息 │  │ 优惠信息 │                │
│ │ [分享]  │  │ [分享]  │                │
│ └─────────┘  └─────────┘                │
└─────────────────────────────────────────┘
│ [首页] [活动] [分享] [我的]              │ 100rpx
└─────────────────────────────────────────┘
```

**顶部标题区域 (120rpx)**
- 标题文字："分享活动"，黄色艺术字体
- 字体样式：加粗，渐变黄色，带阴影效果，36rpx
- 背景：白色背景
- 位置：居中对齐

**搜索栏区域 (100rpx)**
- 搜索框：灰色背景#F5F5F5，圆角40rpx，高度80rpx
- 占位符："搜索活动"，灰色文字#999999，28rpx
- 搜索图标：右侧放大镜图标，灰色#666666，32rpx
- 样式：全宽度，左右边距32rpx

**分类标签栏 (100rpx)**
- 标签列表：推荐、美食、外卖、娱乐、零售等
- 布局：横向滚动，支持左右滑动
- 选中状态：推荐标签默认选中，红色下划线#FF3333，4rpx高度
- 未选中：灰色文字#666666，无下划线
- 背景：白色背景
- 字体：28rpx

**活动内容区域**
- 标题："热门活动分享"，黑色文字#333333，左对齐，32rpx粗体
- 布局：瀑布流布局，双列不等高
- 活动卡片结构：
  - 活动图片：美团活动宣传图片
  - 活动标题：如"新用户专享立减20元"，28rpx
  - 优惠信息：折扣金额、有效期等，24rpx灰色
  - 分享按钮：橙色背景#FF6600，白色文字"分享"
- 卡片样式：白色背景，圆角16rpx，阴影效果

**分享功能实现**
- 分享卡片生成：包含活动图片、标题、优惠信息
- 微信分享接口：使用wx.shareAppMessage生成小程序分享卡片
- 分享统计：记录分享次数和点击数据
- 推广链接：集成美团联盟推广链接到分享内容

### 个人中心页面设计

#### 整体布局结构
```
┌─────────────────────────────────────────┐ 88rpx
│                                         │
│     👤                    退出登录       │
│  AGx424069683                          │
│  186****7573                           │
│                                         │
├─────────────────────────────────────────┤ 160rpx
│ ┌─────┐  ┌─────┐  ┌─────┐              │
│ │红包/神│  │代金券│  │单笔返│              │
│ │券 28 │  │ 6   │  │ 0.0 │              │
│ │个未使用│  │张未使用│  │元未提现│              │
│ └─────┘  └─────┘  └─────┘              │
├─────────────────────────────────────────┤
│ 📋 订单中心                             │ 120rpx
│    查看我的订单                          │
├─────────────────────────────────────────┤
│ 👤 添加官方福利君                        │ 120rpx
│    默认小助手，有20元                     │
├─────────────────────────────────────────┤
│ 👥 加入省钱福利群                        │ 120rpx
│    天天有20元福利                        │
├─────────────────────────────────────────┤
│ 🎧 客服                                 │ 120rpx
│    欢迎咨询，我们会尽快答复您哦～          │
├─────────────────────────────────────────┤ 80rpx
│ 当前版本v1.0.0正式版                     │
└─────────────────────────────────────────┘
│ [首页] [活动] [分享] [我的]              │ 100rpx
└─────────────────────────────────────────┘
```

**用户信息区域 (240rpx)**
- 背景：黄色渐变背景 `linear-gradient(135deg, #FFD100 0%, #FFF200 100%)`
- 用户头像：圆形头像，128rpx直径，默认头像图标
- 用户ID："AGx424069683"，黑色粗体文字，32rpx
- 手机号码："186****7573"，灰色文字#666666，28rpx，脱敏显示
- 退出登录：右上角"退出登录"按钮，灰色文字#666666，28rpx

**统计信息卡片 (160rpx)**
- 布局：三列横向排列的统计卡片，等宽分布
- 红包/神券：
  - 图标：红色礼品盒图标，64rpx
  - 数量：28，红色数字#FF3333，48rpx粗体
  - 描述：个未使用，灰色文字#666666，24rpx
  - 点击功能：跳转到美团小程序红包页面
- 代金券：
  - 图标：黄色代金券图标，64rpx
  - 数量：6，橙色数字#FF6600，48rpx粗体
  - 描述：张未使用，灰色文字#666666，24rpx
  - 点击功能：跳转到美团小程序代金券页面
- 单笔返：
  - 图标：绿色返现图标，64rpx
  - 金额：0.0，绿色数字#00AA90，48rpx粗体
  - 描述：元未提现，灰色文字#666666，24rpx
- 卡片样式：白色背景，圆角16rpx，内边距32rpx，可点击状态

**功能入口列表**
- 每个功能入口：独立的白色背景卡片，高度120rpx
- 图标：左侧彩色图标，64rpx×64rpx
- 标题：功能名称，黑色文字#333333，32rpx粗体
- 描述：功能说明，灰色文字#666666，24rpx
- 箭头：右侧灰色箭头图标，32rpx
- 卡片间距：上下边距8rpx，左右边距32rpx

**版本信息区域 (80rpx)**
- 版本号："当前版本v1.0.0正式版"，灰色小字#999999，24rpx
- 位置：页面底部中央
- 样式：居中对齐

## Components and Interfaces

### 页面组件结构

#### 1. 首页 (pages/index)
```
index/
├── index.wxml     # 页面结构
├── index.wxss     # 页面样式
├── index.js       # 页面逻辑
└── index.json     # 页面配置
```

#### 2. 活动页 (pages/activity)
```
activity/
├── activity.wxml
├── activity.wxss
├── activity.js
└── activity.json
```

#### 3. 分享页 (pages/share)
```
share/
├── share.wxml
├── share.wxss
├── share.js
└── share.json
```

#### 4. 个人中心 (pages/profile)
```
profile/
├── profile.wxml
├── profile.wxss
├── profile.js
└── profile.json
```

### 公共组件

#### 瀑布流组件 (components/waterfall)
```
waterfall/
├── waterfall.wxml    # 瀑布流布局
├── waterfall.wxss    # 瀑布流样式
├── waterfall.js      # 瀑布流逻辑
└── waterfall.json    # 组件配置
```

#### 商品卡片组件 (components/product-card)
```
product-card/
├── product-card.wxml
├── product-card.wxss
├── product-card.js
└── product-card.json
```

#### 搜索栏组件 (components/search-bar)
```
search-bar/
├── search-bar.wxml
├── search-bar.wxss
├── search-bar.js
└── search-bar.json
```

## API集成设计

### 美团联盟API服务 (utils/meituan-api.js)

```javascript
class MeituanApiService {
  static config = {
    appkey: '9498b0824d214ee4b65bfab1be6dbed0',
    secret: '2a1463cb8f364cafbfd8d2a19c48eb48',
    mediaId: '1000304354',
    allianceId: '1904722314357399633',
    sid: 'yjd2025'
  }

  // 生成签名
  static generateSignature(method, contentMD5, headers, url, secret) {
    const stringToSign = `${method}\n${contentMD5}\n${headers}\n${url}`;
    const crypto = require('crypto-js');
    const hmac = crypto.HmacSHA256(stringToSign, secret);
    return crypto.enc.Base64.stringify(hmac);
  }
  
  // 查询商品
  static async queryCoupon(params) {
    const url = '/cps_open/common/api/v1/query_coupon';
    return await this.request(url, params);
  }
  
  // 获取推广链接
  static async getReferralLink(params) {
    const url = '/cps_open/common/api/v1/get_referral_link';
    return await this.request(url, params);
  }
}
```

### 位置服务 (utils/location.js)

```javascript
class LocationService {
  // 获取用户位置
  static getCurrentLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      });
    });
  }
  
  // 根据坐标获取城市信息
  static getCityByCoordinates(latitude, longitude) {
    // 实现地理编码逻辑
  }
}
```

### 缓存服务 (utils/cache.js)

```javascript
class CacheService {
  // 设置缓存
  static set(key, data, expireTime = 30 * 60 * 1000) {
    const cacheData = {
      data: data,
      timestamp: Date.now(),
      expireTime: expireTime
    };
    wx.setStorageSync(key, cacheData);
  }
  
  // 获取缓存
  static get(key) {
    try {
      const cacheData = wx.getStorageSync(key);
      if (cacheData && Date.now() - cacheData.timestamp < cacheData.expireTime) {
        return cacheData.data;
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
```

## Data Models

### 商品数据模型
```javascript
const ProductModel = {
  skuViewId: String,        // 商品ID
  name: String,             // 商品名称
  headUrl: String,          // 商品图片
  originalPrice: String,    // 原价
  sellPrice: String,        // 售价
  brandName: String,        // 品牌名称
  brandLogoUrl: String,     // 品牌Logo
  saleVolume: String,       // 销量
  commissionPercent: String // 佣金比例
}
```

### 用户数据模型
```javascript
const UserModel = {
  userId: String,           // 用户ID
  nickname: String,         // 昵称
  avatar: String,           // 头像
  phone: String,            // 手机号
  city: String,             // 当前城市
  location: {               // 位置信息
    latitude: Number,
    longitude: Number
  }
}
```

## 性能优化

### 图片懒加载
```javascript
// 图片懒加载组件
Component({
  properties: {
    src: String,
    width: Number,
    height: Number
  },
  
  data: {
    loaded: false,
    inView: false
  },
  
  methods: {
    onIntersect(res) {
      if (res.intersectionRatio > 0 && !this.data.loaded) {
        this.setData({ inView: true });
      }
    },
    
    onLoad() {
      this.setData({ loaded: true });
    }
  }
});
```

### 虚拟滚动
```javascript
// 虚拟滚动实现
class VirtualScroll {
  constructor(options) {
    this.itemHeight = options.itemHeight;
    this.containerHeight = options.containerHeight;
    this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
  }
  
  getVisibleItems(scrollTop, items) {
    const startIndex = Math.floor(scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleCount, items.length);
    
    return items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index,
      top: (startIndex + index) * this.itemHeight
    }));
  }
}
```

## 错误处理

### 全局错误处理
```javascript
// app.js
App({
  onError(error) {
    console.error('小程序错误:', error);
    
    // 上报错误信息
    wx.request({
      url: 'https://analytics.example.com/error',
      method: 'POST',
      data: {
        error: error,
        timestamp: Date.now(),
        userAgent: wx.getSystemInfoSync()
      }
    });
  }
});
```

### API错误处理
```javascript
class ErrorHandler {
  static handle(error, context) {
    console.error(`[${context}] Error:`, error);
    
    switch(error.code) {
      case 400:
        wx.showToast({
          title: '请求参数错误',
          icon: 'none'
        });
        break;
      case 401:
        wx.showToast({
          title: '签名验证失败',
          icon: 'none'
        });
        break;
      default:
        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
    }
  }
}
```

## 部署配置

### 微信开发者工具配置
```json
{
  "description": "美团联盟CPS小程序",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true,
    "newFeature": false
  },
  "compileType": "miniprogram",
  "libVersion": "2.19.4",
  "appid": "wxYourAppId",
  "projectname": "meituan-miniprogram"
}
```

### 小程序配置文件 (app.json)
```json
{
  "pages": [
    "pages/index/index",
    "pages/activity/activity",
    "pages/share/share",
    "pages/profile/profile"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#FF6600",
    "navigationBarTitleText": "美团优惠",
    "navigationBarTextStyle": "white"
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#FF6600",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "images/tab/home.png",
        "selectedIconPath": "images/tab/home-active.png"
      },
      {
        "pagePath": "pages/activity/activity",
        "text": "活动",
        "iconPath": "images/tab/activity.png",
        "selectedIconPath": "images/tab/activity-active.png"
      },
      {
        "pagePath": "pages/share/share",
        "text": "分享",
        "iconPath": "images/tab/share.png",
        "selectedIconPath": "images/tab/share-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "images/tab/profile.png",
        "selectedIconPath": "images/tab/profile-active.png"
      }
    ]
  },
  "permission": {
    "scope.userLocation": {
      "desc": "你的位置信息将用于小程序位置接口的效果展示"
    }
  }
}
```

---

*本设计文档基于微信小程序原生框架，使用美团联盟CPS API，确保界面与UI设计图完全一致。*