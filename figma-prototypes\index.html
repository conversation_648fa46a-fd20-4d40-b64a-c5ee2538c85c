<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美团联盟微信小程序原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #FF6600, #FF8533);
            padding: 24px;
            color: white;
            text-align: center;
        }

        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .prototype-card {
            background: #f9f9f9;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .prototype-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .prototype-image {
            height: 120px;
            background: #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        .prototype-info {
            padding: 12px;
        }

        .prototype-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .prototype-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .prototype-link {
            display: block;
            text-decoration: none;
            color: inherit;
        }

        .footer {
            background: #f9f9f9;
            padding: 16px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">美团联盟微信小程序原型</div>
            <div class="subtitle">Figma 原型设计参考</div>
        </div>

        <div class="content">
            <h2 class="section-title">主要页面</h2>
            <div class="prototype-grid">
                <a href="home.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">首页预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">首页</div>
                            <div class="prototype-desc">展示推荐商品、活动和分类入口</div>
                        </div>
                    </div>
                </a>
                <a href="activity.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">活动页预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">活动页</div>
                            <div class="prototype-desc">展示各类促销活动和优惠商品</div>
                        </div>
                    </div>
                </a>
                <a href="share.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">分享页预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">分享页</div>
                            <div class="prototype-desc">用户可分享活动获得返现</div>
                        </div>
                    </div>
                </a>
                <a href="profile.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">个人中心预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">个人中心</div>
                            <div class="prototype-desc">用户信息和功能入口</div>
                        </div>
                    </div>
                </a>
            </div>

            <h2 class="section-title">功能页面</h2>
            <div class="prototype-grid">
                <a href="search.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">搜索结果预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">搜索结果</div>
                            <div class="prototype-desc">展示搜索结果和筛选功能</div>
                        </div>
                    </div>
                </a>
                <a href="product-detail.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">商品详情预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">商品详情</div>
                            <div class="prototype-desc">展示商品详细信息和购买选项</div>
                        </div>
                    </div>
                </a>
                <a href="city-select.html" class="prototype-link">
                    <div class="prototype-card">
                        <div class="prototype-image">城市选择预览</div>
                        <div class="prototype-info">
                            <div class="prototype-name">城市选择</div>
                            <div class="prototype-desc">选择当前城市位置</div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="footer">
            美团联盟微信小程序原型 © 2023
        </div>
    </div>
</body>
</html>