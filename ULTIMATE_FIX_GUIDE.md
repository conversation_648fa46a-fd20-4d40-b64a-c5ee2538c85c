# 美团API终极修复指南

## 🎯 使用终极修复版本

我已经创建了一个全新的API服务实现，包含详细的调试信息。请按以下步骤操作：

### 第一步：更新API密钥（关键）

**重要提醒**：问题很可能是API密钥不正确！

1. **登录美团联盟后台**
   - 访问：https://union.meituan.com/
   - 使用您的账户登录

2. **获取最新的API密钥**
   - 进入"开发者中心"
   - 点击"应用管理"
   - 找到您的应用，点击查看详情
   - **重新复制AppKey和Secret**

3. **更新代码配置**
   
   打开 `src/utils/api-service-ultimate-fix.js` 文件，找到第11-12行：
   
   ```javascript
   // 请在这里更新您的真实API密钥
   appkey: '您从后台复制的新AppKey',
   secret: '您从后台复制的新Secret'
   ```
   
   **注意**：
   - 确保复制时没有多余的空格、换行符或其他字符
   - AppKey和Secret都应该是32位的十六进制字符串
   - 只包含数字0-9和字母a-f

### 第二步：替换API服务

在您的小程序代码中，将原来的API服务引用替换为终极修复版本：

```javascript
// 原来的引用
// const apiService = require('./src/utils/api-service-fixed.js')

// 替换为终极修复版本
const apiService = require('./src/utils/api-service-ultimate-fix.js')

// 使用方法完全相同
apiService.queryCoupon({
  latitude: 39928000,
  longitude: 116404000,
  pageNo: 1,
  pageSize: 10
}).then(result => {
  console.log('API调用成功:', result)
}).catch(error => {
  console.error('API调用失败:', error.message)
})
```

### 第三步：查看详细调试信息

终极修复版本会输出非常详细的调试信息，包括：

1. **配置验证**
   ```
   🔍 验证API配置格式:
   AppKey格式: ✅ (9498b0824d214ee4b65bfab1be6dbed0)
   Secret格式: ✅ (2a1463cb...)
   ```

2. **时间戳生成**
   ```
   ⏰ 时间戳生成:
   原始系统时间: 2025-07-25T10:39:59.033Z
   修正后时间: 2025-01-25T10:39:59.033Z
   最终时间戳: 1737801594033
   ```

3. **签名计算过程**
   ```
   🔐 签名字符串构建:
   Method: POST
   Content-MD5: wFbbb8avoy1BjChyRTlZKA==
   Headers: S-Ca-App:9498b0824d214ee4b65bfab1be6dbed0\nS-Ca-Timestamp:1737801594033\n
   URL: /cps_open/common/api/v1/query_coupon
   ```

4. **请求头验证**
   ```
   🔍 请求头验证:
   Content-Type: ✅
   S-Ca-App长度: ✅
   S-Ca-Timestamp格式: ✅
   S-Ca-Signature长度: ✅
   ```

### 第四步：分析结果

运行后，请仔细查看控制台输出：

**如果成功**：
- 会显示 `✅ API调用成功!`
- 返回正常的数据

**如果仍然失败**：
- 会显示详细的错误分析
- 包含具体的解决建议

## 🔍 常见问题诊断

### 1. 如果显示"AppKey格式: ❌"

说明您的AppKey格式不正确，应该：
- 重新从美团联盟后台复制AppKey
- 确保是32位十六进制字符串（只包含0-9和a-f）
- 检查是否有多余的空格或字符

### 2. 如果显示"Secret格式: ❌"

说明您的Secret格式不正确，应该：
- 重新从美团联盟后台复制Secret
- 确保是32位十六进制字符串
- 检查是否有多余的空格或字符

### 3. 如果仍然显示"签名验证失败"

可能的原因：
1. **API密钥确实不正确**（最可能）
2. **账户未完成实名认证**
3. **应用审核未通过**
4. **没有相应的API权限**
5. **账户被限制或暂停**

## 📞 获取正确的API密钥

**详细步骤**：

1. 打开浏览器，访问：https://union.meituan.com/
2. 使用您的账户登录
3. 点击页面上方的"开发者中心"
4. 在左侧菜单中点击"应用管理"
5. 找到您的应用，点击"查看"或"详情"
6. 在应用详情页面中找到：
   - **AppKey**：通常显示为"应用Key"或"App Key"
   - **Secret**：通常显示为"应用密钥"或"App Secret"
7. 点击"复制"按钮，或者手动选择并复制
8. 将复制的内容粘贴到代码中

**验证方法**：
```javascript
const appkey = '您复制的AppKey'
const secret = '您复制的Secret'

console.log('AppKey长度:', appkey.length) // 应该是32
console.log('Secret长度:', secret.length) // 应该是32
console.log('AppKey格式:', /^[a-f0-9]{32}$/.test(appkey)) // 应该是true
console.log('Secret格式:', /^[a-f0-9]{32}$/.test(secret)) // 应该是true
```

## 🎉 成功标志

当问题解决后，您应该在控制台看到：

```
✅ API调用成功!
```

并且能够正常获取到商品或优惠券数据。

## 📝 重要提醒

1. **API密钥是最可能的问题**：90%的签名验证失败都是因为API密钥不正确
2. **仔细复制**：复制API密钥时要特别小心，不要包含多余的字符
3. **检查账户状态**：确保美团联盟账户状态正常
4. **查看详细日志**：终极修复版本提供了非常详细的调试信息

相信通过使用终极修复版本和正确的API密钥，您的问题一定能够得到解决！
