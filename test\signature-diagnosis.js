/**
 * 美团API签名诊断工具
 * 详细分析签名验证失败的原因
 */

// 模拟微信小程序环境
global.wx = {
  getSystemInfoSync: () => ({ SDKVersion: '3.8.12' }),
  getAccountInfoSync: () => ({ miniProgram: { version: '1.0.0' } }),
  arrayBufferToBase64: (buffer) => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }
}

// Node.js环境下的btoa/atob实现
if (typeof btoa === 'undefined') {
  global.btoa = (str) => Buffer.from(str, 'binary').toString('base64')
  global.atob = (str) => Buffer.from(str, 'base64').toString('binary')
}

console.log('=== 美团API签名诊断工具 ===\n')

// 1. 环境检查
console.log('1. 环境检查:')
console.log('- Node.js环境:', typeof process !== 'undefined')
console.log('- 微信小程序环境:', typeof wx !== 'undefined')
console.log('- Buffer支持:', typeof Buffer !== 'undefined')
console.log('- btoa支持:', typeof btoa !== 'undefined')

// 2. 模块加载测试
console.log('\n2. 模块加载测试:')
try {
  const crypto = require('../src/utils/crypto-fixed-final.js')
  console.log('✅ crypto模块加载成功')
  
  const timeCorrection = require('../src/utils/time-correction.js')
  console.log('✅ time-correction模块加载成功')
  
  const meituanTimeFix = require('../src/utils/meituan-time-fix-official.js')
  console.log('✅ meituan-time-fix-official模块加载成功')
  
  const apiService = require('../src/utils/api-service-fixed.js')
  console.log('✅ api-service-fixed模块加载成功')
  
  // 3. 时间修正状态检查
  console.log('\n3. 时间修正状态:')
  const timeCorrectionStatus = timeCorrection.getStatus()
  console.log('时间修正状态:', {
    isInitialized: timeCorrectionStatus.isInitialized,
    timeOffset: timeCorrectionStatus.timeOffset,
    offsetDays: timeCorrectionStatus.offsetDays,
    rawTime: timeCorrectionStatus.rawTime,
    correctedTime: timeCorrectionStatus.correctedTime
  })
  
  // 4. 美团时间戳生成测试
  console.log('\n4. 美团时间戳生成:')
  const timestamp = meituanTimeFix.getMeituanTimestamp()
  console.log('生成的时间戳:', timestamp)
  console.log('时间戳类型:', typeof timestamp)
  console.log('是否为整数:', Number.isInteger(timestamp))
  console.log('时间戳长度:', timestamp.toString().length)
  console.log('对应时间:', new Date(timestamp).toISOString())
  
  // 5. Content-MD5测试
  console.log('\n5. Content-MD5测试:')
  const testBody = '{"latitude":39928000,"longitude":116404000,"pageNo":1,"pageSize":10}'
  console.log('测试Body:', testBody)
  
  const contentMD5 = crypto.generateContentMD5(testBody)
  console.log('Content-MD5结果:', contentMD5)
  console.log('Content-MD5长度:', contentMD5.length)
  
  // 6. 签名生成测试
  console.log('\n6. 签名生成测试:')
  const testData = {
    latitude: 39928000,
    longitude: 116404000,
    pageNo: 1,
    pageSize: 10
  }
  
  const signatureResult = apiService.generateSignature('POST', '/query_coupon', testData)
  console.log('签名生成结果:', {
    timestamp: signatureResult.timestamp,
    timestampType: typeof signatureResult.timestamp,
    timestampLength: signatureResult.timestamp.toString().length,
    contentMD5: signatureResult.contentMD5,
    contentMD5Length: signatureResult.contentMD5.length,
    signature: signatureResult.signature,
    signatureLength: signatureResult.signature.length
  })
  
  // 7. 请求头构建测试
  console.log('\n7. 请求头构建测试:')
  const headers = {
    'Content-Type': 'application/json;charset=utf-8',
    'S-Ca-App': '9498b0824d214ee4b65bfab1be6dbed0',
    'S-Ca-Timestamp': signatureResult.timestamp,
    'S-Ca-Signature': signatureResult.signature,
    'S-Ca-Signature-Headers': 'S-Ca-App,S-Ca-Timestamp',
    'Content-MD5': signatureResult.contentMD5
  }
  
  console.log('请求头:')
  Object.entries(headers).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`)
  })
  
  // 8. 格式验证
  console.log('\n8. 格式验证:')
  const validations = {
    'Content-Type格式': headers['Content-Type'] === 'application/json;charset=utf-8',
    'S-Ca-App长度': headers['S-Ca-App'].length === 32,
    'S-Ca-Timestamp格式': /^\d{13}$/.test(headers['S-Ca-Timestamp']),
    'S-Ca-Signature长度': headers['S-Ca-Signature'].length === 44,
    'S-Ca-Signature-Headers格式': headers['S-Ca-Signature-Headers'] === 'S-Ca-App,S-Ca-Timestamp',
    'Content-MD5长度': headers['Content-MD5'].length === 24
  }
  
  Object.entries(validations).forEach(([check, result]) => {
    console.log(`  ${check}: ${result ? '✅' : '❌'}`)
  })
  
  const allValid = Object.values(validations).every(v => v)
  
  // 9. 诊断结果
  console.log('\n=== 诊断结果 ===')
  if (allValid) {
    console.log('✅ 所有格式验证通过')
    console.log('')
    console.log('如果仍然出现签名验证失败，可能的原因:')
    console.log('1. 🔑 API密钥问题')
    console.log('   - AppKey或Secret配置错误')
    console.log('   - 密钥已过期或被重置')
    console.log('   - 账户权限不足')
    console.log('')
    console.log('2. ⏰ 时间同步问题')
    console.log('   - 服务器时间与本地时间差异过大')
    console.log('   - 时间戳超出2分钟有效期')
    console.log('   - 时区设置问题')
    console.log('')
    console.log('3. 🌐 网络环境问题')
    console.log('   - 网络延迟导致时间戳过期')
    console.log('   - 代理或防火墙修改请求')
    console.log('   - IP地址被限制')
    console.log('')
    console.log('4. 📋 账户状态问题')
    console.log('   - 美团联盟账户未实名认证')
    console.log('   - 账户被暂停或限制')
    console.log('   - 应用审核未通过')
    console.log('')
    console.log('建议解决步骤:')
    console.log('1. 登录美团联盟后台确认账户状态')
    console.log('2. 重新获取AppKey和Secret')
    console.log('3. 检查应用权限和审核状态')
    console.log('4. 联系美团技术支持')
  } else {
    console.log('❌ 存在格式问题，需要修复')
    console.log('失败的验证项:')
    Object.entries(validations).forEach(([check, result]) => {
      if (!result) {
        console.log(`  - ${check}`)
      }
    })
  }
  
} catch (error) {
  console.error('❌ 模块加载或执行失败:', error.message)
  console.error('错误详情:', error.stack)
  
  console.log('\n可能的问题:')
  console.log('1. 模块路径错误')
  console.log('2. 依赖模块缺失')
  console.log('3. 语法错误')
  console.log('4. 环境兼容性问题')
}

console.log('\n=== 诊断完成 ===')
