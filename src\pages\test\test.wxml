<!--测试页面-->
<view class="test-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">功能测试</text>
    <text class="page-subtitle">测试各个模块的功能是否正常</text>
  </view>

  <!-- 测试按钮区域 -->
  <view class="test-buttons">
    <custom-button 
      text="测试加密工具" 
      type="primary" 
      size="medium"
      bind:tap="testCrypto" />
    
    <custom-button 
      text="测试缓存管理" 
      type="primary" 
      size="medium"
      bind:tap="testCache" />
    
    <custom-button 
      text="测试API签名" 
      type="primary" 
      size="medium"
      bind:tap="testApiSignature" />
    
    <custom-button 
      text="测试位置服务" 
      type="primary" 
      size="medium"
      bind:tap="testLocation" />
    
    <custom-button 
      text="测试API调用" 
      type="primary" 
      size="medium"
      loading="{{loading}}"
      bind:tap="testApiCall" />
    
    <custom-button 
      text="测试推广链接" 
      type="primary" 
      size="medium"
      bind:tap="testReferralLink" />
    
    <custom-button 
      text="测试工具函数" 
      type="primary" 
      size="medium"
      bind:tap="testUtilFunctions" />
  </view>

  <!-- 批量操作 -->
  <view class="batch-actions">
    <custom-button 
      text="运行所有测试" 
      type="secondary" 
      size="large"
      block
      bind:tap="runAllTests" />
    
    <view class="action-row">
      <custom-button 
        text="清空结果" 
        type="outline" 
        size="medium"
        bind:tap="clearResults" />
      
      <custom-button 
        text="复制结果" 
        type="outline" 
        size="medium"
        bind:tap="copyResults" />
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="test-results">
    <view class="results-header">
      <text class="results-title">测试结果 ({{testResults.length}})</text>
    </view>
    
    <scroll-view class="results-list" scroll-y>
      <view class="result-item {{item.type}}" 
            wx:for="{{testResults}}" 
            wx:key="id">
        <view class="result-header">
          <text class="result-type">{{item.type.toUpperCase()}}</text>
          <text class="result-time">{{item.time}}</text>
        </view>
        <text class="result-message">{{item.message}}</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-results" wx:if="{{testResults.length === 0}}">
        <text class="empty-text">暂无测试结果</text>
        <text class="empty-hint">点击上方按钮开始测试</text>
      </view>
    </scroll-view>
  </view>
</view>
